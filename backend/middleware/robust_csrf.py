"""
Robust CSRF middleware that doesn't rely on the session middleware.

This middleware implements CSRF protection without relying on the session
middleware, making it more robust and less prone to errors.
"""
import logging
import secrets
import time
from typing import List, Optional, Callable, Awaitable
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from starlette.requests import Request
from starlette.responses import Response, JSONResponse
from starlette.types import <PERSON>GIApp
from fastapi import HTT<PERSON><PERSON>x<PERSON>, status
from utils.cors_utils import add_cors_headers

# Setup logging
logger = logging.getLogger(__name__)

class RobustCSRFMiddleware(BaseHTTPMiddleware):
    """
    Robust CSRF middleware that doesn't rely on the session middleware.

    This middleware implements CSRF protection without relying on the session
    middleware, making it more robust and less prone to errors.
    """

    def __init__(
        self,
        app: ASGIApp,
        secret_key: str,
        cookie_name: str = "csrf_token",
        header_name: str = "X-CSRF-Token",
        cookie_max_age: int = 3600,  # 1 hour
        safe_methods: Optional[List[str]] = None,
        exempt_paths: Optional[List[str]] = None,
        same_site: str = "lax",
        https_only: bool = False,
        path: str = "/",
    ) -> None:
        super().__init__(app)
        self.secret_key = secret_key
        self.cookie_name = cookie_name
        self.header_name = header_name
        self.cookie_max_age = cookie_max_age
        self.safe_methods = safe_methods or ["GET", "HEAD", "OPTIONS"]
        self.exempt_paths = exempt_paths or []

        # For double-submit pattern, the cookie must be readable by JavaScript
        # so we don't set httponly flag for this specific cookie
        self.security_flags = f"samesite={same_site}; path={path}"
        if https_only:
            self.security_flags += "; secure"

        # Define alternate header names that we'll also check
        self.alternate_header_names = [
            "x-csrf-token",
            "x-xsrf-token",
            "csrf-token",
            "xsrf-token"
        ]

        # Log initialization
        logger.info(f"RobustCSRFMiddleware initialized with cookie: {cookie_name} (using double-submit pattern)")
        logger.info(f"Will also check these alternate header names: {self.alternate_header_names}")

    async def dispatch(
        self, request: Request, call_next: RequestResponseEndpoint
    ) -> Response:
        """
        Process the request and implement CSRF protection.

        Args:
            request: The incoming request
            call_next: The next middleware or route handler

        Returns:
            The response from the next middleware or route handler
        """
        # Skip CSRF check for safe methods (GET, HEAD, OPTIONS)
        if request.method in self.safe_methods:
            # Special handling for specific GET endpoints that need CSRF tokens in response
            if request.method == "GET" and (
                request.url.path == "/users/subscription" or
                request.url.path == "/users/profile" or
                request.url.path == "/users/template"
            ):
                logger.debug(f"Special handling for {request.url.path}")
                # These endpoints are safe methods but we want to include CSRF token in response
                return await self._handle_special_get_method(request, call_next)
            else:
                # Regular safe method handling
                return await self._handle_safe_method(request, call_next)

        # Skip CSRF check for exempt paths
        if any(request.url.path.startswith(path) for path in self.exempt_paths):
            return await call_next(request)

        # Check CSRF token for state-changing operations
        # First try to get the token from the primary header
        csrf_token = request.headers.get(self.header_name)
        cookie_token = request.cookies.get(self.cookie_name)

        # If not found in primary header, check alternate headers
        if not csrf_token:
            for header_name in self.alternate_header_names:
                if request.headers.get(header_name):
                    csrf_token = request.headers.get(header_name)
                    logger.info(f"Found CSRF token in alternate header: {header_name}")
                    break

        # Debug logging for CSRF token validation (reduced verbosity)
        logger.debug(f"CSRF validation for {request.method} {request.url.path}")
        logger.debug(f"Header token: {csrf_token[:8] + '...' if csrf_token else 'None'}")
        logger.debug(f"Cookie token: {cookie_token[:8] + '...' if cookie_token else 'None'}")

        # Special handling for multipart/form-data requests (file uploads)
        content_type = request.headers.get("content-type", "")
        is_multipart = content_type.startswith("multipart/form-data")

        # For multipart/form-data requests, log additional information
        if is_multipart:
            logger.info(f"Processing multipart/form-data request: {request.url.path}")
            logger.info(f"Content-Type: {content_type}")

            # Log all headers for debugging
            for name, value in request.headers.items():
                if "csrf" in name.lower() or "token" in name.lower() or "content" in name.lower():
                    logger.info(f"Header {name}: {value}")

        # If no token in cookie or headers, or tokens don't match, reject the request
        if not cookie_token or not csrf_token or csrf_token != cookie_token:
            client_ip = request.client.host if request.client else "unknown"
            user_agent = request.headers.get("user-agent", "unknown")

            # Log more details for debugging
            logger.warning(
                f"CSRF token validation failed for {request.method} {request.url.path} "
                f"from {client_ip} ({user_agent[:50]})"
            )

            # Log only relevant headers for debugging (reduced verbosity)
            logger.debug("Relevant request headers:")
            for header_name, header_value in request.headers.items():
                if any(keyword in header_name.lower() for keyword in ['csrf', 'token', 'auth', 'content-type']):
                    logger.debug(f"Header {header_name}: {header_value}")

            # Log only relevant cookies for debugging (reduced verbosity)
            logger.debug("Relevant request cookies:")
            for cookie_name, cookie_value in request.cookies.items():
                if any(keyword in cookie_name.lower() for keyword in ['csrf', 'token', 'session']):
                    logger.debug(f"Cookie {cookie_name}: {cookie_value[:8]}... (length: {len(cookie_value)})")

            if is_multipart:
                logger.warning(f"This is a multipart/form-data request (file upload)")
                logger.warning(f"Content-Type: {content_type}")

            if not cookie_token:
                logger.warning("No CSRF token found in cookie")
            if not csrf_token:
                logger.warning("No CSRF token found in header")
            if cookie_token and csrf_token and csrf_token != cookie_token:
                logger.warning("CSRF tokens in cookie and header do not match")
                logger.warning(f"Cookie token: {cookie_token[:8]}..., Header token: {csrf_token[:8]}...")
                logger.warning(f"Cookie token length: {len(cookie_token)}, Header token length: {len(csrf_token)}")

            # Special handling for multipart/form-data requests (file uploads)
            # This is a more robust approach than a temporary exemption
            if is_multipart and request.url.path == "/users/template" and request.method == "POST":
                logger.info("Enhanced CSRF validation for template upload (multipart/form-data)")

                # For multipart/form-data, check all possible headers for CSRF token
                all_csrf_headers = []
                for header_name in [self.header_name] + self.alternate_header_names:
                    if header_value := request.headers.get(header_name):
                        all_csrf_headers.append((header_name, header_value))

                # Log all found CSRF tokens
                if all_csrf_headers:
                    logger.info(f"Found {len(all_csrf_headers)} CSRF headers in multipart request")
                    for header_name, header_value in all_csrf_headers:
                        logger.info(f"Header {header_name}: {header_value[:8]}...")

                        # If any header matches the cookie token, consider it valid
                        if cookie_token and header_value == cookie_token:
                            logger.info(f"CSRF validation passed with header: {header_name}")
                            return await call_next(request)

                # If we get here, no matching token was found
                logger.warning("No matching CSRF token found in any header for multipart request")

            # Create response with CSRF error
            response = JSONResponse(
                status_code=status.HTTP_403_FORBIDDEN,
                content={"detail": "CSRF token validation failed. Please refresh the page and try again."}
            )

            # Add CORS headers to the error response
            response = add_cors_headers(response, request)

            return response

        # If we get here, CSRF validation passed
        return await call_next(request)

    async def _handle_safe_method(
        self, request: Request, call_next: RequestResponseEndpoint
    ) -> Response:
        """
        Handle safe methods (GET, HEAD, OPTIONS) by setting a CSRF token cookie.

        Args:
            request: The incoming request
            call_next: The next middleware or route handler

        Returns:
            The response from the next middleware or route handler
        """
        # Process the request
        response = await call_next(request)

        # Check if we need to set a CSRF token cookie
        if self.cookie_name not in request.cookies:
            # Generate a new CSRF token
            csrf_token = secrets.token_hex(32)
            logger.info(f"Generated new CSRF token for safe method: {request.method} {request.url.path}")

            # Set the cookie in the response using set_cookie for better compatibility
            # For double-submit pattern, the cookie must be readable by JavaScript
            # so we don't set httponly flag for this specific cookie
            response.set_cookie(
                key=self.cookie_name,
                value=csrf_token,
                max_age=self.cookie_max_age,
                httponly=False,  # Must be readable by JavaScript for double-submit pattern
                samesite="lax",
                path="/"
            )

            # Also set the token in the response header for clients that prefer that method
            response.headers["X-CSRF-Token"] = csrf_token

            # Also set in alternate headers for better compatibility
            for alt_header in self.alternate_header_names:
                response.headers[alt_header] = csrf_token

            logger.info(f"CSRF token cookie and headers set for: {request.url.path}")
        else:
            # Even if we already have a cookie, set the token in the headers
            # This ensures the frontend always has access to the token
            csrf_token = request.cookies.get(self.cookie_name)
            if csrf_token:
                response.headers["X-CSRF-Token"] = csrf_token

                # Also set in alternate headers for better compatibility
                for alt_header in self.alternate_header_names:
                    response.headers[alt_header] = csrf_token

            logger.debug(f"Added existing CSRF token to headers for: {request.url.path}")

        return response

    async def _handle_special_get_method(
        self, request: Request, call_next: RequestResponseEndpoint
    ) -> Response:
        """
        Handle special GET endpoints that need CSRF tokens in the response.
        This is for endpoints like /users/profile and /users/subscription.

        Args:
            request: The incoming request
            call_next: The next middleware or route handler

        Returns:
            The response from the next middleware or route handler with CSRF token
        """
        # Get or generate a CSRF token
        csrf_token = request.cookies.get(self.cookie_name)
        if not csrf_token:
            # Generate a new CSRF token
            csrf_token = secrets.token_hex(32)
            logger.info(f"Generated new CSRF token for special GET endpoint: {request.url.path}")
        else:
            logger.info(f"Using existing CSRF token from cookie for: {request.url.path}")

        # Create a custom response handler to modify the response
        async def custom_endpoint_handler():
            # Call the original endpoint
            response = await call_next(request)

            # Set the CSRF token cookie if needed
            if not request.cookies.get(self.cookie_name):
                # For double-submit pattern, the cookie must be readable by JavaScript
                # so we don't set httponly flag for this specific cookie
                response.set_cookie(
                    key=self.cookie_name,
                    value=csrf_token,
                    max_age=self.cookie_max_age,
                    httponly=False,  # Must be readable by JavaScript for double-submit pattern
                    samesite="lax",
                    path="/"
                )
                logger.info(f"CSRF token cookie set for special GET endpoint: {request.url.path}")

            # Return the response with the CSRF token in the header
            # The frontend will extract it from the header
            response.headers["X-CSRF-Token"] = csrf_token

            # Also set in alternate headers for better compatibility
            for alt_header in self.alternate_header_names:
                response.headers[alt_header] = csrf_token

            logger.info(f"Added CSRF token to headers for {request.url.path}")
            logger.info(f"Response headers: {dict(response.headers)}")
            return response

        # Execute the custom handler
        return await custom_endpoint_handler()
