import authService from './authService';
import { apiCache } from './api';
import logger from '../utils/logger';

/**
 * Service for handling reference data API calls (Nodal Officers only)
 */
const referenceDataService = {
  // ATM search functionality has been removed

  /**
   * Search for Nodal Officers with optional filters
   * @param searchQuery General search query
   * @param organization Filter by organization name
   * @param organizationType Filter by organization type
   * @param state Filter by state
   * @param page Page number (1-based)
   * @param limit Number of items per page
   * @returns Promise with Nodal Officer search results
   */
  async searchNodalOfficers(
    searchQuery?: string,
    organization?: string,
    organizationType?: string,
    state?: string,
    page: number = 1,
    limit: number = 10,
    forceRefresh: boolean = false
  ) {
    try {
      // Create a cache key that includes all search parameters
      const cacheKey = `nodal_search_${searchQuery || ''}_${organization || ''}_${organizationType || ''}_${state || ''}_page${page}_limit${limit}`;

      // Check cache first unless forceRefresh is true
      if (!forceRefresh) {
        const cachedData = apiCache.get(cacheKey);
        if (cachedData) {
          logger.debug('Using cached Nodal Officer search results');
          return {
            success: true,
            data: cachedData
          };
        }
      }

      logger.debug('Fetching Nodal Officer search results from API');

      // Build query parameters
      const params: Record<string, any> = {
        page,
        limit
      };

      if (searchQuery) params.search = searchQuery;
      if (organization) params.organization = organization;
      if (organizationType) params.organization_type = organizationType;
      if (state) params.state = state;

      // Fetch from API
      const response = await authService.api.get('/reference/nodal-officers', { params });

      // Store in cache for 5 minutes
      apiCache.set(cacheKey, response.data, 5 * 60 * 1000);

      return {
        success: true,
        data: response.data
      };
    } catch (error: any) {
      logger.error('Error searching Nodal Officers:', error);
      return {
        success: false,
        error: error.response?.data?.detail || error.message || 'Failed to search Nodal Officers'
      };
    }
  }
};

export default referenceDataService;
