import React, { useEffect, useState } from 'react';
import { useAuth } from '../context/AuthContext';
import { useAlert } from '../context/TailwindAlertContext';
import logger from '../utils/logger';

interface SessionConflictDialogProps {
  isOpen: boolean;
  onClose: () => void;
  activeSessionInfo?: {
    id: string;
    lastActive?: string;
  };
}

/**
 * Dialog component that appears when a session conflict is detected
 * Gives the user options to take over the session or logout
 */
const SessionConflictDialog: React.FC<SessionConflictDialogProps> = ({
  isOpen,
  onClose,
  activeSessionInfo
}) => {
  const { resolveSessionConflict } = useAuth();
  const { showSuccess, showError } = useAlert();
  const [countdown, setCountdown] = useState<number>(30); // 30 second countdown
  const [isProcessing, setIsProcessing] = useState<boolean>(false);

  // Start countdown when dialog opens
  useEffect(() => {
    if (isOpen && countdown > 0) {
      const timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);

      return () => clearTimeout(timer);
    } else if (isOpen && countdown === 0 && !isProcessing) {
      // Auto-logout when countdown reaches zero
      handleLogout();
    }
  }, [isOpen, countdown, isProcessing]);

  if (!isOpen) return null;

  const handleTakeOver = async () => {
    try {
      setIsProcessing(true);
      await resolveSessionConflict('TAKE_OVER');
      showSuccess('Successfully took over the session');
      onClose();
    } catch (error) {
      logger.error('Error taking over session:', error);
      showError('Failed to take over session. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleLogout = async () => {
    try {
      setIsProcessing(true);
      await resolveSessionConflict('LOGOUT');
      showSuccess('Logged out successfully');
      onClose();
      // Redirect to login page
      window.location.href = '/login';
    } catch (error) {
      logger.error('Error logging out:', error);
      showError('Failed to logout. Please try again.');
      setIsProcessing(false);
    }
  };

  // Format the last active time if available
  const formatLastActive = () => {
    if (!activeSessionInfo?.lastActive) return 'Unknown time';

    try {
      const date = new Date(activeSessionInfo.lastActive);
      return date.toLocaleString();
    } catch (e) {
      return 'Unknown time';
    }
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div className="fixed inset-0 transition-opacity" aria-hidden="true">
          <div className="absolute inset-0 bg-gray-500 dark:bg-gray-900 opacity-75"></div>
        </div>

        {/* Dialog */}
        <div
          className="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"
          role="dialog"
          aria-modal="true"
          aria-labelledby="modal-headline"
        >
          <div className="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="sm:flex sm:items-start">
              <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900 sm:mx-0 sm:h-10 sm:w-10">
                {/* Warning icon */}
                <svg className="h-6 w-6 text-red-600 dark:text-red-300" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </div>
              <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-headline">
                  Session Conflict Detected
                </h3>
                <div className="mt-2">
                  <p className="text-sm text-gray-500 dark:text-gray-300">
                    Your account is already active in another browser or device.
                    {activeSessionInfo?.lastActive && (
                      <span> Last activity was at {formatLastActive()}.</span>
                    )}
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-300 mt-2">
                    You can either take over this session (which will log out the other session) or log out from this device.
                  </p>
                  <div className="mt-3 text-center">
                    <p className="text-sm font-medium text-red-600 dark:text-red-400">
                      Auto-logout in {countdown} seconds
                    </p>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 mt-1">
                      <div
                        className="bg-red-600 dark:bg-red-500 h-2.5 rounded-full"
                        style={{ width: `${(countdown / 30) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="button"
              className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={handleTakeOver}
              disabled={isProcessing}
            >
              {isProcessing ? 'Processing...' : 'Take Over Session'}
            </button>
            <button
              type="button"
              className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={handleLogout}
              disabled={isProcessing}
            >
              {isProcessing ? 'Processing...' : 'Logout'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SessionConflictDialog;
