"""
Security-related routes for Cyber Sakha.

This module provides routes for security-related functionality such as
CSP violation reporting.
"""
from fastapi import APIRouter, Request, Depends
from fastapi.responses import JSONResponse
import logging
from typing import Dict, Any
from routes.auth import get_current_user

# Setup logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter()

@router.post("/csp-report")
async def csp_report(request: Request):
    """
    Endpoint for receiving Content Security Policy violation reports.
    
    This endpoint collects CSP violation reports sent by browsers when
    a CSP violation occurs. These reports can be used to identify and
    fix potential security issues.
    
    Args:
        request: The request object containing the CSP violation report
        
    Returns:
        JSONResponse acknowledging receipt of the report
    """
    try:
        # Get the report data
        report_data = await request.json()
        
        # Log the CSP violation
        logger.warning(f"CSP Violation: {report_data}")
        
        # Extract useful information from the report
        csp_report = report_data.get("csp-report", {})
        blocked_uri = csp_report.get("blocked-uri", "unknown")
        violated_directive = csp_report.get("violated-directive", "unknown")
        document_uri = csp_report.get("document-uri", "unknown")
        
        # Log a more structured message
        logger.warning(
            f"CSP Violation: {violated_directive} directive violated by {blocked_uri} on {document_uri}"
        )
        
        # Return a simple acknowledgement
        return JSONResponse(
            status_code=204,
            content=None
        )
    except Exception as e:
        logger.error(f"Error processing CSP report: {str(e)}")
        return JSONResponse(
            status_code=400,
            content={"message": "Invalid CSP report format"}
        )

@router.get("/security-headers")
async def security_headers_test(user: Dict[str, Any] = Depends(get_current_user)):
    """
    Test endpoint for security headers.
    
    This endpoint returns a simple response that can be used to test
    security headers. It requires authentication to prevent abuse.
    
    Args:
        user: The authenticated user
        
    Returns:
        JSONResponse with security header test results
    """
    return JSONResponse(
        status_code=200,
        content={
            "message": "Security headers test",
            "user": user.get("email", "unknown")
        }
    )
