#!/usr/bin/env python3
"""
Security Check Script for Cyber Sakha

This script performs various security checks on the codebase to identify potential issues:
1. Checks for hardcoded secrets
2. Verifies environment files are not committed
3. Checks for proper CORS configuration
4. Verifies rate limiting is enabled
5. Checks for secure headers
6. Verifies authentication is required for sensitive endpoints

Usage:
    python security_check.py
"""

import os
import re
import sys
import json
from pathlib import Path
from typing import List, Dict, Any, Tuple

# ANSI color codes for output formatting
GREEN = "\033[92m"
YELLOW = "\033[93m"
RED = "\033[91m"
RESET = "\033[0m"
BOLD = "\033[1m"

# Patterns to search for potential security issues
PATTERNS = {
    "hardcoded_secret": [
        r"password\s*=\s*['\"][^'\"]+['\"]",
        r"secret\s*=\s*['\"][^'\"]+['\"]",
        r"key\s*=\s*['\"][^'\"]+['\"]",
        r"token\s*=\s*['\"][^'\"]+['\"]",
        r"api_key\s*=\s*['\"][^'\"]+['\"]",
    ],
    "insecure_config": [
        r"debug\s*=\s*True",
        r"DEBUG\s*=\s*True",
        r"ALLOWED_HOSTS\s*=\s*\[\s*['\"][*]['\"]",
    ],
    "insecure_import": [
        r"import\s+pickle",
        r"import\s+marshal",
        r"import\s+shelve",
        r"from\s+pickle\s+import",
        r"from\s+marshal\s+import",
        r"from\s+shelve\s+import",
    ],
    "insecure_function": [
        r"eval\s*\(",
        r"exec\s*\(",
        r"os\.system\s*\(",
        r"subprocess\.call\s*\(",
        r"subprocess\.Popen\s*\(",
    ],
    "sql_injection": [
        r"execute\s*\(\s*['\"][^'\"]*\%s[^'\"]*['\"]",
        r"execute\s*\(\s*f['\"]",
        r"executemany\s*\(\s*['\"][^'\"]*\%s[^'\"]*['\"]",
        r"executemany\s*\(\s*f['\"]",
    ],
}

# Files to exclude from checks
EXCLUDE_DIRS = [
    ".git",
    "node_modules",
    "venv",
    ".venv",
    "__pycache__",
    "dist",
    "build",
]

# File extensions to check
INCLUDE_EXTENSIONS = [
    ".py",
    ".js",
    ".ts",
    ".tsx",
    ".jsx",
    ".html",
    ".css",
    ".json",
    ".yml",
    ".yaml",
    ".md",
    ".txt",
]

def print_header(text: str) -> None:
    """Print a formatted header."""
    print(f"\n{BOLD}{text}{RESET}")
    print("=" * len(text))

def print_result(message: str, status: str, details: str = None) -> None:
    """Print a formatted result line."""
    if status == "PASS":
        status_str = f"{GREEN}[PASS]{RESET}"
    elif status == "WARN":
        status_str = f"{YELLOW}[WARN]{RESET}"
    else:  # FAIL
        status_str = f"{RED}[FAIL]{RESET}"
    
    print(f"{status_str} {message}")
    if details:
        print(f"      {details}")

def find_files(base_dir: str) -> List[str]:
    """Find all relevant files to check."""
    files = []
    for root, dirs, filenames in os.walk(base_dir):
        # Skip excluded directories
        dirs[:] = [d for d in dirs if d not in EXCLUDE_DIRS]
        
        for filename in filenames:
            file_path = os.path.join(root, filename)
            if any(file_path.endswith(ext) for ext in INCLUDE_EXTENSIONS):
                files.append(file_path)
    return files

def check_file_for_patterns(file_path: str) -> Dict[str, List[Tuple[int, str]]]:
    """Check a file for security patterns."""
    results = {pattern_type: [] for pattern_type in PATTERNS}
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        for i, line in enumerate(lines, 1):
            for pattern_type, patterns in PATTERNS.items():
                for pattern in patterns:
                    if re.search(pattern, line, re.IGNORECASE):
                        # Skip if line is a comment
                        if line.strip().startswith(('#', '//', '/*', '*', '*/')):
                            continue
                        results[pattern_type].append((i, line.strip()))
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
    
    return results

def check_env_files() -> List[str]:
    """Check for committed environment files."""
    env_files = []
    for root, _, filenames in os.walk('.'):
        for filename in filenames:
            if filename.startswith('.env') and not filename.endswith('.example'):
                env_files.append(os.path.join(root, filename))
    return env_files

def check_cors_config() -> bool:
    """Check for proper CORS configuration."""
    backend_main = Path('backend/main.py')
    if not backend_main.exists():
        return False
    
    with open(backend_main, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for CORS middleware with proper configuration
    has_cors = 'CORSMiddleware' in content
    has_allowed_origins = 'allow_origins' in content
    has_allow_credentials = 'allow_credentials' in content
    
    return has_cors and has_allowed_origins and has_allow_credentials

def check_rate_limiting() -> bool:
    """Check for rate limiting configuration."""
    backend_main = Path('backend/main.py')
    if not backend_main.exists():
        return False
    
    with open(backend_main, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for rate limiting middleware
    return 'RateLimitMiddleware' in content

def check_secure_headers() -> bool:
    """Check for secure headers configuration."""
    backend_main = Path('backend/main.py')
    if not backend_main.exists():
        return False
    
    with open(backend_main, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for security headers middleware
    return 'SecurityHeadersMiddleware' in content

def check_authentication() -> bool:
    """Check for authentication on sensitive endpoints."""
    auth_middleware = Path('backend/middleware/auth_middleware.py')
    if not auth_middleware.exists():
        return False
    
    with open(auth_middleware, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for JWT verification
    return 'verify_jwt' in content

def main() -> None:
    """Main function to run security checks."""
    print(f"{BOLD}Cyber Sakha Security Check{RESET}")
    print("=" * 30)
    print("Running security checks on the codebase...")
    
    # 1. Check for hardcoded secrets
    print_header("Checking for hardcoded secrets")
    files = find_files('.')
    issues_found = False
    
    for file_path in files:
        results = check_file_for_patterns(file_path)
        for pattern_type, matches in results.items():
            if matches:
                issues_found = True
                for line_num, line in matches:
                    print_result(
                        f"{file_path}:{line_num}",
                        "WARN",
                        f"Potential {pattern_type.replace('_', ' ')}: {line}"
                    )
    
    if not issues_found:
        print_result("No hardcoded secrets found", "PASS")
    
    # 2. Check for committed environment files
    print_header("Checking for committed environment files")
    env_files = check_env_files()
    if env_files:
        for env_file in env_files:
            print_result(
                f"Environment file found: {env_file}",
                "WARN",
                "Environment files should not be committed to version control"
            )
    else:
        print_result("No environment files found in version control", "PASS")
    
    # 3. Check CORS configuration
    print_header("Checking CORS configuration")
    if check_cors_config():
        print_result("CORS is properly configured", "PASS")
    else:
        print_result(
            "CORS configuration issue detected",
            "WARN",
            "Ensure CORSMiddleware is configured with proper allowed origins"
        )
    
    # 4. Check rate limiting
    print_header("Checking rate limiting")
    if check_rate_limiting():
        print_result("Rate limiting is enabled", "PASS")
    else:
        print_result(
            "Rate limiting not detected",
            "WARN",
            "Implement rate limiting to prevent abuse"
        )
    
    # 5. Check secure headers
    print_header("Checking secure headers")
    if check_secure_headers():
        print_result("Security headers are configured", "PASS")
    else:
        print_result(
            "Security headers not detected",
            "WARN",
            "Implement security headers middleware"
        )
    
    # 6. Check authentication
    print_header("Checking authentication")
    if check_authentication():
        print_result("Authentication middleware is implemented", "PASS")
    else:
        print_result(
            "Authentication middleware not detected",
            "WARN",
            "Ensure proper authentication for sensitive endpoints"
        )
    
    print(f"\n{BOLD}Security check completed{RESET}")

if __name__ == "__main__":
    main()
