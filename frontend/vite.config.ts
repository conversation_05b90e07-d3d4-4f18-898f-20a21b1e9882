import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  // Load env file based on `mode` in the current directory
  const env = loadEnv(mode, process.cwd())

  return {
    plugins: [react()],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
      },
    },
    server: {
      port: 3001,
      host: mode === 'production' ? '0.0.0.0' : 'localhost',
      cors: true,
      hmr: {
        overlay: false, // Disable the HMR error overlay
      },
      proxy: {
        '/api': {
          target: mode === 'production'
            ? env.VITE_API_BASE_URL || 'https://api.cybersakha.in'
            : 'http://localhost:8000',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ''),
          secure: mode === 'production',
          ws: true
        }
      }
    },
    build: {
      outDir: 'dist',
      // Disable sourcemaps completely to avoid browser warnings
      sourcemap: false,
      // Production optimizations
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: mode === 'production',
          drop_debugger: mode === 'production'
        }
      },
      // Split chunks for better caching
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ['react', 'react-dom', 'react-router-dom'],
            ui: ['@heroicons/react', 'react-icons', 'framer-motion'],
            utils: ['axios', 'jwt-decode'],
            table: ['@tanstack/react-table'],
            graph: ['reactflow', 'dagre']
          }
        }
      },
      // Reduce chunk size warnings threshold
      chunkSizeWarningLimit: 1500,
      // Add cache busting for assets
      assetsDir: 'assets',
      assetsInlineLimit: 4096, // 4kb
    },
    // Add cache busting for production
    experimental: {
      renderBuiltUrl(filename) {
        if (mode === 'production') {
          return `${env.VITE_ASSET_URL || ''}/${filename}`
        }
        return filename
      }
    }
  }
})
