import os
import jwt
import uuid
import logging
from typing import Optional, Tu<PERSON>, Dict, Any
from datetime import datetime, timedelta, timezone
from fastapi import HTT<PERSON><PERSON>x<PERSON>, Request
from passlib.context import CryptContext
from dotenv import load_dotenv
from database import get_db

# Load environment variables
load_dotenv()

# Setup logging
logger = logging.getLogger(__name__)

# Get secret key from environment variables
SECRET_KEY = os.getenv("SECRET_KEY", "")
if not SECRET_KEY:
    raise ValueError("SECRET_KEY must be set in environment variables")

# JWT settings
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 24 * 30  # 30 days for access token (persist until explicit logout)
REFRESH_TOKEN_EXPIRE_DAYS = 60    # 60 days for refresh token

# Security settings
MAX_FAILED_LOGIN_ATTEMPTS = 5
ACCOUNT_LOCKOUT_MINUTES = 15
PROGRESSIVE_LOCKOUT = True

# Session settings
ENFORCE_SINGLE_SESSION = True     # Only allow one active session per user
SESSION_CONFLICT_RESOLUTION = "NOTIFY"  # Options: "TERMINATE_OLD", "NOTIFY"
SESSION_TIMEOUT_MINUTES = 30      # Inactive session timeout in minutes

# Password hashing
pwd_context = CryptContext(
    schemes=["argon2", "bcrypt"],
    deprecated="auto",
    argon2__time_cost=3,  # Number of iterations
    argon2__memory_cost=65536,  # Memory usage in KiB
    argon2__parallelism=4,  # Number of parallel threads
    argon2__hash_len=32  # Length of the hash in bytes
)

def get_password_hash(password: str) -> str:
    """Hash a password using Argon2"""
    return pwd_context.hash(password)

async def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against a hash and migrate if needed"""
    is_valid = pwd_context.verify(plain_password, hashed_password)

    # If password is valid and using old hash scheme, rehash it
    if is_valid and pwd_context.needs_update(hashed_password):
        try:
            # Get database connection
            db = get_db()
            if db is None:
                logger.error("Database connection error during password rehash")
                return is_valid

            # Find user by hashed password
            user = await db.users.find_one({"hashed_password": hashed_password})
            if user:
                # Generate new hash
                new_hash = get_password_hash(plain_password)
                # Update user's password hash
                await db.users.update_one(
                    {"_id": user["_id"]},
                    {"$set": {"hashed_password": new_hash}}
                )
                logger.info(f"Successfully migrated password hash for user: {user['email']}")
        except Exception as e:
            logger.error(f"Error migrating password hash: {str(e)}")

    return is_valid

def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """Create an access token"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire, "type": "access", "jti": str(uuid.uuid4())})
    return jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)

def create_refresh_token(data: Dict[str, Any]) -> str:
    """Create a refresh token"""
    to_encode = data.copy()
    expire = datetime.now(timezone.utc) + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
    to_encode.update({"exp": expire, "type": "refresh", "jti": str(uuid.uuid4())})
    return jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)

def create_tokens(user_email: str) -> Tuple[str, str]:
    """Create both access and refresh tokens"""
    access_token = create_access_token({"sub": user_email})
    refresh_token = create_refresh_token({"sub": user_email})
    return access_token, refresh_token

async def blacklist_token(token: str, token_type: str) -> bool:
    """
    Token blacklisting functionality removed for simplicity
    This function now just returns True without doing anything
    """
    # Just log the attempt but don't actually blacklist
    logger.info(f"Token blacklisting disabled - would have blacklisted a {token_type} token")
    return True

async def verify_token(token: str, token_type: str = "access") -> dict:
    """Verify a token"""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])

        # Verify token type
        if payload.get("type") != token_type:
            raise HTTPException(status_code=401, detail=f"Invalid token type: expected {token_type}")

        # Token blacklisting check removed for simplicity

        return payload
    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=401, detail="Token has expired")
    except jwt.InvalidTokenError:
        raise HTTPException(status_code=401, detail="Invalid token")

async def get_current_user(request: Request, check_session_conflict=True) -> dict:
    """
    Get the current user from the request

    Args:
        request: The request object
        check_session_conflict: Whether to check for session conflicts

    Returns:
        dict: User object

    Raises:
        HTTPException: If authentication fails
    """
    # Try to get token from cookies first (preferred method)
    token = request.cookies.get("access_token")

    # If not in cookies, try Authorization header (for backward compatibility)
    if not token:
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            token = auth_header.replace("Bearer ", "")

    if not token:
        logger.debug("No token found in cookies or Authorization header")
        raise HTTPException(
            status_code=401,
            detail="Not authenticated"
        )

    try:
        payload = await verify_token(token, "access")
        user_email = payload.get("sub")

        if not user_email:
            logger.warning("Token payload missing 'sub' field")
            raise HTTPException(status_code=401, detail="Invalid token")

        db = get_db()
        if db is None:
            logger.error("Database connection error when getting current user")
            raise HTTPException(status_code=500, detail="Database error")

        user = await db.users.find_one({"email": user_email})
        if not user:
            logger.warning(f"User not found for email: {user_email}")
            raise HTTPException(status_code=401, detail="User not found")

        # Get current session ID from request
        current_session_id = request.cookies.get("session_id") or request.headers.get("X-Session-ID")

        # Check for session conflicts if enabled
        if ENFORCE_SINGLE_SESSION and check_session_conflict and current_session_id:
            active_session = await get_user_active_session(db, user["_id"])

            # Check if there's an active session
            if active_session:
                # Check if the session has timed out
                now = datetime.now(timezone.utc)
                last_active = active_session.get("last_updated")

                if last_active:
                    # Ensure both datetimes are timezone-aware
                    if last_active.tzinfo is None:
                        last_active = last_active.replace(tzinfo=timezone.utc)

                    # Calculate time difference
                    time_diff = now - last_active
                    minutes_diff = time_diff.total_seconds() / 60

                    # If session has timed out, clear it and set the current session as active
                    if minutes_diff > SESSION_TIMEOUT_MINUTES:
                        logger.info(f"Session {active_session['session_id']} timed out after {minutes_diff:.2f} minutes of inactivity")
                        await clear_user_session(db, user["_id"])
                        await update_user_last_access(
                            db, user["_id"], user_email,
                            now, current_session_id
                        )
                    # If session is still active but different from current session
                    elif active_session["session_id"] != current_session_id:
                        # There's a session conflict with an active session
                        if SESSION_CONFLICT_RESOLUTION == "TERMINATE_OLD":
                            # Automatically terminate old session and continue with new one
                            logger.warning(f"Terminating old session for user {user_email}")
                            # Update session to current one
                            await update_user_last_access(
                                db, user["_id"], user_email,
                                now, current_session_id
                            )
                        else:
                            # Notify about session conflict
                            raise HTTPException(
                                status_code=409,  # Conflict
                                detail={
                                    "message": "Another session is active",
                                    "code": "SESSION_CONFLICT",
                                    "active_session": {
                                        "id": active_session["session_id"],
                                        "last_active": active_session["last_updated"].isoformat()
                                            if active_session.get("last_updated") else None
                                    }
                                }
                            )
                    else:
                        # Same session, just update the last access time
                        await update_user_last_access(
                            db, user["_id"], user_email,
                            now, current_session_id
                        )
                else:
                    # No last_updated timestamp, treat as a new session
                    await update_user_last_access(
                        db, user["_id"], user_email,
                        now, current_session_id
                    )
            else:
                # No active session but we have a session ID, so set this as the active session
                await update_user_last_access(
                    db, user["_id"], user_email,
                    datetime.now(timezone.utc), current_session_id
                )
        else:
            # Just update the last access time without session management
            await update_user_last_access(db, user["_id"], user_email, datetime.now(timezone.utc))

        return user
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting current user: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

async def update_user_last_access(db, user_id, user_email, timestamp, session_id=None):
    """
    Update user's last access time and session information

    Args:
        db: Database connection
        user_id: User ID
        user_email: User email
        timestamp: Current timestamp
        session_id: Current session ID (optional)
    """
    try:
        update_data = {"last_access": timestamp}

        # If session_id is provided, update active session
        if session_id:
            update_data["active_session_id"] = session_id
            update_data["active_session_updated"] = timestamp

        await db.users.update_one(
            {"_id": user_id},
            {"$set": update_data}
        )
        logger.debug(f"Updated last access time for user {user_email}")
    except Exception as e:
        logger.error(f"Failed to update last access time for user {user_email}: {str(e)}")

async def get_user_active_session(db, user_id):
    """
    Get user's active session information

    Args:
        db: Database connection
        user_id: User ID

    Returns:
        dict: Session information or None if no active session
    """
    try:
        user = await db.users.find_one(
            {"_id": user_id},
            {"active_session_id": 1, "active_session_updated": 1}
        )

        if user and "active_session_id" in user:
            return {
                "session_id": user["active_session_id"],
                "last_updated": user.get("active_session_updated")
            }
        return None
    except Exception as e:
        logger.error(f"Error getting user active session: {str(e)}")
        return None

async def clear_user_session(db, user_id):
    """
    Clear user's active session

    Args:
        db: Database connection
        user_id: User ID
    """
    try:
        await db.users.update_one(
            {"_id": user_id},
            {"$unset": {"active_session_id": "", "active_session_updated": ""}}
        )
        logger.debug(f"Cleared active session for user {user_id}")
        return True
    except Exception as e:
        logger.error(f"Error clearing user session: {str(e)}")
        return False

async def check_account_lockout(db, user_email: str) -> Tuple[bool, Optional[datetime]]:
    """Check if account is locked out"""
    user = await db.users.find_one({"email": user_email})
    if not user:
        return False, None

    failed_attempts = user.get("failed_login_attempts", 0)
    lockout_until = user.get("account_locked_until")

    if lockout_until and datetime.now(timezone.utc) < lockout_until:
        return True, lockout_until

    if failed_attempts >= MAX_FAILED_LOGIN_ATTEMPTS:
        lockout_minutes = ACCOUNT_LOCKOUT_MINUTES
        if PROGRESSIVE_LOCKOUT:
            lockout_minutes *= (2 ** (failed_attempts - MAX_FAILED_LOGIN_ATTEMPTS))

        lockout_until = datetime.now(timezone.utc) + timedelta(minutes=lockout_minutes)
        await db.users.update_one(
            {"email": user_email},
            {"$set": {"account_locked_until": lockout_until}}
        )
        return True, lockout_until

    return False, None
