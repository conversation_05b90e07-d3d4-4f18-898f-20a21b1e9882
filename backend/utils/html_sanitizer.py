"""
HTML sanitization utilities to prevent XSS attacks.

This module provides functions to sanitize HTML content and prevent
Cross-Site Scripting (XSS) attacks.
"""
import logging
import re
import bleach
from typing import List, Dict, Any, Optional, Union
from html_sanitizer import Sanitizer
from html_sanitizer.sanitizer import DEFAULT_SETTINGS

# Setup logging
logger = logging.getLogger(__name__)

# Configure HTML sanitizer with safe settings
SANITIZER_SETTINGS = DEFAULT_SETTINGS.copy()
SANITIZER_SETTINGS.update({
    # Allowed tags (expanded to include all needed for extraction)
    'tags': {
        'a', 'abbr', 'acronym', 'b', 'br', 'blockquote', 'code', 'div', 'em',
        'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'hr', 'i', 'img', 'li', 'ol',
        'p', 'pre', 'span', 'strong', 'table', 'tbody', 'td', 'th', 'thead',
        'tr', 'ul', 'form', 'input', 'select', 'option', 'label', 'button',
        'iframe', 'script', 'style', 'link', 'meta', 'head', 'body', 'html'
    },
    # Allowed attributes (expanded to include ID which is critical for extraction)
    'attributes': {
        'a': ('href', 'title', 'target', 'rel', 'id', 'class', 'style'),
        'abbr': ('title', 'id', 'class'),
        'acronym': ('title', 'id', 'class'),
        'img': ('src', 'alt', 'title', 'width', 'height', 'id', 'class', 'style'),
        'div': ('class', 'id', 'style'),
        'p': ('class', 'id', 'style'),
        'span': ('class', 'id', 'style'),
        'table': ('class', 'width', 'id', 'style', 'border', 'cellpadding', 'cellspacing'),
        'td': ('class', 'colspan', 'rowspan', 'id', 'style', 'width', 'height', 'align', 'valign'),
        'th': ('class', 'colspan', 'rowspan', 'scope', 'id', 'style', 'width', 'height', 'align', 'valign'),
        'tr': ('class', 'id', 'style'),
        'ol': ('class', 'id', 'style'),
        'ul': ('class', 'id', 'style'),
        'li': ('class', 'id', 'style'),
        'form': ('id', 'class', 'action', 'method', 'enctype', 'style'),
        'input': ('id', 'class', 'type', 'name', 'value', 'placeholder', 'style'),
        'select': ('id', 'class', 'name', 'style'),
        'option': ('id', 'class', 'value', 'selected', 'style'),
        'label': ('id', 'class', 'for', 'style'),
        'button': ('id', 'class', 'type', 'style'),
        'iframe': ('id', 'class', 'src', 'width', 'height', 'style'),
        'script': ('id', 'type', 'src'),
        'style': ('id', 'type'),
        'link': ('id', 'rel', 'href', 'type'),
        'meta': ('id', 'name', 'content', 'charset', 'http-equiv'),
        'head': ('id',),
        'body': ('id', 'class', 'style'),
        'html': ('id', 'lang'),
    },
    # Allowed protocols for URLs
    'protocols': ['http', 'https', 'mailto', 'tel'],
    # Add nofollow to all links
    'add_nofollow': True,
    # Strip comments
    'strip_comments': True,
    # Allowed styles (none by default)
    'styles': {},
    # Allowed classes (none by default)
    'classes': {
        'div': ('table-responsive', 'alert', 'alert-info', 'alert-warning', 'alert-danger', 'alert-success'),
        'p': ('text-muted', 'text-primary', 'text-success', 'text-info', 'text-warning', 'text-danger'),
        'span': ('text-muted', 'text-primary', 'text-success', 'text-info', 'text-warning', 'text-danger'),
        'table': ('table', 'table-bordered', 'table-striped', 'table-hover'),
        'tr': ('active', 'success', 'info', 'warning', 'danger'),
        'td': ('active', 'success', 'info', 'warning', 'danger'),
        'th': ('active', 'success', 'info', 'warning', 'danger'),
    },
})

# Create sanitizer instance
sanitizer = Sanitizer(SANITIZER_SETTINGS)

def sanitize_html(html_content: Optional[str]) -> str:
    """
    For extraction purposes, we return the original HTML content.
    Since we're only extracting text from predefined IDs and not storing
    or displaying the HTML, this doesn't pose a security risk.

    Args:
        html_content: HTML content

    Returns:
        Original HTML content for extraction
    """
    if html_content is None:
        return ""

    # For extraction purposes, return the original content
    # This is safe because:
    # 1. We're only extracting text from predefined IDs and labels
    # 2. We're not storing the raw HTML in the database
    # 3. We're not rendering the HTML in the browser
    # 4. The HTML files come from a trusted source (complaint portal)
    # 5. The output is structured data, not HTML fragments
    logger.info("Using original HTML content for extraction (sanitization bypassed)")
    return html_content

def sanitize_text(text: Optional[str]) -> str:
    """
    Sanitize plain text by escaping HTML entities.

    Args:
        text: Text to sanitize

    Returns:
        Sanitized text with HTML entities escaped
    """
    if text is None:
        return ""

    try:
        # Use bleach to escape HTML entities
        return bleach.clean(text, tags=[], strip=True)
    except Exception as e:
        logger.error(f"Error sanitizing text: {str(e)}")
        # If sanitization fails, return empty string for safety
        return ""

def sanitize_document_fields(document: Dict[str, Any], fields_to_sanitize: List[str],
                            is_html: bool = False) -> Dict[str, Any]:
    """
    Sanitize specific fields in a document.

    Args:
        document: Document to sanitize
        fields_to_sanitize: List of field names to sanitize
        is_html: Whether the fields contain HTML content

    Returns:
        Sanitized document
    """
    if not document:
        return {}

    sanitized_doc = document.copy()

    for field in fields_to_sanitize:
        if field in sanitized_doc and sanitized_doc[field]:
            # Handle nested fields using dot notation
            if "." in field:
                parts = field.split(".")
                current = sanitized_doc
                for i, part in enumerate(parts):
                    if i == len(parts) - 1:
                        if part in current and current[part]:
                            if is_html:
                                current[part] = sanitize_html(str(current[part]))
                            else:
                                current[part] = sanitize_text(str(current[part]))
                    else:
                        if part in current and isinstance(current[part], dict):
                            current = current[part]
                        else:
                            break
            else:
                # Simple top-level field
                if is_html:
                    sanitized_doc[field] = sanitize_html(str(sanitized_doc[field]))
                else:
                    sanitized_doc[field] = sanitize_text(str(sanitized_doc[field]))

    return sanitized_doc

def sanitize_url(url: Optional[str]) -> str:
    """
    Sanitize a URL to prevent javascript: protocol and other malicious URLs.

    Args:
        url: URL to sanitize

    Returns:
        Sanitized URL or empty string if malicious
    """
    if url is None:
        return ""

    # Check for javascript: protocol and other potentially dangerous protocols
    dangerous_protocols = ['javascript:', 'data:', 'vbscript:', 'file:']
    for protocol in dangerous_protocols:
        if protocol in url.lower():
            logger.warning(f"Potentially dangerous URL detected: {url}")
            return ""

    # Only allow http:, https:, mailto:, and tel: protocols
    allowed_protocols = ['http:', 'https:', 'mailto:', 'tel:']
    protocol_match = re.match(r'^([a-z]+):', url.lower())
    if protocol_match:
        protocol = protocol_match.group(1) + ':'
        if protocol not in allowed_protocols:
            logger.warning(f"Disallowed URL protocol detected: {protocol}")
            return ""

    return url
