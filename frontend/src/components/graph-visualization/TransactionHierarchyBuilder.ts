/**
 * TransactionHierarchyBuilder.ts
 *
 * This module preprocesses transaction data to build a hierarchical structure
 * that can be used for more accurate graph visualization.
 */

// Define types for transaction data
export interface Transaction {
  id?: string;
  layer: number | string;
  sender_account?: string;
  sender_bank?: string;
  receiver_account?: string;
  receiver_bank?: string;
  amount?: string | number;
  txn_id?: string;
  sender_transaction_id?: string;
  receiver_transaction_id?: string;
  date?: string;
  type?: string;
  receiver_info?: string;
  isMainTransaction?: boolean;
  isSubTransaction?: boolean;
  parentId?: string; // Explicit reference to parent transaction
  multipleTransactions?: Transaction[]; // For nodes with multiple transactions
  subTransactions?: Transaction[]; // For nodes with sub-transactions
  hasMultipleTransactions?: boolean;
  [key: string]: any; // Allow for additional properties
}

// Define hierarchical node structure
export interface TransactionNode {
  transaction: Transaction;
  children: TransactionNode[];
  level: number; // Hierarchical level (0 for sender nodes, 1+ for transaction nodes)
  totalDescendants: number; // Total number of descendants (for layout calculations)
  maxDescendantsInLayer: { [key: number]: number }; // Max descendants per layer
  leafNodeCount: number; // Number of leaf nodes in the deepest layer
  maxDepth: number; // Maximum depth of this subtree
}

// Define the hierarchical tree structure
export interface TransactionHierarchy {
  senderNodes: TransactionNode[]; // Layer 0 nodes (sender accounts)
  transactionMap: Map<string, TransactionNode>; // Map of all transaction nodes by ID
  accountMap: Map<string, string>; // Map of account numbers to transaction IDs
}

/**
 * Generate a unique ID for a transaction if it doesn't have one
 * @param txn Transaction object
 * @param index Index in the array (for uniqueness)
 * @returns Transaction with a unique ID
 */
const ensureTransactionId = (txn: Transaction, index: number): Transaction => {
  if (!txn.id) {
    // Create a unique ID based on available properties
    const senderPart = txn.sender_account ? txn.sender_account.substring(0, 8).replace(/[^a-zA-Z0-9]/g, '') : '';
    const receiverPart = txn.receiver_account ? txn.receiver_account.substring(0, 8).replace(/[^a-zA-Z0-9]/g, '') : '';
    const txnIdPart = txn.txn_id ? txn.txn_id.substring(0, 8).replace(/[^a-zA-Z0-9]/g, '') : '';

    // Combine parts with index for uniqueness
    txn.id = `txn_${senderPart}_${receiverPart}_${txnIdPart}_${index}`;
  }
  return txn;
};

/**
 * Group transactions by their parent (sender account) and receiver account
 * This will combine multiple "Money Transfer to" transactions with the same parent and receiver account
 * @param transactions Array of transactions
 * @returns Grouped transactions
 */
export const groupTransactions = (transactions: Transaction[]): Transaction[] => {
  const groupedTxns: { [key: string]: Transaction[] } = {};

  // Group transactions by sender and receiver account
  transactions.forEach(txn => {
    const senderAccount = txn.sender_account || '';
    const receiverAccount = txn.receiver_account || '';

    // Create a unique key for this parent-receiver combination
    const groupKey = `${senderAccount}_to_${receiverAccount}`;

    if (!groupedTxns[groupKey]) {
      groupedTxns[groupKey] = [];
    }

    groupedTxns[groupKey].push(txn);
  });

  // Convert grouped transactions back to array
  const result: Transaction[] = [];

  Object.entries(groupedTxns).forEach(([, transactions]) => {
    if (transactions.length === 1) {
      // If only one transaction in the group, use it as is
      result.push(transactions[0]);
    } else {
      // If multiple transactions, create a combined transaction
      const firstTxn = transactions[0];
      const combinedTxn: Transaction = {
        ...firstTxn,
        hasMultipleTransactions: true,
        multipleTransactions: transactions,
        // Make sure we keep the receiver_transaction_id from the first transaction
        receiver_transaction_id: firstTxn.receiver_transaction_id || firstTxn.txn_id
      };
      result.push(combinedTxn);
    }
  });

  return result;
};

/**
 * Build a hierarchical structure from flat transaction data
 * @param transactions Array of transaction data
 * @returns Hierarchical structure of transactions
 */
export const buildTransactionHierarchy = (transactions: Transaction[]): TransactionHierarchy => {
  console.log("Building transaction hierarchy from", transactions.length, "transactions");

  // Ensure all transactions have IDs
  const txnsWithIds = transactions.map((txn, index) => ensureTransactionId(txn, index));

  // Separate main transactions from sub-transactions
  const mainTransactions = txnsWithIds.filter(txn =>
    txn.type === 'Money Transfer to' || txn.isMainTransaction === true
  );

  const subTransactions = txnsWithIds.filter(txn =>
    txn.type !== 'Money Transfer to' && txn.isMainTransaction !== true
  );

  console.log(`Separated ${mainTransactions.length} main transactions and ${subTransactions.length} sub-transactions`);

  // Group transactions by layer
  const transactionsByLayer: { [key: number]: Transaction[] } = {};

  mainTransactions.forEach(txn => {
    const layer = typeof txn.layer === 'string' ? parseInt(txn.layer, 10) : (txn.layer as number);
    if (!transactionsByLayer[layer]) {
      transactionsByLayer[layer] = [];
    }
    transactionsByLayer[layer].push(txn);
  });

  // Create maps to track transactions and accounts
  const transactionMap = new Map<string, TransactionNode>();
  const accountMap = new Map<string, string>(); // Maps account numbers to transaction IDs
  const txnIdMap = new Map<string, string>(); // Maps transaction IDs to node IDs

  // Extract sender accounts from Layer 1 transactions
  const senderAccounts = new Set<string>();
  if (transactionsByLayer[1]) {
    transactionsByLayer[1].forEach(txn => {
      if (txn.sender_account) {
        senderAccounts.add(txn.sender_account);
      }
    });
  }

  // Create sender nodes (Layer 0)
  const senderNodes: TransactionNode[] = [];

  Array.from(senderAccounts).forEach(account => {
    // Find a transaction with this sender account to get bank info
    const sampleTxn = transactionsByLayer[1]?.find(txn =>
      txn.sender_account === account
    );

    const senderTxn: Transaction = {
      id: `sender_${account.replace(/[^a-zA-Z0-9]/g, '')}`,
      layer: 0,
      sender_account: account,
      sender_bank: sampleTxn?.sender_bank || 'Unknown Bank',
      type: sampleTxn?.type || 'Money Transfer to',
      isSenderNode: true,
      isVictim: true
    };

    const senderNode: TransactionNode = {
      transaction: senderTxn,
      children: [],
      level: 0,
      totalDescendants: 0,
      maxDescendantsInLayer: {},
      leafNodeCount: 0,
      maxDepth: 0
    };

    senderNodes.push(senderNode);
    transactionMap.set(senderTxn.id as string, senderNode);
    accountMap.set(account, senderTxn.id as string);
  });

  // Process each layer, starting from layer 1, to build parent-child relationships for main transactions
  Object.keys(transactionsByLayer)
    .map(Number)
    .filter(layer => layer > 0)
    .sort((a, b) => a - b)
    .forEach(layer => {
      // Group main transactions by parent-receiver combinations
      const groupedLayerTxns = groupTransactions(transactionsByLayer[layer]);

      groupedLayerTxns.forEach(txn => {
        const senderAccount = txn.sender_account || '';
        const receiverAccount = txn.receiver_account || '';
        const txnId = txn.txn_id || '';
        const receiverTxnId = txn.receiver_transaction_id || '';

        // Create transaction node
        const txnNode: TransactionNode = {
          transaction: txn,
          children: [],
          level: layer,
          totalDescendants: 0,
          maxDescendantsInLayer: {},
          leafNodeCount: 0,
          maxDepth: 0
        };

        transactionMap.set(txn.id as string, txnNode);

        // Track this account and transaction ID for future connections
        if (receiverAccount) {
          accountMap.set(receiverAccount, txn.id as string);
        }
        if (txnId) {
          txnIdMap.set(txnId, txn.id as string);
        }
        if (receiverTxnId) {
          txnIdMap.set(receiverTxnId, txn.id as string);
        }

        // Find parent node
        let parentId: string | undefined;

        // First, check if parentId is explicitly set
        if (txn.parentId && transactionMap.has(txn.parentId)) {
          parentId = txn.parentId;
        }
        // Otherwise, try to find parent by sender account
        else if (senderAccount) {
          parentId = accountMap.get(senderAccount);
        }

        // If parent found, add this node as a child
        if (parentId) {
          const parentNode = transactionMap.get(parentId);
          if (parentNode) {
            parentNode.children.push(txnNode);
            // Set explicit parentId for reference
            txn.parentId = parentId;
          }
        }
      });
    });

  // Now process sub-transactions and attach them to their parent nodes
  subTransactions.forEach(subTxn => {
    const senderAccount = subTxn.sender_account || '';
    const senderTxnId = subTxn.sender_transaction_id || subTxn.txn_id || '';
    const receiverAccount = subTxn.receiver_account || '';

    // Try to find parent node by matching:
    // 1. First try to match sender transaction ID with a parent's receiver transaction ID
    // 2. Then try to match sender account with a parent's receiver account
    let parentNodeId: string | undefined;

    // Try to find by transaction ID first
    if (senderTxnId) {
      parentNodeId = txnIdMap.get(senderTxnId);
    }

    // If not found by transaction ID, try by account
    if (!parentNodeId && senderAccount) {
      parentNodeId = accountMap.get(senderAccount);
    }

    // If we found a parent node, attach this sub-transaction to it
    if (parentNodeId) {
      const parentNode = transactionMap.get(parentNodeId);
      if (parentNode) {
        // Initialize sub-transactions array if it doesn't exist
        if (!parentNode.transaction.subTransactions) {
          parentNode.transaction.subTransactions = [];
          parentNode.transaction.hasSubTransactions = true;
        }

        // Add this sub-transaction to the parent's subTransactions array
        parentNode.transaction.subTransactions.push(subTxn);
      }
    } else {
      console.log(`Could not find parent for sub-transaction: ${subTxn.type} - ${senderTxnId || senderAccount}`);
    }
  });

  // Calculate total descendants, leaf nodes, and max depth for each node (bottom-up)
  const calculateHierarchyMetrics = (node: TransactionNode): { descendants: number, leafNodes: number, depth: number } => {
    // Reset maxDescendantsInLayer
    node.maxDescendantsInLayer = {};

    // If no children, this is a leaf node
    if (node.children.length === 0) {
      node.leafNodeCount = 1;
      node.maxDepth = 0;
      node.totalDescendants = 0;
      return { descendants: 0, leafNodes: 1, depth: 0 };
    }

    // Calculate metrics by processing all children
    let totalDescendants = node.children.length;
    let totalLeafNodes = 0;
    let maxChildDepth = 0;

    // Track descendants by layer
    node.children.forEach(child => {
      const childLayer = child.level;
      if (!node.maxDescendantsInLayer[childLayer]) {
        node.maxDescendantsInLayer[childLayer] = 0;
      }
      node.maxDescendantsInLayer[childLayer]++;

      // Recursively calculate metrics for this child
      const childMetrics = calculateHierarchyMetrics(child);
      totalDescendants += childMetrics.descendants;
      totalLeafNodes += childMetrics.leafNodes;
      maxChildDepth = Math.max(maxChildDepth, childMetrics.depth);

      // Update maxDescendantsInLayer for deeper layers
      Object.entries(child.maxDescendantsInLayer).forEach(([layerStr, count]) => {
        const layer = parseInt(layerStr, 10);
        if (!node.maxDescendantsInLayer[layer] || node.maxDescendantsInLayer[layer] < count) {
          node.maxDescendantsInLayer[layer] = count;
        }
      });
    });

    // Update node metrics
    node.totalDescendants = totalDescendants;
    node.leafNodeCount = totalLeafNodes;
    node.maxDepth = maxChildDepth + 1;

    return {
      descendants: totalDescendants,
      leafNodes: totalLeafNodes,
      depth: maxChildDepth + 1
    };
  };

  // Calculate hierarchy metrics for all sender nodes
  senderNodes.forEach(node => {
    calculateHierarchyMetrics(node);
  });

  // Sort sender nodes by leaf node count (ascending)
  senderNodes.sort((a, b) => {
    // First compare by leaf node count
    const leafDiff = a.leafNodeCount - b.leafNodeCount;
    if (leafDiff !== 0) return leafDiff;

    // If leaf counts are equal, compare by total descendants
    return a.totalDescendants - b.totalDescendants;
  });

  // Log summary of the hierarchy
  console.log(`Built hierarchy with ${senderNodes.length} sender nodes`);
  let totalMainNodes = 0;
  let totalSubTransactions = 0;

  senderNodes.forEach(senderNode => {
    const countNodesInTree = (node: TransactionNode): { mainNodes: number, subTransactions: number } => {
      let mainNodes = 0;
      let subTxns = 0;

      // Count sub-transactions in this node
      if (node.transaction.hasSubTransactions && node.transaction.subTransactions) {
        subTxns += node.transaction.subTransactions.length;
      }

      // Count this node
      mainNodes++;

      // Count children recursively
      node.children.forEach(child => {
        const counts = countNodesInTree(child);
        mainNodes += counts.mainNodes;
        subTxns += counts.subTransactions;
      });

      return { mainNodes, subTransactions: subTxns };
    };

    const counts = countNodesInTree(senderNode);
    totalMainNodes += counts.mainNodes;
    totalSubTransactions += counts.subTransactions;
  });

  console.log(`Total main transaction nodes: ${totalMainNodes}`);
  console.log(`Total sub-transactions (attached to parent nodes): ${totalSubTransactions}`);

  return {
    senderNodes,
    transactionMap,
    accountMap
  };
};
