import React, { useState, useEffect, useMemo } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  flexRender,
  createColumnHelper,
  SortingState
} from '@tanstack/react-table';
import { FiSearch, FiRefreshCw, FiFilter } from 'react-icons/fi';
import { useAlert } from '../context/TailwindAlertContext';
import { useLayout } from '../context/LayoutContext';
import referenceDataService from '../services/referenceDataService';
import Spinner from '../components/common/Spinner';

// Define the search type (nodal only)
type SearchType = 'nodal';

const InformationSearchPage: React.FC = () => {
  // Set layout configuration
  const { setLayoutConfig } = useLayout();
  const { showError } = useAlert();

  // State for search parameters
  const [] = useState<SearchType>('nodal'); // Fixed to nodal search only
  const [searchQuery, setSearchQuery] = useState('');
  const [organization, setOrganization] = useState('');
  const [organizationType, setOrganizationType] = useState('');
  const [state, setState] = useState('');

  // State for data and loading
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const [isFilterOpen, setIsFilterOpen] = useState(false);

  // Set layout configuration on mount
  useEffect(() => {
    setLayoutConfig({
      hideSidebar: false,
      fullWidth: false
    });
  }, [setLayoutConfig]);

  // Define columns for nodal officers
  const columnHelper = createColumnHelper<any>();

  const columns = useMemo(
    () => [
      columnHelper.accessor('name', {
        header: 'Name',
        cell: info => info.getValue(),
      }),
      columnHelper.accessor('organization', {
        header: 'Organization',
        cell: info => info.getValue(),
      }),
      columnHelper.accessor('organization_type', {
        header: 'Type',
        cell: info => info.getValue(),
      }),
      columnHelper.accessor('designation', {
        header: 'Designation',
        cell: info => info.getValue(),
      }),
      columnHelper.accessor('email', {
        header: 'Email',
        cell: info => info.getValue(),
      }),
      columnHelper.accessor('phone', {
        header: 'Phone',
        cell: info => info.getValue(),
      }),
      columnHelper.accessor('address', {
        header: 'Address',
        cell: info => info.getValue(),
      }),
      columnHelper.accessor('state', {
        header: 'State',
        cell: info => info.getValue(),
      }),
    ],
    []
  );

  // Initialize react-table
  const [sorting, setSorting] = useState<SortingState>([]);

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    state: {
      sorting,
      pagination: {
        pageIndex: currentPage - 1,
        pageSize,
      },
    },
    onSortingChange: setSorting,
    manualPagination: true,
    pageCount: totalPages,
  });

  // Function to perform search
  const performSearch = async (page = 1) => {
    setLoading(true);
    try {
      const result = await referenceDataService.searchNodalOfficers(
        searchQuery,
        organization,
        organizationType,
        state,
        page,
        pageSize,
        true // Force refresh
      );

      if (result.success && result.data) {
        setData(result.data.items || []);
        setTotalItems(result.data.total || 0);
        setTotalPages(result.data.total_pages || 0);
        setCurrentPage(result.data.page || 1);
      } else {
        showError(result.error || 'Failed to search Nodal Officers');
        setData([]);
      }
    } catch (error) {
      console.error('Search error:', error);
      showError('An error occurred while searching');
      setData([]);
    } finally {
      setLoading(false);
    }
  };

  // Handle search form submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
    performSearch(1);
  };

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
    performSearch(newPage);
  };

  // Reset search filters
  const resetFilters = () => {
    setData([]);
    setTotalItems(0);
    setTotalPages(0);
    setCurrentPage(1);
    // Reset filters
    setSearchQuery('');
    setOrganization('');
    setOrganizationType('');
    setState('');
  };

  // Toggle filter panel
  const toggleFilter = () => {
    setIsFilterOpen(!isFilterOpen);
  };

  return (
    <div className="p-4 space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6 gap-4">
        <h1 className="text-2xl font-bold" style={{ color: 'var(--theme-text)' }}>
          Nodal Officer Search
        </h1>

        <div className="flex space-x-2">
          <button
            onClick={resetFilters}
            className="px-4 py-2 rounded-lg transition-all"
            style={{
              backgroundColor: 'var(--theme-bg-card)',
              color: 'var(--theme-text)'
            }}
          >
            Reset Filters
          </button>
        </div>
      </div>

      <div className="rounded-lg shadow-md p-4" style={{
        backgroundColor: 'var(--theme-bg-primary)',
        borderColor: 'var(--theme-border)',
        boxShadow: '0 0 10px var(--theme-glow)'
      }}>
        <form onSubmit={handleSearch} className="space-y-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-grow">
              <div className="relative">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search Nodal Officers..."
                  className="w-full px-4 py-2 pl-10 border rounded-lg"
                  style={{
                    backgroundColor: 'var(--theme-bg-lighter)',
                    borderColor: 'var(--theme-border)',
                    color: 'var(--theme-text)'
                  }}
                />
                <FiSearch className="absolute left-2.5 top-1/2 transform -translate-y-1/2" style={{ color: 'var(--theme-text-secondary)' }} />
              </div>
            </div>

            <div className="flex gap-2">
              <button
                type="button"
                onClick={toggleFilter}
                className="px-4 py-2 rounded-lg transition-all flex items-center gap-2"
                style={{
                  backgroundColor: 'var(--theme-bg-card)',
                  color: 'var(--theme-text)'
                }}
              >
                <FiFilter /> Filters
              </button>

              <button
                type="submit"
                className="px-4 py-2 rounded-lg transition-all flex items-center gap-2"
                style={{
                  backgroundColor: 'var(--theme-button)',
                  color: 'white',
                  boxShadow: '0 0 10px var(--theme-glow)'
                }}
              >
                <FiSearch /> Search
              </button>

              <button
                type="button"
                onClick={() => performSearch(currentPage)}
                className="px-4 py-2 rounded-lg transition-all flex items-center gap-2"
                style={{
                  backgroundColor: 'var(--theme-bg-card)',
                  color: 'var(--theme-text)'
                }}
              >
                <FiRefreshCw className={loading ? 'animate-spin' : ''} /> Refresh
              </button>
            </div>
          </div>

          {isFilterOpen && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4 p-4 rounded-lg" style={{
              backgroundColor: 'var(--theme-bg-lighter)',
              borderColor: 'var(--theme-border)',
              border: '1px solid var(--theme-border)'
            }}>
              <div>
                <label className="block text-sm font-medium mb-1" style={{ color: 'var(--theme-text)' }}>
                  Organization
                </label>
                <input
                  type="text"
                  value={organization}
                  onChange={(e) => setOrganization(e.target.value)}
                  className="w-full px-3 py-2 border rounded-lg"
                  style={{
                    backgroundColor: 'var(--theme-bg-card)',
                    borderColor: 'var(--theme-border)',
                    color: 'var(--theme-text)'
                  }}
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1" style={{ color: 'var(--theme-text)' }}>
                  Organization Type
                </label>
                <select
                  value={organizationType}
                  onChange={(e) => setOrganizationType(e.target.value)}
                  className="w-full px-3 py-2 border rounded-lg"
                  style={{
                    backgroundColor: 'var(--theme-bg-card)',
                    borderColor: 'var(--theme-border)',
                    color: 'var(--theme-text)'
                  }}
                >
                  <option value="">All Types</option>
                  <option value="Bank">Bank</option>
                  <option value="Wallet">Wallet</option>
                  <option value="E-commerce">E-commerce</option>
                  <option value="Other">Other</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1" style={{ color: 'var(--theme-text)' }}>
                  State
                </label>
                <input
                  type="text"
                  value={state}
                  onChange={(e) => setState(e.target.value)}
                  className="w-full px-3 py-2 border rounded-lg"
                  style={{
                    backgroundColor: 'var(--theme-bg-card)',
                    borderColor: 'var(--theme-border)',
                    color: 'var(--theme-text)'
                  }}
                />
              </div>


            </div>
          )}
        </form>
      </div>

      <div className="rounded-lg shadow-md overflow-hidden" style={{
        backgroundColor: 'var(--theme-bg-primary)',
        boxShadow: '0 0 10px var(--theme-glow)'
      }}>
        {loading ? (
          <div className="flex justify-center items-center p-8">
            <Spinner size="lg" />
          </div>
        ) : (
          <>
            <div className="overflow-x-auto" style={{ maxWidth: '100%' }}>
              <table
                className="w-full border-collapse"
                style={{
                  borderSpacing: 0,
                  border: '1px solid var(--theme-border)',
                  tableLayout: 'auto',
                  backgroundColor: 'var(--theme-bg-primary)'
                }}
              >
                <thead>
                  {table.getHeaderGroups().map(headerGroup => (
                    <tr key={headerGroup.id}>
                      {headerGroup.headers.map(header => (
                        <th
                          key={header.id}
                          onClick={header.column.getToggleSortingHandler()}
                          className="px-4 py-3 text-left text-sm font-medium border-b border-r"
                          style={{
                            borderBottom: '1px solid var(--theme-border)',
                            borderRight: '1px solid var(--theme-border)',
                            backgroundColor: 'var(--theme-accent)',
                            color: 'var(--theme-text)',
                            minWidth: header.id.includes('address') ? '250px' :
                                     header.id.includes('name') ? '180px' :
                                     header.id.includes('email') ? '200px' :
                                     header.id.includes('phone') ? '150px' :
                                     header.id.includes('organization') ? '180px' : '120px'
                          }}
                        >
                          {flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                          <span>
                            {{
                              asc: ' 🔼',
                              desc: ' 🔽',
                            }[header.column.getIsSorted() as string] ?? ''}
                          </span>
                        </th>
                      ))}
                    </tr>
                  ))}
                </thead>
                <tbody>
                  {data.length > 0 ? (
                    table.getRowModel().rows.map(row => (
                      <tr key={row.id}>
                        {row.getVisibleCells().map(cell => (
                          <td
                            key={cell.id}
                            className="px-4 py-2 text-sm border-b border-r"
                            style={{
                              borderBottom: '1px solid var(--theme-border)',
                              borderRight: '1px solid var(--theme-border)',
                              backgroundColor: 'var(--theme-bg-card)',
                              color: 'var(--theme-text)',
                              minWidth: cell.column.id.includes('address') ? '250px' :
                                       cell.column.id.includes('name') ? '180px' :
                                       cell.column.id.includes('email') ? '200px' :
                                       cell.column.id.includes('phone') ? '150px' :
                                       cell.column.id.includes('organization') ? '180px' : '120px'
                            }}
                          >
                            {flexRender(
                              cell.column.columnDef.cell,
                              cell.getContext()
                            )}
                          </td>
                        ))}
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td
                        colSpan={columns.length}
                        className="px-4 py-4 text-center text-gray-500 dark:text-gray-400"
                      >
                        No data found. Try a different search.
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {totalPages > 0 && (
              <div className="flex items-center justify-between px-4 py-3 border-t" style={{
                backgroundColor: 'var(--theme-bg-lighter)',
                borderColor: 'var(--theme-border)'
              }}>
                <div className="flex-1 flex justify-between sm:hidden">
                  <button
                    onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium rounded-md`}
                    style={{
                      backgroundColor: currentPage === 1 ? 'var(--theme-bg-card)' : 'var(--theme-bg-primary)',
                      borderColor: 'var(--theme-border)',
                      color: currentPage === 1 ? 'var(--theme-text-secondary)' : 'var(--theme-text)',
                      opacity: currentPage === 1 ? 0.7 : 1
                    }}
                  >
                    Previous
                  </button>
                  <button
                    onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                    className={`ml-3 relative inline-flex items-center px-4 py-2 border text-sm font-medium rounded-md`}
                    style={{
                      backgroundColor: currentPage === totalPages ? 'var(--theme-bg-card)' : 'var(--theme-bg-primary)',
                      borderColor: 'var(--theme-border)',
                      color: currentPage === totalPages ? 'var(--theme-text-secondary)' : 'var(--theme-text)',
                      opacity: currentPage === totalPages ? 0.7 : 1
                    }}
                  >
                    Next
                  </button>
                </div>
                <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                  <div>
                    <p className="text-sm" style={{ color: 'var(--theme-text)' }}>
                      Showing <span className="font-medium">{data.length > 0 ? (currentPage - 1) * pageSize + 1 : 0}</span> to{' '}
                      <span className="font-medium">
                        {Math.min(currentPage * pageSize, totalItems)}
                      </span>{' '}
                      of <span className="font-medium">{totalItems}</span> results
                    </p>
                  </div>
                  <div>
                    <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                      <button
                        onClick={() => handlePageChange(1)}
                        disabled={currentPage === 1}
                        className={`relative inline-flex items-center px-2 py-2 rounded-l-md border text-sm font-medium`}
                        style={{
                          backgroundColor: currentPage === 1 ? 'var(--theme-bg-card)' : 'var(--theme-bg-primary)',
                          borderColor: 'var(--theme-border)',
                          color: currentPage === 1 ? 'var(--theme-text-secondary)' : 'var(--theme-text)',
                          opacity: currentPage === 1 ? 0.7 : 1
                        }}
                      >
                        <span className="sr-only">First</span>
                        ⟪
                      </button>
                      <button
                        onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                        disabled={currentPage === 1}
                        className={`relative inline-flex items-center px-2 py-2 border text-sm font-medium`}
                        style={{
                          backgroundColor: currentPage === 1 ? 'var(--theme-bg-card)' : 'var(--theme-bg-primary)',
                          borderColor: 'var(--theme-border)',
                          color: currentPage === 1 ? 'var(--theme-text-secondary)' : 'var(--theme-text)',
                          opacity: currentPage === 1 ? 0.7 : 1
                        }}
                      >
                        <span className="sr-only">Previous</span>
                        ←
                      </button>

                      {/* Page numbers */}
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        let pageNum;
                        if (totalPages <= 5) {
                          pageNum = i + 1;
                        } else if (currentPage <= 3) {
                          pageNum = i + 1;
                        } else if (currentPage >= totalPages - 2) {
                          pageNum = totalPages - 4 + i;
                        } else {
                          pageNum = currentPage - 2 + i;
                        }

                        return (
                          <button
                            key={pageNum}
                            onClick={() => handlePageChange(pageNum)}
                            className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium`}
                            style={{
                              backgroundColor: currentPage === pageNum ? 'var(--theme-button)' : 'var(--theme-bg-primary)',
                              borderColor: 'var(--theme-border)',
                              color: currentPage === pageNum ? 'white' : 'var(--theme-text)',
                              zIndex: currentPage === pageNum ? 10 : 'auto',
                              boxShadow: currentPage === pageNum ? '0 0 8px var(--theme-glow)' : 'none'
                            }}
                          >
                            {pageNum}
                          </button>
                        );
                      })}

                      <button
                        onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                        disabled={currentPage === totalPages}
                        className={`relative inline-flex items-center px-2 py-2 border text-sm font-medium`}
                        style={{
                          backgroundColor: currentPage === totalPages ? 'var(--theme-bg-card)' : 'var(--theme-bg-primary)',
                          borderColor: 'var(--theme-border)',
                          color: currentPage === totalPages ? 'var(--theme-text-secondary)' : 'var(--theme-text)',
                          opacity: currentPage === totalPages ? 0.7 : 1
                        }}
                      >
                        <span className="sr-only">Next</span>
                        →
                      </button>
                      <button
                        onClick={() => handlePageChange(totalPages)}
                        disabled={currentPage === totalPages}
                        className={`relative inline-flex items-center px-2 py-2 rounded-r-md border text-sm font-medium`}
                        style={{
                          backgroundColor: currentPage === totalPages ? 'var(--theme-bg-card)' : 'var(--theme-bg-primary)',
                          borderColor: 'var(--theme-border)',
                          color: currentPage === totalPages ? 'var(--theme-text-secondary)' : 'var(--theme-text)',
                          opacity: currentPage === totalPages ? 0.7 : 1
                        }}
                      >
                        <span className="sr-only">Last</span>
                        ⟫
                      </button>
                    </nav>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default InformationSearchPage;
