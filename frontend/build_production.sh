#!/bin/bash
# Production build script for Cyber Sakha frontend
# This script builds the frontend for production deployment

set -e  # Exit on any error

# Display banner
echo "====================================================="
echo "Cyber Sakha Frontend Production Build"
echo "====================================================="

# Check if .env.production exists
if [ ! -f ".env.production" ]; then
  echo "ERROR: .env.production file not found"
  echo "Please create a .env.production file with your production settings"
  exit 1
fi

# Check if VITE_API_BASE_URL is set in .env.production
if ! grep -q "VITE_API_BASE_URL" .env.production; then
  echo "ERROR: VITE_API_BASE_URL is not set in .env.production"
  echo "Please set VITE_API_BASE_URL to your production API endpoint"
  exit 1
fi

# Install dependencies
echo "Installing dependencies..."
npm ci

# Run linting
echo "Running linting..."
npm run lint

# Run type checking
echo "Running type checking..."
npm run typecheck

# Build for production
echo "Building for production..."
npm run build

# Optimize images
echo "Optimizing images..."
find dist -type f -name "*.png" -o -name "*.jpg" -o -name "*.jpeg" -o -name "*.gif" | xargs -P 4 -I {} optipng -quiet -strip all "{}"

# Copy Nginx configuration
echo "Copying Nginx configuration..."
cp nginx.prod.conf dist/nginx.conf

# Create version file
echo "Creating version file..."
VERSION=$(grep -m 1 '"version":' package.json | cut -d '"' -f 4)
BUILD_DATE=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
echo "{\"version\":\"$VERSION\",\"buildDate\":\"$BUILD_DATE\"}" > dist/version.json

# Create a production deployment archive
echo "Creating deployment archive..."
tar -czf cyber-sakha-frontend-$VERSION.tar.gz -C dist .

echo "====================================================="
echo "Build completed successfully!"
echo "Deployment archive: cyber-sakha-frontend-$VERSION.tar.gz"
echo "====================================================="

# Instructions for deployment
echo "To deploy to a server with Nginx:"
echo "1. Extract the archive to /var/www/html"
echo "2. Copy nginx.conf to /etc/nginx/conf.d/cyber-sakha.conf"
echo "3. Restart Nginx: sudo systemctl restart nginx"
echo "====================================================="
