<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Shield -->
  <path d="M100 10L30 40V100C30 146.5 59.5 180 100 190C140.5 180 170 146.5 170 100V40L100 10Z" fill="url(#shield-gradient)" />
  
  <!-- Inner Shield -->
  <path d="M100 30L50 52V100C50 135 70 160 100 170C130 160 150 135 150 100V52L100 30Z" fill="url(#inner-shield-gradient)" />
  
  <!-- S Letter -->
  <path d="M85 70C85 65 90 60 100 60C110 60 115 65 115 70C115 75 110 80 100 80C90 80 85 85 85 90C85 95 90 100 100 100C110 100 115 95 115 90" stroke="white" stroke-width="10" stroke-linecap="round" />
  
  <!-- Horizontal Lines -->
  <path d="M70 110H130" stroke="white" stroke-width="6" stroke-linecap="round" />
  <path d="M80 125H120" stroke="white" stroke-width="6" stroke-linecap="round" />
  <path d="M90 140H110" stroke="white" stroke-width="6" stroke-linecap="round" />
  
  <!-- Glowing Effect -->
  <path d="M100 10L30 40V100C30 146.5 59.5 180 100 190C140.5 180 170 146.5 170 100V40L100 10Z" stroke="url(#glow-gradient)" stroke-width="2" />
  
  <!-- Gradients -->
  <defs>
    <linearGradient id="shield-gradient" x1="30" y1="10" x2="170" y2="190" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#00B0FF" />
      <stop offset="1" stop-color="#3A7BD5" />
    </linearGradient>
    
    <linearGradient id="inner-shield-gradient" x1="50" y1="30" x2="150" y2="170" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#3A7BD5" />
      <stop offset="1" stop-color="#00B0FF" />
    </linearGradient>
    
    <linearGradient id="glow-gradient" x1="30" y1="10" x2="170" y2="190" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFFFFF" stop-opacity="0.8" />
      <stop offset="1" stop-color="#FFFFFF" stop-opacity="0.2" />
    </linearGradient>
  </defs>
</svg>
