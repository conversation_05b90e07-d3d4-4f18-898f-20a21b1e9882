import os
import base64
from cryptography.fernet import Ferne<PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import logging

logger = logging.getLogger(__name__)

# Get encryption key from environment or generate one
ENCRYPTION_KEY = os.getenv("ENCRYPTION_KEY")
if not ENCRYPTION_KEY:
    logger.warning("ENCRYPTION_KEY not found in environment, generating a temporary one")
    ENCRYPTION_KEY = base64.urlsafe_b64encode(os.urandom(32)).decode()

# Create a Fernet cipher for encryption/decryption
try:
    cipher_suite = Fernet(ENCRYPTION_KEY.encode())
except Exception as e:
    logger.error(f"Error initializing encryption: {e}")
    # Generate a valid key if the provided one is invalid
    logger.warning("Generating a new encryption key")
    ENCRYPTION_KEY = base64.urlsafe_b64encode(os.urandom(32)).decode()
    cipher_suite = Fernet(ENCRYPTION_KEY.encode())

def derive_key(password: str, salt: bytes | None = None) -> tuple:
    """
    Derive a key from a password using PBKDF2.

    Args:
        password: The password to derive the key from
        salt: Optional salt, will be generated if not provided

    Returns:
        Tuple of (key, salt)
    """
    if salt is None:
        salt = os.urandom(16)

    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=salt,
        iterations=100000,
    )

    key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
    return key, salt

def encrypt_data(data: str) -> str:
    """
    Encrypt a string using Fernet symmetric encryption.

    Args:
        data: The string to encrypt

    Returns:
        Base64-encoded encrypted string
    """
    if not data:
        return data

    try:
        encrypted_data = cipher_suite.encrypt(data.encode())
        return base64.urlsafe_b64encode(encrypted_data).decode()
    except Exception as e:
        logger.error(f"Encryption error: {e}")
        # Return original data if encryption fails
        return data

def decrypt_data(encrypted_data: str) -> str:
    """
    Decrypt a Fernet-encrypted string.

    Args:
        encrypted_data: Base64-encoded encrypted string

    Returns:
        Decrypted string
    """
    if not encrypted_data:
        return encrypted_data

    try:
        decoded_data = base64.urlsafe_b64decode(encrypted_data.encode())
        decrypted_data = cipher_suite.decrypt(decoded_data)
        return decrypted_data.decode()
    except Exception as e:
        logger.error(f"Decryption error for data '{encrypted_data[:50]}...': {e}")
        # Return original data if decryption fails
        return encrypted_data

def encrypt_document(document: dict, fields_to_encrypt: list) -> dict:
    """
    Encrypt specific fields in a document.

    Args:
        document: The document to encrypt fields in
        fields_to_encrypt: List of field names to encrypt

    Returns:
        Document with encrypted fields
    """
    if not document or not fields_to_encrypt:
        return document

    encrypted_doc = document.copy()

    for field in fields_to_encrypt:
        if field in encrypted_doc and encrypted_doc[field]:
            # Handle nested fields using dot notation
            if "." in field:
                parts = field.split(".")
                current = encrypted_doc
                for i, part in enumerate(parts):
                    if i == len(parts) - 1:
                        if part in current and current[part]:
                            current[part] = encrypt_data(str(current[part]))
                    else:
                        if part in current and current[part]:
                            current = current[part]
                        else:
                            break
            else:
                # Simple top-level field
                encrypted_doc[field] = encrypt_data(str(encrypted_doc[field]))

    return encrypted_doc

def decrypt_document(document: dict, fields_to_decrypt: list) -> dict:
    """
    Decrypt specific fields in a document.

    Args:
        document: The document with encrypted fields
        fields_to_decrypt: List of field names to decrypt

    Returns:
        Document with decrypted fields
    """
    if not document or not fields_to_decrypt:
        return document

    decrypted_doc = document.copy()

    for field in fields_to_decrypt:
        if field in decrypted_doc and decrypted_doc[field]:
            # Handle nested fields using dot notation
            if "." in field:
                parts = field.split(".")
                current = decrypted_doc
                for i, part in enumerate(parts):
                    if i == len(parts) - 1:
                        if part in current and current[part]:
                            current[part] = decrypt_data(str(current[part]))
                    else:
                        if part in current and current[part]:
                            current = current[part]
                        else:
                            break
            else:
                # Simple top-level field
                decrypted_doc[field] = decrypt_data(str(decrypted_doc[field]))

    return decrypted_doc
