import React from 'react';
import { Link as RouterLink } from 'react-router-dom';
import { FiChevronRight } from 'react-icons/fi';

interface BreadcrumbItem {
  label: string;
  href?: string;
}

interface PageHeaderProps {
  title: string;
  subtitle?: string;
  breadcrumbs?: BreadcrumbItem[];
  action?: React.ReactNode;
  className?: string;
}

const TailwindPageHeader: React.FC<PageHeaderProps> = ({
  title,
  subtitle,
  breadcrumbs,
  action,
  className,
}) => {
  return (
    <div className={`mb-6 animate-fade-in ${className || ''}`}>
      {breadcrumbs && breadcrumbs.length > 0 && (
        <nav className="flex mb-3" aria-label="Breadcrumb">
          <ol className="inline-flex items-center space-x-1 md:space-x-2">
            {breadcrumbs.map((item, index) => {
              const isLast = index === breadcrumbs.length - 1;

              return (
                <li key={item.label} className="flex items-center">
                  {index > 0 && (
                    <FiChevronRight className="mx-1 h-3 w-3 text-gray-400 dark:text-gray-500" />
                  )}

                  {isLast || !item.href ? (
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      {item.label}
                    </span>
                  ) : (
                    <RouterLink
                      to={item.href}
                      className="text-sm text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400"
                    >
                      {item.label}
                    </RouterLink>
                  )}
                </li>
              );
            })}
          </ol>
        </nav>
      )}

      <div className="flex flex-wrap items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-gray-900 to-gray-700 dark:from-white dark:to-gray-300">
            {title}
          </h1>

          {subtitle && (
            <p className="mt-1 text-gray-500 dark:text-gray-400">
              {subtitle}
            </p>
          )}
        </div>

        {action && (
          <div className="flex-shrink-0">
            {action}
          </div>
        )}
      </div>
    </div>
  );
};

export default TailwindPageHeader;
