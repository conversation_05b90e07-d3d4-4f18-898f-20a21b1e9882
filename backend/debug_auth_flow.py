#!/usr/bin/env python3
"""
Comprehensive authentication flow debugging script
"""
import requests
import json
import time
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:8000"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "Rohit@9988776"

def print_separator(title):
    print("\n" + "="*60)
    print(f"🔍 {title}")
    print("="*60)

def print_cookies(session):
    print("\n📍 Current Cookies:")
    for cookie in session.cookies:
        print(f"   {cookie.name}: {cookie.value[:20]}{'...' if len(cookie.value) > 20 else ''}")
        print(f"      Domain: {cookie.domain}, Path: {cookie.path}")
        print(f"      Secure: {cookie.secure}, HttpOnly: {cookie.has_nonstandard_attr('HttpOnly')}")

def test_comprehensive_auth_flow():
    """Test the complete authentication flow with detailed debugging"""
    print_separator("COMPREHENSIVE AUTHENTICATION FLOW DEBUG")
    
    # Create a session to maintain cookies
    session = requests.Session()
    
    # Step 1: Get CSRF token
    print_separator("STEP 1: Getting CSRF Token")
    try:
        resp = session.get(f"{BASE_URL}/csrf/token")
        print(f"Status: {resp.status_code}")
        print(f"Headers: {dict(resp.headers)}")
        
        if resp.status_code == 200:
            csrf_token = resp.headers.get('X-CSRF-Token')
            if csrf_token:
                print(f"✅ CSRF token obtained: {csrf_token[:16]}...")
                print_cookies(session)
            else:
                print("❌ No CSRF token in response headers")
                print("Available headers:", list(resp.headers.keys()))
                return False
        else:
            print(f"❌ Failed to get CSRF token: {resp.status_code}")
            print(f"Response: {resp.text}")
            return False
    except Exception as e:
        print(f"❌ Error getting CSRF token: {e}")
        return False
    
    # Step 2: Attempt login
    print_separator("STEP 2: Login Attempt")
    try:
        login_data = {
            "username": TEST_EMAIL,
            "password": TEST_PASSWORD
        }
        headers = {
            "X-CSRF-Token": csrf_token,
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        print(f"Login URL: {BASE_URL}/auth/login")
        print(f"Login data: {login_data}")
        print(f"Headers: {headers}")
        
        resp = session.post(f"{BASE_URL}/auth/login", 
                          data=login_data, 
                          headers=headers)
        
        print(f"Login response status: {resp.status_code}")
        print(f"Login response headers: {dict(resp.headers)}")
        
        if resp.status_code == 200:
            try:
                response_data = resp.json()
                print(f"✅ Login successful")
                print(f"Response data keys: {list(response_data.keys())}")
                if 'user' in response_data:
                    user_data = response_data['user']
                    print(f"User email: {user_data.get('email', 'N/A')}")
                    print(f"User verified: {user_data.get('email_verified', 'N/A')}")
                
                # Check for new CSRF token
                new_csrf = resp.headers.get('X-CSRF-Token')
                if new_csrf:
                    csrf_token = new_csrf
                    print(f"New CSRF token: {csrf_token[:16]}...")
                
                print_cookies(session)
            except json.JSONDecodeError:
                print(f"❌ Invalid JSON response: {resp.text}")
                return False
        else:
            print(f"❌ Login failed: {resp.status_code}")
            print(f"Response: {resp.text}")
            return False
    except Exception as e:
        print(f"❌ Error during login: {e}")
        return False
    
    # Step 3: Immediate token verification
    print_separator("STEP 3: Immediate Token Verification")
    try:
        resp = session.get(f"{BASE_URL}/auth/verify-token")
        print(f"Verify token status: {resp.status_code}")
        print(f"Verify token headers: {dict(resp.headers)}")
        
        if resp.status_code == 200:
            data = resp.json()
            print(f"✅ Token verification successful")
            print(f"Valid: {data.get('valid', False)}")
            print(f"Response: {data}")
        else:
            print(f"❌ Token verification failed: {resp.status_code}")
            print(f"Response: {resp.text}")
            print_cookies(session)
            return False
    except Exception as e:
        print(f"❌ Error during token verification: {e}")
        return False
    
    # Step 4: Get user data
    print_separator("STEP 4: Get User Data")
    try:
        resp = session.get(f"{BASE_URL}/auth/user")
        print(f"User data status: {resp.status_code}")
        print(f"User data headers: {dict(resp.headers)}")
        
        if resp.status_code == 200:
            data = resp.json()
            print(f"✅ User data retrieval successful")
            print(f"Email: {data.get('email', 'N/A')}")
            print(f"Verified: {data.get('email_verified', False)}")
            print(f"Active: {data.get('active', False)}")
        else:
            print(f"❌ User data retrieval failed: {resp.status_code}")
            print(f"Response: {resp.text}")
            return False
    except Exception as e:
        print(f"❌ Error getting user data: {e}")
        return False
    
    # Step 5: Wait and test again (simulate frontend behavior)
    print_separator("STEP 5: Wait 5 seconds and test again")
    time.sleep(5)
    
    try:
        resp = session.get(f"{BASE_URL}/auth/verify-token")
        print(f"Second verify token status: {resp.status_code}")
        
        if resp.status_code == 200:
            data = resp.json()
            print(f"✅ Second token verification successful")
            print(f"Valid: {data.get('valid', False)}")
        else:
            print(f"❌ Second token verification failed: {resp.status_code}")
            print(f"Response: {resp.text}")
            print_cookies(session)
    except Exception as e:
        print(f"❌ Error during second verification: {e}")
    
    # Step 6: Test multiple rapid requests (simulate frontend)
    print_separator("STEP 6: Multiple Rapid Requests Test")
    for i in range(3):
        try:
            print(f"\nRequest {i+1}:")
            resp = session.get(f"{BASE_URL}/auth/verify-token")
            print(f"  Status: {resp.status_code}")
            if resp.status_code == 200:
                data = resp.json()
                print(f"  Valid: {data.get('valid', False)}")
            else:
                print(f"  Error: {resp.text}")
            time.sleep(1)
        except Exception as e:
            print(f"  Exception: {e}")
    
    # Step 7: Check cookies in detail
    print_separator("STEP 7: Final Cookie Analysis")
    print_cookies(session)
    
    # Check if cookies are HttpOnly by trying to access them
    print("\n📍 Cookie Analysis:")
    for cookie in session.cookies:
        print(f"Cookie: {cookie.name}")
        print(f"  Value length: {len(cookie.value)}")
        print(f"  Domain: {cookie.domain}")
        print(f"  Path: {cookie.path}")
        print(f"  Secure: {cookie.secure}")
        print(f"  Expires: {cookie.expires}")
        print(f"  HttpOnly: {cookie.has_nonstandard_attr('HttpOnly')}")
    
    return True

if __name__ == "__main__":
    test_comprehensive_auth_flow()
