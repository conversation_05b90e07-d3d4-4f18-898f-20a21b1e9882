import os
import sys
import json
from pymongo import MongoClient
from bson import ObjectId, json_util
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get MongoDB connection details from environment variables
DATABASE_URL = os.getenv("DATABASE_URL")
DATABASE_NAME = os.getenv("DATABASE_NAME", "sahayak")

def main():
    try:
        # Connect to MongoDB
        client = MongoClient(DATABASE_URL)
        db = client[DATABASE_NAME]

        # Check if we can connect
        print(f"Connected to database: {DATABASE_NAME}")

        # Count complaints
        complaint_count = db.complaints.count_documents({})
        print(f"Found {complaint_count} complaints in the database")

        # Get a sample complaint with CSV data
        sample_complaint = db.complaints.find_one(
            {"csv_data_base64": {"$exists": True}},
            {"_id": 1, "csv_data_base64": 1, "bank_notice_data": 1}
        )

        if sample_complaint:
            print(f"Sample complaint ID: {sample_complaint['_id']}")

            # Check CSV data
            has_csv = "csv_data_base64" in sample_complaint and bool(sample_complaint["csv_data_base64"])
            print(f"  Has CSV data: {has_csv}")
            if has_csv:
                csv_length = len(sample_complaint["csv_data_base64"])
                print(f"  CSV data length: {csv_length}")
                print(f"  CSV data sample: {sample_complaint['csv_data_base64'][:50]}...")

            # Check notice data
            has_notice = "bank_notice_data" in sample_complaint and bool(sample_complaint["bank_notice_data"])
            print(f"  Has notice data: {has_notice}")
            if has_notice:
                notice_type = type(sample_complaint["bank_notice_data"])
                print(f"  Notice data type: {notice_type}")

                if isinstance(sample_complaint["bank_notice_data"], list):
                    print(f"  Notice data length: {len(sample_complaint['bank_notice_data'])}")
                    if len(sample_complaint["bank_notice_data"]) > 0:
                        print(f"  First notice item type: {type(sample_complaint['bank_notice_data'][0])}")
                elif isinstance(sample_complaint["bank_notice_data"], dict):
                    print(f"  Notice data keys: {list(sample_complaint['bank_notice_data'].keys())}")
        else:
            print("No complaints with CSV data found")

            # Try to find any complaint
            any_complaint = db.complaints.find_one({}, {"_id": 1})
            if any_complaint:
                print(f"Found a complaint without CSV data: {any_complaint['_id']}")
            else:
                print("No complaints found at all")

    except Exception as e:
        print(f"Error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
