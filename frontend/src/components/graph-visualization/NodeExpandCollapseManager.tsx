import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { useReact<PERSON>low, Node, Edge } from 'reactflow';
import { calculateDynamicLayout } from './DynamicLayoutManager';

// Define types for our context
interface NodeExpandCollapseContextType {
  // Set of expanded node IDs
  expandedNodes: Set<string>;
  // Function to toggle a node's expanded state
  toggleNodeExpansion: (nodeId: string) => void;
  // Function to expand a node
  expandNode: (nodeId: string) => void;
  // Function to collapse a node
  collapseNode: (nodeId: string) => void;
  // Function to get all visible nodes based on expanded state
  getVisibleNodes: (allNodes: Node[]) => Node[];
  // Function to get all visible edges based on visible nodes
  getVisibleEdges: (allEdges: Edge[], visibleNodes: Node[]) => Edge[];
  // Function to initialize with all nodes collapsed except metadata, senders, and layer 1
  initializeExpandedState: (nodes: Node[]) => void;
  // Function to expand all nodes
  expandAllNodes: (nodes: Node[]) => void;
  // Function to collapse all nodes except metadata, senders, and layer 1
  collapseAllNodes: (nodes: Node[]) => void;
  // Function to check if a node is expanded
  isNodeExpanded: (nodeId: string) => boolean;
  // Function to update the layout direction
  setLayoutDirection: (isHorizontal: boolean) => void;
  // Current layout direction
  isHorizontalLayout: boolean;
}

// Create the context with default values
const NodeExpandCollapseContext = createContext<NodeExpandCollapseContextType>({
  expandedNodes: new Set<string>(),
  toggleNodeExpansion: () => {},
  expandNode: () => {},
  collapseNode: () => {},
  getVisibleNodes: () => [],
  getVisibleEdges: () => [],
  initializeExpandedState: () => {},
  expandAllNodes: () => {},
  collapseAllNodes: () => {},
  isNodeExpanded: () => false,
  setLayoutDirection: () => {},
  isHorizontalLayout: false,
});

// Provider component
export const NodeExpandCollapseProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // State to track expanded node IDs
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set<string>());
  // State to track layout direction
  const [isHorizontalLayout, setIsHorizontalLayout] = useState<boolean>(false);
  const { setNodes, setEdges, getNodes, getEdges } = useReactFlow();

  // Toggle a node's expanded state
  const toggleNodeExpansion = useCallback((nodeId: string) => {
    setExpandedNodes(prevExpandedNodes => {
      const newExpandedNodes = new Set(prevExpandedNodes);
      if (newExpandedNodes.has(nodeId)) {
        // When collapsing a node, we only remove that specific node from expanded set
        newExpandedNodes.delete(nodeId);
      } else {
        // When expanding a node, we just add that specific node to expanded set
        newExpandedNodes.add(nodeId);
      }
      return newExpandedNodes;
    });
  }, []);

  // Expand a node
  const expandNode = useCallback((nodeId: string) => {
    setExpandedNodes(prevExpandedNodes => {
      const newExpandedNodes = new Set(prevExpandedNodes);
      newExpandedNodes.add(nodeId);
      return newExpandedNodes;
    });
  }, []);

  // Collapse a node
  const collapseNode = useCallback((nodeId: string) => {
    setExpandedNodes(prevExpandedNodes => {
      const newExpandedNodes = new Set(prevExpandedNodes);
      newExpandedNodes.delete(nodeId);
      return newExpandedNodes;
    });
  }, []);

  // Check if a node is expanded
  const isNodeExpanded = useCallback((nodeId: string) => {
    return expandedNodes.has(nodeId);
  }, [expandedNodes]);

  // Get all child node IDs recursively
  const getAllChildNodeIds = useCallback((nodeId: string, nodes: Node[]): string[] => {
    const childNodes = nodes.filter(node => node.data?.parentId === nodeId);
    let childNodeIds = childNodes.map(node => node.id);

    // Recursively get children of children
    childNodes.forEach(childNode => {
      childNodeIds = [...childNodeIds, ...getAllChildNodeIds(childNode.id, nodes)];
    });

    return childNodeIds;
  }, []);

  // Get all visible nodes based on expanded state
  const getVisibleNodes = useCallback((allNodes: Node[]): Node[] => {
    // Always show metadata nodes
    const metadataNodes = allNodes.filter(node => node.type === 'metadata');
    const metadataNodeIds = new Set(metadataNodes.map(node => node.id));

    // Always show sender nodes (layer 0)
    const senderNodes = allNodes.filter(node => node.data?.layer === 0);
    const senderNodeIds = new Set(senderNodes.map(node => node.id));

    // Create a set of all visible node IDs starting with metadata and sender nodes
    const visibleNodeIds = new Set<string>([
      ...metadataNodeIds,
      ...senderNodeIds
    ]);

    // Function to check if a node should be visible based on its parent's expanded state
    const isNodeVisible = (node: Node): boolean => {
      // If it's a metadata or sender node, it's always visible
      if (node.type === 'metadata' || node.data?.layer === 0) {
        return true;
      }

      // If the node has a parent and that parent is expanded, the node is visible
      if (node.data?.parentId && expandedNodes.has(node.data.parentId)) {
        return true;
      }

      return false;
    };

    // Add all nodes that should be visible based on their parent's expanded state
    allNodes.forEach(node => {
      if (isNodeVisible(node)) {
        visibleNodeIds.add(node.id);
      }
    });

    // Return only visible nodes
    return allNodes.filter(node => visibleNodeIds.has(node.id));
  }, [expandedNodes]);

  // Get all visible edges based on visible nodes
  const getVisibleEdges = useCallback((allEdges: Edge[], visibleNodes: Node[]): Edge[] => {
    const visibleNodeIds = new Set(visibleNodes.map(node => node.id));

    // Only keep edges where both source and target nodes are visible
    return allEdges.filter(edge =>
      visibleNodeIds.has(edge.source) && visibleNodeIds.has(edge.target)
    );
  }, []);

  // Initialize expanded state with only sender nodes
  const initializeExpandedState = useCallback((nodes: Node[]) => {
    // Expand all sender nodes by default
    const senderNodeIds = nodes
      .filter(node => node.data?.layer === 0)
      .map(node => node.id);

    setExpandedNodes(new Set(senderNodeIds));
  }, []);

  // Expand all nodes
  const expandAllNodes = useCallback((nodes: Node[]) => {
    const allNodeIds = nodes.map(node => node.id);
    setExpandedNodes(new Set(allNodeIds));
  }, []);

  // Collapse all nodes except sender nodes
  const collapseAllNodes = useCallback((nodes: Node[]) => {
    // Only keep sender nodes expanded
    const senderNodeIds = nodes
      .filter(node => node.data?.layer === 0)
      .map(node => node.id);

    setExpandedNodes(new Set(senderNodeIds));
  }, []);

  // Function to set the layout direction
  const setLayoutDirection = useCallback((isHorizontal: boolean) => {
    setIsHorizontalLayout(isHorizontal);
  }, []);

  // Update visible nodes and edges when expanded state changes
  useEffect(() => {
    const allNodes = getNodes();
    const allEdges = getEdges();

    const visibleNodes = getVisibleNodes(allNodes);
    const visibleEdges = getVisibleEdges(allEdges, visibleNodes);

    // First, update node visibility
    const nodesWithVisibility = allNodes.map(node => ({
      ...node,
      hidden: !visibleNodes.some(visNode => visNode.id === node.id)
    }));

    // Update edge visibility
    const edgesWithVisibility = allEdges.map(edge => ({
      ...edge,
      hidden: !visibleEdges.some(visEdge => visEdge.id === edge.id)
    }));

    // Apply visibility changes first
    setNodes(nodesWithVisibility);
    setEdges(edgesWithVisibility);

    // Then recalculate layout with a slight delay to ensure visibility changes are applied
    setTimeout(() => {
      // Recalculate layout based on visible nodes
      const nodesWithUpdatedLayout = calculateDynamicLayout(
        nodesWithVisibility,
        edgesWithVisibility,
        {
          isHorizontalLayout,
          animate: true
        }
      );

      // Apply the updated layout while preserving the original node data and style
      setNodes(prevNodes => {
        return prevNodes.map(prevNode => {
          // Find the corresponding node with updated layout
          const updatedNode = nodesWithUpdatedLayout.find(n => n.id === prevNode.id);
          if (!updatedNode) return prevNode;

          // Preserve the original node data and style, only update position and hidden status
          return {
            ...prevNode,
            position: updatedNode.position,
            hidden: updatedNode.hidden,
            // Ensure the style includes transition for smooth animation
            style: {
              ...prevNode.style,
              transition: 'all 0.5s cubic-bezier(0.25, 0.1, 0.25, 1.0)'
            }
          };
        });
      });
    }, 50); // Use a shorter delay for more responsive updates
  }, [expandedNodes, isHorizontalLayout, getNodes, getEdges, getVisibleNodes, getVisibleEdges, setNodes, setEdges]);

  // Context value
  const contextValue: NodeExpandCollapseContextType = {
    expandedNodes,
    toggleNodeExpansion,
    expandNode,
    collapseNode,
    getVisibleNodes,
    getVisibleEdges,
    initializeExpandedState,
    expandAllNodes,
    collapseAllNodes,
    isNodeExpanded,
    setLayoutDirection,
    isHorizontalLayout,
  };

  return (
    <NodeExpandCollapseContext.Provider value={contextValue}>
      {children}
    </NodeExpandCollapseContext.Provider>
  );
};

// Custom hook to use the context
export const useNodeExpandCollapse = () => useContext(NodeExpandCollapseContext);

// Export a component that adds expand/collapse buttons to nodes
export const ExpandCollapseButton: React.FC<{
  nodeId: string;
  hasChildren: boolean;
  className?: string;
}> = ({ nodeId, hasChildren, className = '' }) => {
  const { isNodeExpanded, toggleNodeExpansion } = useNodeExpandCollapse();
  const expanded = isNodeExpanded(nodeId);

  if (!hasChildren) return null;

  return (
    <button
      onClick={(e) => {
        e.stopPropagation();
        toggleNodeExpansion(nodeId);
      }}
      className={`p-1 rounded transition-all duration-200 ${className}`}
      title={expanded ? "Collapse child nodes" : "Expand child nodes"}
    >
      {expanded ? (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      ) : (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
      )}
    </button>
  );
};
