<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Modern Shield Background -->
  <path d="M100 10L30 40V100C30 146.5 59.5 180 100 190C140.5 180 170 146.5 170 100V40L100 10Z" fill="url(#shield-gradient)" />
  
  <!-- Inner Shield with Subtle Gradient -->
  <path d="M100 30L50 52V100C50 135 70 160 100 170C130 160 150 135 150 100V52L100 30Z" fill="url(#inner-shield-gradient)" opacity="0.7" />
  
  <!-- S Letter - More Modern and Clean -->
  <path d="M85 70C85 63 92 58 100 58C108 58 115 63 115 70C115 77 108 82 100 82C92 82 85 87 85 94C85 101 92 106 100 106C108 106 115 101 115 94" 
        stroke="white" stroke-width="8" stroke-linecap="round" />
  
  <!-- Digital Circuit Lines -->
  <path d="M70 115H130" stroke="white" stroke-width="4" stroke-linecap="round" />
  <path d="M80 125H120" stroke="white" stroke-width="4" stroke-linecap="round" />
  <path d="M90 135H110" stroke="white" stroke-width="4" stroke-linecap="round" />
  <path d="M95 145H105" stroke="white" stroke-width="4" stroke-linecap="round" />
  
  <!-- Subtle Glow Effect -->
  <path d="M100 10L30 40V100C30 146.5 59.5 180 100 190C140.5 180 170 146.5 170 100V40L100 10Z" 
        stroke="url(#glow-gradient)" stroke-width="1.5" />
  
  <!-- Gradients -->
  <defs>
    <linearGradient id="shield-gradient" x1="30" y1="10" x2="170" y2="190" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0070F3" />
      <stop offset="1" stop-color="#1E40AF" />
    </linearGradient>
    
    <linearGradient id="inner-shield-gradient" x1="50" y1="30" x2="150" y2="170" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#1E40AF" />
      <stop offset="1" stop-color="#0070F3" />
    </linearGradient>
    
    <linearGradient id="glow-gradient" x1="30" y1="10" x2="170" y2="190" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFFFFF" stop-opacity="0.8" />
      <stop offset="1" stop-color="#FFFFFF" stop-opacity="0.2" />
    </linearGradient>
  </defs>
</svg>
