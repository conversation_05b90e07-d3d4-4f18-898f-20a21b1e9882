import React from 'react';
import { useThemeContext } from '../../context/TailwindThemeContext';
import { twMerge } from 'tailwind-merge';
import { FiCheck } from 'react-icons/fi';

interface ThemeColorSelectorProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showLabels?: boolean;
}

const ThemeColorSelector: React.FC<ThemeColorSelectorProps> = ({
  className,
  size = 'md',
  showLabels = true,
}) => {
  const { themeColor, setThemeColor } = useThemeContext();

  // Size styles
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-10 h-10',
  };

  // Theme color options - reduced to 6 colors as requested
  const themeColors = [
    // Keeping black (darkmode) and green (mint) as requested
    { id: 'darkmode', name: 'Dark Mode', color: '#2d2d3a', bgColor: '#1a1a2e', textColor: '#ffffff' },
    { id: 'mint', name: 'Mint Green', color: '#d8efe0', bgColor: '#a8d9bc', textColor: '#333333' },

    // Four additional colors for a total of 6
    { id: 'lightblue', name: 'Light Blue', color: '#d6e8f5', bgColor: '#a8cce6', textColor: '#333333' },
    { id: 'lightgray', name: 'Light Gray', color: '#e6e6e6', bgColor: '#d9d9d9', textColor: '#333333' },
    { id: 'lightpurple', name: 'Light Purple', color: '#e6d8f0', bgColor: '#d4a8e6', textColor: '#333333' },
    { id: 'lightpeach', name: 'Light Peach', color: '#f5e6d8', bgColor: '#e6c2a8', textColor: '#333333' },
  ];

  return (
    <div className={twMerge('flex flex-col space-y-2', className)}>
      {showLabels && (
        <div className="text-sm font-medium mb-1" style={{ color: 'var(--theme-text)' }}>
          Theme Color
        </div>
      )}
      <div className="flex flex-wrap gap-2">
        {themeColors.map((color) => (
          <button
            key={color.id}
            onClick={() => setThemeColor(color.id as any)}
            className={twMerge(
              'rounded-full transition-all duration-200 flex items-center justify-center',
              'border-2 hover:scale-110 relative',
              sizeClasses[size],
              themeColor === color.id ? 'ring-2 ring-offset-2 ring-opacity-50' : ''
            )}
            style={{
              backgroundColor: color.bgColor,
              borderColor: color.color,
              boxShadow: themeColor === color.id ? `0 0 0 2px ${color.color}` : 'none',
            }}
            title={color.name}
            aria-label={`Set theme color to ${color.name}`}
          >
            {themeColor === color.id && (
              <FiCheck
                className="text-gray-700"
                style={{ color: color.textColor }}
                size={size === 'sm' ? 12 : size === 'md' ? 16 : 20}
              />
            )}
          </button>
        ))}
      </div>
    </div>
  );
};

export default ThemeColorSelector;
