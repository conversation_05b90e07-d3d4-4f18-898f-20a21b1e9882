import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import PaidAccessRequired from './PaidAccessRequired';
import { useAlert } from '../context/TailwindAlertContext';
import SessionConflictDialog from './SessionConflictDialog';
import logger from '../utils/logger';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requirePaid?: boolean;
}

/**
 * A component that protects routes requiring authentication
 * Redirects to login page if user is not authenticated
 * Shows paid access required message if route requires paid access
 *
 * @param children The components to render if authenticated
 * @param requirePaid Whether this route requires paid access
 */
const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requirePaid = false
}) => {
  const { isAuthenticated, loading, user, logout, refreshUserData } = useAuth();
  const navigate = useNavigate();
  const { showError } = useAlert();
  const [isChecking, setIsChecking] = useState(true);
  const [showSessionConflict, setShowSessionConflict] = useState(false);
  const [activeSessionInfo, setActiveSessionInfo] = useState<{id: string, lastActive?: string} | undefined>();

  useEffect(() => {
    const checkAuth = async () => {
      try {
        setIsChecking(true);

        if (!loading) {
          logger.debug('ProtectedRoute: Authentication check started');
          logger.debug('ProtectedRoute: isAuthenticated =', isAuthenticated);
          logger.debug('ProtectedRoute: user =', user);
          logger.debug('ProtectedRoute: requirePaid =', requirePaid);
          logger.debug('ProtectedRoute: user.paid =', user?.paid);

          // First check if user is authenticated
          if (!isAuthenticated) {
            logger.warn('Not authenticated, redirecting to login');
            try {
              await logout();
              navigate('/login', { replace: true });
              return;
            } catch (error) {
              logger.error('Error during logout in ProtectedRoute:', error);
              navigate('/login', { replace: true });
              return;
            }
          }

          // If we need to refresh user data
          if (!user) {
            logger.debug('User data not available, refreshing');
            try {
              const userData = await refreshUserData();

              if (!userData) {
                logger.warn('Failed to get user data, redirecting to login');
                await logout();
                navigate('/login', { replace: true });
                return;
              }
            } catch (error: any) {
              // Check if this is a session conflict error
              if (error.response?.status === 409 && error.response?.data?.code === 'SESSION_CONFLICT') {
                logger.warn('Session conflict detected during auth check');

                // Show session conflict dialog
                setActiveSessionInfo({
                  id: error.response.data.active_session.id,
                  lastActive: error.response.data.active_session.last_active
                });
                setShowSessionConflict(true);
                setIsChecking(false);

                // Don't proceed with other checks until conflict is resolved
                return;
              } else {
                // For other errors, redirect to login
                logger.error('Error refreshing user data:', error);
                showError('Authentication error. Please try logging in again.');
                navigate('/login', { replace: true });
                return;
              }
            }
          }

          // Then check if email is verified
          if (user && !user.email_verified) {
            logger.warn('Email not verified, redirecting to resend verification');
            navigate('/resend-verification', { replace: true });
            return;
          }

          // Finally check if paid access is required
          if (requirePaid) {
            // Ensure user and paid status are properly defined
            if (!user || typeof user.paid !== 'boolean') {
              logger.warn('User or paid status not properly defined, refreshing user data');
              await refreshUserData();
              return;
            }

            if (!user.paid) {
              logger.warn('Paid access required but user is not paid');
              showError('This feature requires a paid subscription');
              return;
            }
          }

          // If we get here, all checks passed
          logger.debug('ProtectedRoute: All authentication checks passed');
        }
      } catch (error) {
        logger.error('Error in ProtectedRoute auth check:', error);
        showError('Authentication error. Please try logging in again.');
        navigate('/login', { replace: true });
      } finally {
        setIsChecking(false);
      }
    };

    checkAuth();
  }, [isAuthenticated, loading, navigate, user, logout, requirePaid, showError, refreshUserData]);

  // Show loading state while checking authentication
  if (loading || isChecking) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // Show paid access required message if needed
  if (requirePaid && user && !user.paid) {
    return <PaidAccessRequired />;
  }

  // Handle session conflict dialog close
  const handleSessionConflictClose = () => {
    setShowSessionConflict(false);
    // Refresh the page to retry authentication
    window.location.reload();
  };

  // If all checks pass, render the children with session conflict dialog if needed
  return (
    <>
      {children}
      {showSessionConflict && (
        <SessionConflictDialog
          isOpen={showSessionConflict}
          onClose={handleSessionConflictClose}
          activeSessionInfo={activeSessionInfo}
        />
      )}
    </>
  );
};

export default ProtectedRoute;
