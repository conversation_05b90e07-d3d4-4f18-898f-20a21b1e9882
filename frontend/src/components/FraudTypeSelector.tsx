import React from 'react';

export interface FraudType {
  id: string;
  name: string;
  description: string;
  icon: string;
  extractionType: string; // Added extraction type
}

interface FraudTypeSelectorProps {
  selectedType: string | null;
  onTypeSelect: (type: string | null, extractionType: string | null) => void;
}

export const fraudTypes: FraudType[] = [
  {
    id: 'online_banking',
    name: 'Online/UPI/Net Banking Fraud',
    description: 'For UPI, IMPS, NEFT, or online banking frauds',
    icon: '💸',
    extractionType: 'banking_upi' // Always use the new banking_upi extraction type
  },
  {
    id: 'card',
    name: 'Credit/Debit Card Fraud',
    description: 'Unauthorized transactions using credit or debit cards',
    icon: '💳',
    extractionType: 'card' // Use the card extraction type
  }
];

const FraudTypeSelector: React.FC<FraudTypeSelectorProps> = ({ selectedType, onTypeSelect }) => {
  // Get the selected fraud type object
  const selectedFraudType = selectedType ? fraudTypes.find((type: FraudType) => type.id === selectedType) : null;

  return (
    <div className="mb-2 p-2 rounded-lg border" style={{
        backgroundColor: 'var(--theme-bg-primary)',
        borderColor: 'var(--theme-border)',
        color: 'var(--theme-text)'
      }}>
      <h2 className="text-sm font-medium mb-1" style={{ color: 'var(--theme-text)' }}>Select Fraud Type</h2>
      <p className="text-xs mb-2" style={{ color: 'var(--theme-text-secondary)' }}>
        Select the type of fraud for better data extraction.
      </p>

      <div className="flex flex-col gap-2">
        {fraudTypes.map((type) => (
          <button
            key={type.id}
            className={`w-full cursor-pointer transition-all flex items-center p-2 rounded-lg border`}
            style={{
              backgroundColor: selectedType === type.id ? 'var(--theme-button)' : 'var(--theme-bg-card)',
              borderColor: selectedType === type.id ? 'var(--theme-accent)' : 'var(--theme-border)',
              boxShadow: selectedType === type.id ? '0 0 8px var(--theme-glow)' : 'none',
              color: selectedType === type.id ? 'white' : 'var(--theme-text)'
            }}
            onClick={() => onTypeSelect(type.id, type.extractionType)}
          >
            <div className="text-xl mr-3 p-1.5 rounded-full" style={{
              backgroundColor: selectedType === type.id ? 'rgba(255, 255, 255, 0.2)' : 'var(--theme-bg-primary)',
              color: selectedType === type.id ? 'white' : 'var(--theme-text)'
            }}>{type.icon}</div>
            <div className="flex-1 text-left">
              <h3 className="font-medium text-sm" style={{
                color: selectedType === type.id ? 'white' : 'var(--theme-text)'
              }}>{type.name}</h3>
              <p className="text-xs" style={{
                color: selectedType === type.id ? 'rgba(255, 255, 255, 0.8)' : 'var(--theme-text-secondary)'
              }}>
                {type.description}
              </p>
            </div>
            {selectedType === type.id && (
              <div style={{ color: 'white' }} className="ml-1">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path></svg>
              </div>
            )}
          </button>
        ))}
      </div>

      {/* Display selected fraud type */}
      {selectedType && selectedFraudType && (
        <div className="mt-2 p-2 rounded border text-sm" style={{
          backgroundColor: 'var(--theme-bg-card)',
          borderColor: 'var(--theme-accent)',
          boxShadow: '0 0 8px var(--theme-glow)'
        }}>
          <div className="font-medium" style={{ color: 'var(--theme-accent)' }}>
            Selected: {selectedFraudType.name}
          </div>
        </div>
      )}

      {selectedType && (
        <div className="mt-4 flex justify-end">
          <button
            className="text-sm bg-transparent border border-gray-200 dark:border-gray-700 rounded-md px-3 py-1 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            style={{ color: 'var(--theme-text)' }}
            onClick={() => onTypeSelect(null, null)}
          >
            Clear Selection
          </button>
        </div>
      )}
    </div>
  );
};

export default FraudTypeSelector;
