import React from 'react';
import { EdgeProps, Position } from 'reactflow';
import { useThemeContext } from '../../context/TailwindThemeContext';

// Custom edge component with animation and markers
export const CustomEdge: React.FC<EdgeProps> = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  data,
  // Unused parameters but kept for type compatibility
  style = {}
}) => {
  const { isDark } = useThemeContext();
  const theme = {
    primaryColor: isDark ? '#6366f1' : '#3b82f6'
  };

  // Get path for a smoother, more direct connection
  const getSmoothPath = () => {
    // Calculate the vertical and horizontal distances
    const verticalDistance = Math.abs(targetY - sourceY);
    const horizontalDistance = Math.abs(targetX - sourceX);

    // Determine if we're using horizontal layout based on source/target positions
    const isHorizontalLayout =
      sourcePosition === Position.Right ||
      targetPosition === Position.Left ||
      data?.layoutDirection === 'horizontal';

    // Check if this is a parent-child connection (based on data or positions)
    const isParentChildConnection = data?.isParentChild ||
      (isHorizontalLayout
        ? (sourcePosition === Position.Right && targetPosition === Position.Left)
        : (sourcePosition === Position.Bottom && targetPosition === Position.Top));

    // For sender node connections, ensure direct path
    const isSenderConnection = data?.source?.includes('sender_') ||
      (isHorizontalLayout
        ? (sourcePosition === Position.Right && targetPosition === Position.Left && horizontalDistance > 500)
        : (sourcePosition === Position.Bottom && targetPosition === Position.Top && verticalDistance > 500));

    // For parent-child connections, create a more direct path
    if (isParentChildConnection || isSenderConnection) {
      if (isHorizontalLayout) {
        // Horizontal layout - create a horizontal path
        const offsetX = horizontalDistance * 0.15; // Small offset for a slight curve

        // If source and target are nearly aligned horizontally, use an even straighter path
        if (Math.abs(sourceY - targetY) < 50) {
          return `
            M${sourceX},${sourceY}
            C${sourceX + offsetX},${sourceY} ${targetX - offsetX},${targetY} ${targetX},${targetY}
          `.trim().replace(/\s+/g, ' ');
        } else {
          // For slightly offset nodes, create a path that moves horizontally first, then vertically
          const midX = sourceX + (targetX - sourceX) * 0.5;
          return `
            M${sourceX},${sourceY}
            C${midX},${sourceY} ${midX},${targetY} ${targetX},${targetY}
          `.trim().replace(/\s+/g, ' ');
        }
      } else {
        // Vertical layout - create a vertical path
        const offsetY = verticalDistance * 0.15; // Small offset for a slight curve

        // If source and target are nearly aligned vertically, use an even straighter path
        if (Math.abs(sourceX - targetX) < 50) {
          return `
            M${sourceX},${sourceY}
            C${sourceX},${sourceY + offsetY} ${targetX},${targetY - offsetY} ${targetX},${targetY}
          `.trim().replace(/\s+/g, ' ');
        } else {
          // For slightly offset nodes, create a path that moves vertically first, then horizontally
          const midY = sourceY + (targetY - sourceY) * 0.5;
          return `
            M${sourceX},${sourceY}
            C${sourceX},${midY} ${targetX},${midY} ${targetX},${targetY}
          `.trim().replace(/\s+/g, ' ');
        }
      }
    }

    // For other connections, determine if primarily vertical or horizontal
    const isVertical = isHorizontalLayout ? false : (verticalDistance > horizontalDistance);

    // Use different control points based on direction
    let path;

    if (isHorizontalLayout) {
      // Horizontal layout - primarily horizontal connections
      if (sourceX < targetX) {
        // Left to right connection
        const controlPointX1 = sourceX + horizontalDistance * 0.2;
        const controlPointX2 = targetX - horizontalDistance * 0.2;

        path = `
          M${sourceX},${sourceY}
          C${controlPointX1},${sourceY} ${controlPointX2},${targetY} ${targetX},${targetY}
        `;
      } else {
        // Right to left connection (less common)
        const controlPointX1 = sourceX - horizontalDistance * 0.2;
        const controlPointX2 = targetX + horizontalDistance * 0.2;

        path = `
          M${sourceX},${sourceY}
          C${controlPointX1},${sourceY} ${controlPointX2},${targetY} ${targetX},${targetY}
        `;
      }
    } else if (isVertical && sourceY < targetY) {
      // Vertical layout - downward connection
      const controlPointY1 = sourceY + verticalDistance * 0.2;
      const controlPointY2 = targetY - verticalDistance * 0.2;

      path = `
        M${sourceX},${sourceY}
        C${sourceX},${controlPointY1} ${targetX},${controlPointY2} ${targetX},${targetY}
      `;
    } else if (isVertical && sourceY > targetY) {
      // Vertical layout - upward connection
      const controlPointY1 = sourceY - verticalDistance * 0.2;
      const controlPointY2 = targetY + verticalDistance * 0.2;

      path = `
        M${sourceX},${sourceY}
        C${sourceX},${controlPointY1} ${targetX},${controlPointY2} ${targetX},${targetY}
      `;
    } else {
      // Horizontal connection in vertical layout - smoother S-curve
      // Adjust control points based on source and target positions
      const sourceOffset = sourcePosition === Position.Bottom ? 0.3 : 0.1;
      const targetOffset = targetPosition === Position.Top ? 0.3 : 0.1;

      path = `
        M${sourceX},${sourceY}
        C${sourceX + (targetX - sourceX) * sourceOffset},${sourceY}
         ${targetX - (targetX - sourceX) * targetOffset},${targetY}
         ${targetX},${targetY}
      `;
    }

    return path.trim().replace(/\s+/g, ' ');
  };

  const edgePath = getSmoothPath();

  return (
    <>
      <path
        id={id}
        className="react-flow__edge-path"
        d={edgePath}
        style={{
          ...style,
          strokeWidth: 2,
          stroke: theme.primaryColor,
          animation: 'flowDots 30s linear infinite' // Animation for flowing dots
        }}
        markerEnd="url(#edgeMarker)" // Add the marker for arrow/circle at end
      />
    </>
  );
};

// Export edge types for use in ReactFlow
export const edgeTypes = {
  default: CustomEdge
};
