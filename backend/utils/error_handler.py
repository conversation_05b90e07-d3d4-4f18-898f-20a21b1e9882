"""
Standardized error handling utilities.

This module provides functions for standardized error handling and response formatting
to ensure consistent error responses across the application.

This module extends the functionality in error_utils.py with additional features
while maintaining compatibility with the existing error handling system.
"""
import logging
import traceback
from typing import Dict, Any, Optional
from fastapi import HTTPException, status
from fastapi.responses import JSONResponse
from pydantic import ValidationError
from utils.error_utils import create_error_response

# Setup logging
logger = logging.getLogger(__name__)

# Error type constants
VALIDATION_ERROR = "validation_error"
AUTHENTICATION_ERROR = "authentication_error"
AUTHORIZATION_ERROR = "authorization_error"
NOT_FOUND_ERROR = "not_found_error"
CONFLICT_ERROR = "conflict_error"
RATE_LIMIT_ERROR = "rate_limit_error"
SERVER_ERROR = "server_error"
INPUT_ERROR = "input_error"
DATABASE_ERROR = "database_error"
EXTERNAL_SERVICE_ERROR = "external_service_error"

class StandardError(HTTPException):
    """
    Standard error class that extends HTTPException with additional fields.

    This class provides a standardized way to raise HTTP exceptions with
    additional metadata for better error handling and logging.
    """

    def __init__(
        self,
        status_code: int,
        detail: str,
        error_type: str = "general_error",
        error_code: Optional[str] = None,
        field_errors: Optional[Dict[str, str]] = None,
        headers: Optional[Dict[str, str]] = None,
        log_error: bool = True,
        log_level: str = "error",
        exception: Optional[Exception] = None
    ):
        """
        Initialize a StandardError.

        Args:
            status_code: HTTP status code
            detail: Human-readable error message
            error_type: Type of error (e.g., validation_error, auth_error)
            error_code: Optional error code for client reference
            field_errors: Optional field-specific error messages
            headers: Optional HTTP headers to include in the response
            log_error: Whether to log the error
            log_level: Logging level (debug, info, warning, error, critical)
            exception: Original exception that caused this error
        """
        # Create a structured error detail using the format from error_utils
        error_response = {
            "success": False,
            "error": {
                "message": detail,
                "error_type": error_type,
                "status_code": status_code
            }
        }

        if error_code:
            error_response["error"]["error_code"] = error_code

        if field_errors:
            error_response["error"]["field_errors"] = field_errors

        # Initialize the parent HTTPException
        super().__init__(status_code=status_code, detail=error_response, headers=headers)

        # Store additional metadata
        self.error_type = error_type
        self.error_code = error_code
        self.field_errors = field_errors
        self.log_error = log_error
        self.log_level = log_level
        self.original_exception = exception
        self.error_message = detail  # Store the message separately for logging

        # Log the error if requested
        if log_error:
            self._log_error()

    def _log_error(self):
        """Log the error with appropriate level and details."""
        log_message = f"{self.error_type}: {self.error_message}"

        if self.field_errors:
            log_message += f" Field errors: {self.field_errors}"

        if self.original_exception:
            log_message += f" Original exception: {str(self.original_exception)}"

        # Get the appropriate logger method
        log_method = getattr(logger, self.log_level, logger.error)

        # Log with stack trace for server errors
        if self.status_code >= 500 and self.original_exception:
            log_method(log_message + "\n" + traceback.format_exc())
        else:
            log_method(log_message)

def validation_error(
    detail: str = "Validation error",
    field_errors: Optional[Dict[str, str]] = None,
    headers: Optional[Dict[str, str]] = None,
    log_error: bool = True
) -> HTTPException:
    """
    Create a validation error.

    Args:
        detail: Human-readable error message
        field_errors: Field-specific error messages
        headers: Optional HTTP headers
        log_error: Whether to log the error

    Returns:
        HTTPException: A validation error exception
    """
    # Convert field_errors to details format expected by error_utils
    details = {}
    if field_errors:
        details["field_errors"] = field_errors

    # Create error response
    error_response = create_error_response(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        message=detail,
        error_type=VALIDATION_ERROR,
        details=details if details else None,
        log_error=log_error
    )

    # Return HTTPException
    return HTTPException(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        detail=error_response,
        headers=headers
    )

def authentication_error(
    detail: str = "Authentication required",
    headers: Optional[Dict[str, str]] = None,
    log_error: bool = True
) -> HTTPException:
    """
    Create an authentication error.

    Args:
        detail: Human-readable error message
        headers: Optional HTTP headers
        log_error: Whether to log the error

    Returns:
        HTTPException: An authentication error exception
    """
    # Create error response
    error_response = create_error_response(
        status_code=status.HTTP_401_UNAUTHORIZED,
        message=detail,
        error_type=AUTHENTICATION_ERROR,
        log_error=log_error
    )

    # Return HTTPException
    return HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail=error_response,
        headers=headers or {"WWW-Authenticate": "Bearer"}
    )

def authorization_error(
    detail: str = "Permission denied",
    headers: Optional[Dict[str, str]] = None,
    log_error: bool = True
) -> HTTPException:
    """
    Create an authorization error.

    Args:
        detail: Human-readable error message
        headers: Optional HTTP headers
        log_error: Whether to log the error

    Returns:
        HTTPException: An authorization error exception
    """
    # Create error response
    error_response = create_error_response(
        status_code=status.HTTP_403_FORBIDDEN,
        message=detail,
        error_type=AUTHORIZATION_ERROR,
        log_error=log_error
    )

    # Return HTTPException
    return HTTPException(
        status_code=status.HTTP_403_FORBIDDEN,
        detail=error_response,
        headers=headers
    )

def not_found_error(
    detail: str = "Resource not found",
    resource_type: Optional[str] = None,
    resource_id: Optional[str] = None,
    headers: Optional[Dict[str, str]] = None,
    log_error: bool = True
) -> HTTPException:
    """
    Create a not found error.

    Args:
        detail: Human-readable error message
        resource_type: Optional type of resource that was not found
        resource_id: Optional ID of resource that was not found
        headers: Optional HTTP headers
        log_error: Whether to log the error

    Returns:
        HTTPException: A not found error exception
    """
    # Create details if resource info is provided
    details = {}
    if resource_type:
        details["resource_type"] = resource_type
    if resource_id:
        details["resource_id"] = resource_id

    # Create error response
    error_response = create_error_response(
        status_code=status.HTTP_404_NOT_FOUND,
        message=detail,
        error_type=NOT_FOUND_ERROR,
        details=details if details else None,
        log_error=log_error
    )

    # Return HTTPException
    return HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail=error_response,
        headers=headers
    )

def server_error(
    detail: str = "Internal server error",
    error_code: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None,
    headers: Optional[Dict[str, str]] = None,
    log_error: bool = True,
    exception: Optional[Exception] = None
) -> HTTPException:
    """
    Create a server error.

    Args:
        detail: Human-readable error message
        error_code: Optional error code for client reference
        details: Optional additional error details
        headers: Optional HTTP headers
        log_error: Whether to log the error
        exception: Original exception

    Returns:
        HTTPException: A server error exception
    """
    # Create error response
    error_response = create_error_response(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        message=detail,
        error_type=SERVER_ERROR,
        error_code=error_code,
        details=details,
        log_error=log_error
    )

    # Log the exception if provided
    if exception and log_error:
        logger.error(f"Server error: {detail}", exc_info=exception)

    # Return HTTPException
    return HTTPException(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        detail=error_response,
        headers=headers
    )

def handle_validation_error(exc: ValidationError) -> JSONResponse:
    """
    Handle Pydantic validation errors.

    Args:
        exc: Pydantic ValidationError

    Returns:
        JSONResponse: A standardized error response
    """
    field_errors = {}
    for error in exc.errors():
        field = ".".join(str(loc) for loc in error["loc"])
        field_errors[field] = error["msg"]

    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={
            "message": "Validation error",
            "type": VALIDATION_ERROR,
            "field_errors": field_errors
        }
    )
