import React, { useCallback, useState, useRef } from 'react';
import React<PERSON><PERSON>, {
  Background,
  BackgroundVariant,
  Controls,
  useNodesState,
  useEdgesState,
  Panel,
  ConnectionLineType,
  NodeChange,
  applyNodeChanges,
  ReactFlowProvider
} from 'reactflow';
import 'reactflow/dist/style.css';
import { useThemeContext } from '../../context/TailwindThemeContext';

// Import our modular components
import {
  nodeTypes,
  edgeTypes,
  processTransactionsForGraph
} from './index';
import GraphExporter from './GraphExporter';
import { NodeExpandCollapseProvider, useNodeExpandCollapse } from './NodeExpandCollapseManager';

interface TransactionGraphVisualizerProps {
  transactions: any[];
  metadata: any;
  onNodeClick?: (nodeId: string, data: any) => void;
}

/**
 * TransactionGraphVisualizer Component
 *
 * A reusable component that encapsulates the graph visualization logic
 * This component handles the rendering of the transaction graph using ReactFlow
 */
const TransactionGraphVisualizerInner: React.FC<TransactionGraphVisualizerProps> = ({
  transactions,
  metadata,
  onNodeClick
}) => {
  const [nodes, setNodes] = useNodesState([]);
  const [edges, setEdges] = useEdgesState([]);
  const reactFlowRef = useRef<HTMLDivElement | null>(null);
  const { isDark } = useThemeContext();

  // Get expand/collapse functionality and layout direction
  const {
    initializeExpandedState,
    expandAllNodes,
    collapseAllNodes,
    isHorizontalLayout,
    setLayoutDirection
  } = useNodeExpandCollapse();

  // Store the ReactFlow instance reference for export
  const [reactFlowInstance, setReactFlowInstance] = useState<any>(null);

  // Expose the ReactFlow instance to the window for canvas rendering
  React.useEffect(() => {
    if (reactFlowInstance) {
      (window as any).reactFlowInstance = reactFlowInstance;
    }

    return () => {
      // Clean up when component unmounts
      delete (window as any).reactFlowInstance;
    };
  }, [reactFlowInstance]);

  // Process transactions and set up the graph on mount or when layout changes
  React.useEffect(() => {
    if (!transactions || transactions.length === 0) {
      console.warn('No transactions provided to TransactionGraphVisualizer');
      return;
    }

    try {
      console.log(`Processing ${transactions.length} transactions for graph visualization`);
      console.log(`Using ${isHorizontalLayout ? 'horizontal' : 'vertical'} layout`);

      // Process transactions for graph using our improved processor
      const { nodes: graphNodes, edges: graphEdges } = processTransactionsForGraph(
        transactions,
        metadata,
        isHorizontalLayout
      );
      console.log(`Generated ${graphNodes.length} nodes and ${graphEdges.length} edges`);

      // Store the current theme context value in each node's data
      // This ensures the theme context is preserved during layout recalculation
      const nodesWithThemeContext = graphNodes.map(node => ({
        ...node,
        data: {
          ...node.data,
          // Store the theme context value
          themeContext: { isDark }
        }
      }));

      setNodes(nodesWithThemeContext);
      setEdges(graphEdges);

      // Initialize expanded state to show only metadata, sender nodes, and layer 1 nodes
      initializeExpandedState(nodesWithThemeContext);
    } catch (error) {
      console.error('Error processing graph data:', error);
    }
  }, [transactions, metadata, setNodes, setEdges, isHorizontalLayout, initializeExpandedState, isDark]);

  // Handle node changes (dragging, selection)
  const onNodesChange = useCallback((changes: NodeChange[]) => {
    const nonSelectionChanges = changes.filter(change =>
      change.type !== 'select' || (change.type === 'select' && change.selected !== undefined)
    );

    if (nonSelectionChanges.length > 0) {
      // First apply the changes to get the updated nodes
      setNodes((nds) => {
        const updatedNodes = applyNodeChanges(nonSelectionChanges, nds);

        // Check if any of the changes are position changes
        const positionChanges = nonSelectionChanges.filter(change =>
          change.type === 'position' && change.dragging === false
        );

        // If there are position changes and dragging has completed
        if (positionChanges.length > 0) {
          // For each moved node, update its child nodes
          const movedNodeIds = positionChanges.map(change => (change as any).id);

          // Create a map of parent nodes to their new positions
          const parentPositions = new Map();
          movedNodeIds.forEach(id => {
            const node = updatedNodes.find(n => n.id === id);
            if (node) {
              parentPositions.set(id, node.position);
            }
          });

          // Now update child nodes to follow their parents
          return updatedNodes.map(node => {
            // If this node has a parentId and that parent was moved
            if (node.data?.parentId && parentPositions.has(node.data.parentId)) {
              const parentPos = parentPositions.get(node.data.parentId);
              const parentNode = updatedNodes.find(n => n.id === node.data.parentId);

              // Calculate the relative position to maintain
              const offsetX = node.position.x - (parentNode?.position?.x || 0);
              const offsetY = node.position.y - (parentNode?.position?.y || 0);

              // Update the node's position to maintain the same relative position
              return {
                ...node,
                position: {
                  x: parentPos.x + offsetX,
                  y: parentPos.y + offsetY
                }
              };
            }
            return node;
          });
        }

        return updatedNodes;
      });
    }
  }, [setNodes]);

  // Handle node click
  const handleNodeClick = useCallback((_: React.MouseEvent, node: any) => {
    // Update selection state for the clicked node
    setNodes(nodes => nodes.map(n => ({
      ...n,
      selected: n.id === node.id
    })));

    // Call the onNodeClick callback if provided
    if (onNodeClick) {
      onNodeClick(node.id, node.data);
    }
  }, [setNodes, onNodeClick]);

  return (
    <div className="w-full h-full" ref={reactFlowRef}>
      {/* SVG Definitions for markers */}
      <svg style={{ position: 'absolute', width: 0, height: 0 }}>
        <defs>
          {/* Arrow marker for edges */}
          <marker
            id="edgeMarker"
            viewBox="0 0 12 12"
            refX="6"
            refY="6"
            markerWidth="8"
            markerHeight="8"
            orient="auto"
          >
            <path
              d="M 0 0 L 12 6 L 0 12 z"
              fill="#3b82f6"
            />
          </marker>
        </defs>
      </svg>

      <ReactFlow
        nodes={nodes}
        edges={edges}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        onNodesChange={onNodesChange}
        onNodeClick={handleNodeClick}
        onInit={setReactFlowInstance}
        fitView
        fitViewOptions={{
          padding: 1.0,
          includeHiddenNodes: true,
          minZoom: 0.3,
          maxZoom: 1.5
        }}
        defaultEdgeOptions={{
          type: 'default',
          style: {
            strokeWidth: 2,
            stroke: '#9CA3AF',
            strokeDasharray: 'none !important'
          }
        }}
        connectionLineType={ConnectionLineType.SmoothStep}
        proOptions={{ hideAttribution: true }}
        className={isDark ? "dark-flow" : "light-flow"}
        style={{
          backgroundColor: isDark ? '#10101e' : '#f5f5f5',
          width: '100%',
          height: '100%'
        }}
        selectNodesOnDrag={false}
        nodesDraggable={true}
        elementsSelectable={true}
        zoomOnScroll={true}
        panOnScroll={true}
        panOnDrag={true}
        minZoom={0.1}
        maxZoom={2}
        defaultViewport={{ x: 0, y: 0, zoom: 0.6 }}
      >
        <Background
          color={isDark ? "#444" : "#aaa"}
          gap={16}
          size={1}
          variant={BackgroundVariant.Dots}
        />
        <Controls className={isDark ? "dark-controls" : "light-controls"} showInteractive={true} />
        <Panel position="top-right" className={`${isDark ? 'bg-gray-800 text-white' : 'bg-white text-gray-800'} p-1 rounded shadow-md`}>
          <div className="flex gap-1">
            <button
              className={`p-0.5 ${isDark ? 'bg-gray-700 hover:bg-gray-600 text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-800'} rounded`}
              onClick={() => {
                const reactFlowWrapper = document.querySelector('.react-flow__renderer');
                if (reactFlowWrapper) {
                  if (reactFlowWrapper.requestFullscreen) {
                    reactFlowWrapper.requestFullscreen();
                  }
                }
              }}
              title="Fullscreen"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l5-5m11 5v-4m0 4h-4m4 0l-5-5M4 16v4m0 0h4m-4 0l5-5m11 5v-4m0 4h-4m4 0l-5-5" />
              </svg>
            </button>
            <button
              className={`p-0.5 ${isHorizontalLayout
                ? (isDark ? 'bg-blue-800 hover:bg-blue-700 text-white' : 'bg-blue-100 hover:bg-blue-200 text-blue-800')
                : (isDark ? 'bg-gray-700 hover:bg-gray-600 text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-800')
              } rounded`}
              onClick={() => setLayoutDirection(!isHorizontalLayout)}
              title={isHorizontalLayout ? "Switch to vertical layout" : "Switch to horizontal layout"}
            >
              {isHorizontalLayout ? (
                <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7l4-4m0 0l4 4m-4-4v18" />
                </svg>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16l4 4m0 0l4-4m-4 4V4" />
                </svg>
              )}
            </button>
            {/* Expand All Nodes button */}
            <button
              className={`p-0.5 ${isDark ? 'bg-gray-700 hover:bg-gray-600 text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-800'} rounded`}
              onClick={() => expandAllNodes(nodes)}
              title="Expand all nodes"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8v8m0 0h16m0 0V8m0 8L4 8" />
              </svg>
            </button>
            {/* Collapse All Nodes button */}
            <button
              className={`p-0.5 ${isDark ? 'bg-gray-700 hover:bg-gray-600 text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-800'} rounded`}
              onClick={() => collapseAllNodes(nodes)}
              title="Collapse to initial view"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v-8m0 0h16m0 0v8m0-8L4 16" />
              </svg>
            </button>
            {/* Add GraphExporter component */}
            <GraphExporter
              reactFlowRef={reactFlowRef}
              isHorizontalLayout={isHorizontalLayout}
              reactFlowInstance={reactFlowInstance}
            />
          </div>
        </Panel>
      </ReactFlow>
    </div>
  );
};

// Wrap the component with ReactFlowProvider and NodeExpandCollapseProvider
const TransactionGraphVisualizer: React.FC<TransactionGraphVisualizerProps> = (props) => {
  return (
    <ReactFlowProvider>
      <NodeExpandCollapseProvider>
        <TransactionGraphVisualizerInner {...props} />
      </NodeExpandCollapseProvider>
    </ReactFlowProvider>
  );
};

export default TransactionGraphVisualizer;
