"""
Standardized error handling utilities for the backend.

This module provides consistent error handling functions and classes
to ensure uniform error responses across the API.
"""
from fastapi import HTT<PERSON>Ex<PERSON>, status
import logging
from typing import Dict, Any, Optional, Union, List

# Setup logging
logger = logging.getLogger(__name__)

class ErrorDetail:
    """
    Error detail class for standardized error responses.
    """
    def __init__(
        self,
        message: str,
        error_type: str = "general_error",
        error_code: Optional[str] = None,
        field: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize error detail.

        Args:
            message: Human-readable error message
            error_type: Type of error (e.g., validation_error, auth_error)
            error_code: Optional error code for client reference
            field: Optional field name for validation errors
            details: Optional additional error details
        """
        self.message = message
        self.error_type = error_type
        self.error_code = error_code
        self.field = field
        self.details = details or {}

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert error detail to dictionary.

        Returns:
            Dictionary representation of error detail
        """
        result = {
            "message": self.message,
            "error_type": self.error_type
        }

        if self.error_code:
            result["error_code"] = self.error_code

        if self.field:
            result["field"] = self.field

        if self.details:
            result["details"] = self.details

        return result


def create_error_response(
    status_code: int,
    message: str,
    error_type: str = "general_error",
    error_code: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None,
    log_error: bool = True,
    log_level: str = "error"
) -> Dict[str, Any]:
    """
    Create a standardized error response.

    Args:
        status_code: HTTP status code
        message: Human-readable error message
        error_type: Type of error (e.g., validation_error, auth_error)
        error_code: Optional error code for client reference
        details: Optional additional error details
        log_error: Whether to log the error
        log_level: Logging level (debug, info, warning, error, critical)

    Returns:
        Dictionary with standardized error response format
    """
    if log_error:
        log_fn = getattr(logger, log_level)
        log_fn(f"Error ({status_code}, {error_type}): {message}")

    response = {
        "success": False,
        "error": {
            "message": message,
            "error_type": error_type,
            "status_code": status_code
        }
    }

    if error_code:
        response["error"]["error_code"] = error_code

    if details:
        response["error"]["details"] = details

    return response


def raise_http_exception(
    status_code: int,
    message: str,
    error_type: str = "general_error",
    error_code: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None,
    log_error: bool = True,
    log_level: str = "error",
    headers: Optional[Dict[str, str]] = None
) -> None:
    """
    Raise an HTTPException with standardized error response.

    Args:
        status_code: HTTP status code
        message: Human-readable error message
        error_type: Type of error (e.g., validation_error, auth_error)
        error_code: Optional error code for client reference
        details: Optional additional error details
        log_error: Whether to log the error
        log_level: Logging level (debug, info, warning, error, critical)
        headers: Optional HTTP headers to include in the response

    Raises:
        HTTPException: FastAPI HTTP exception with standardized error response
    """
    error_response = create_error_response(
        status_code=status_code,
        message=message,
        error_type=error_type,
        error_code=error_code,
        details=details,
        log_error=log_error,
        log_level=log_level
    )

    raise HTTPException(
        status_code=status_code,
        detail=error_response,
        headers=headers
    )


# Common error functions for frequently used error types
def validation_error(
    message: str,
    field: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None,
    log_error: bool = True
) -> None:
    """
    Raise a validation error.

    Args:
        message: Human-readable error message
        field: Optional field name that failed validation
        details: Optional additional error details
        log_error: Whether to log the error

    Raises:
        HTTPException: FastAPI HTTP exception with validation error response
    """
    error_details = details or {}
    if field:
        error_details["field"] = field

    raise_http_exception(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        message=message,
        error_type="validation_error",
        details=error_details,
        log_error=log_error,
        log_level="warning"
    )


def authentication_error(
    message: str = "Authentication required",
    error_code: Optional[str] = None,
    log_error: bool = True
) -> None:
    """
    Raise an authentication error.

    Args:
        message: Human-readable error message
        error_code: Optional error code for client reference
        log_error: Whether to log the error

    Raises:
        HTTPException: FastAPI HTTP exception with authentication error response
    """
    raise_http_exception(
        status_code=status.HTTP_401_UNAUTHORIZED,
        message=message,
        error_type="authentication_error",
        error_code=error_code,
        log_error=log_error,
        log_level="warning",
        headers={"WWW-Authenticate": "Bearer"}
    )


def authorization_error(
    message: str = "You don't have permission to perform this action",
    error_code: Optional[str] = None,
    log_error: bool = True
) -> None:
    """
    Raise an authorization error.

    Args:
        message: Human-readable error message
        error_code: Optional error code for client reference
        log_error: Whether to log the error

    Raises:
        HTTPException: FastAPI HTTP exception with authorization error response
    """
    raise_http_exception(
        status_code=status.HTTP_403_FORBIDDEN,
        message=message,
        error_type="authorization_error",
        error_code=error_code,
        log_error=log_error,
        log_level="warning"
    )


def not_found_error(
    message: str = "Resource not found",
    resource_type: Optional[str] = None,
    resource_id: Optional[str] = None,
    log_error: bool = True
) -> None:
    """
    Raise a not found error.

    Args:
        message: Human-readable error message
        resource_type: Optional type of resource that was not found
        resource_id: Optional ID of resource that was not found
        log_error: Whether to log the error

    Raises:
        HTTPException: FastAPI HTTP exception with not found error response
    """
    details = {}
    if resource_type:
        details["resource_type"] = resource_type
    if resource_id:
        details["resource_id"] = resource_id

    raise_http_exception(
        status_code=status.HTTP_404_NOT_FOUND,
        message=message,
        error_type="not_found_error",
        details=details if details else None,
        log_error=log_error,
        log_level="info"
    )


def server_error(
    message: str = "An unexpected error occurred",
    error_code: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None,
    log_error: bool = True
) -> None:
    """
    Raise a server error.

    Args:
        message: Human-readable error message
        error_code: Optional error code for client reference
        details: Optional additional error details
        log_error: Whether to log the error

    Raises:
        HTTPException: FastAPI HTTP exception with server error response
    """
    raise_http_exception(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        message=message,
        error_type="server_error",
        error_code=error_code,
        details=details,
        log_error=log_error,
        log_level="error"
    )
