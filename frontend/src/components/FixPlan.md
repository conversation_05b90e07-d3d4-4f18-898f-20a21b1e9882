# Plan to Fix ReactTableCSVPreview Component

## Issues Identified

1. **Data Persistence Issue**: When updating/deleting rows in the ReactTableCSVPreview, the changes don't persist because:
   - The component is being re-rendered with the old data due to the `key={csvKey}` forcing a re-render
   - The `refetch()` call is causing a full page refresh instead of just updating the table data

2. **Unnecessary Page Refreshes**: The entire page is refreshing when data is fetched because:
   - The `refetch()` function in `useComplaintData` is causing a full data reload
   - Multiple cache clearing operations are happening which trigger re-renders

## Detailed Fix Plan

### 1. Fix the ComplaintDetail Component

- [x] Remove the `csvKey` state and its usage to prevent forced re-renders
- [x] Modify the `handleCSVEdit` function to:
  - [x] Not update local state for row operations (let the component handle its own state)
  - [x] Not force re-renders for row operations
  - [x] Remove unnecessary cache clearing and refetches for row operations

### 2. Fix the ReactTableCSVPreview Component

- [ ] Fix TypeScript errors in the component
- [ ] Implement proper optimistic UI updates for edits and deletes:
  - [ ] Maintain internal state for rows and headers
  - [ ] Update internal state immediately when edits/deletes happen
  - [ ] Only revert to original state if the server operation fails
- [ ] Implement proper error handling for failed operations
- [ ] Remove unnecessary re-renders and state updates

### 3. Testing Plan

- [ ] Test row editing functionality:
  - [ ] Verify changes persist after edit
  - [ ] Verify no page refresh occurs
  - [ ] Verify UI remains consistent
- [ ] Test row deletion functionality:
  - [ ] Verify row is removed from the table
  - [ ] Verify no page refresh occurs
  - [ ] Verify UI remains consistent
- [ ] Test error handling:
  - [ ] Verify proper error messages are shown
  - [ ] Verify UI reverts to original state on error

## Implementation Approach

1. Create a simplified version of the ReactTableCSVPreview component that focuses on core functionality
2. Implement proper state management with optimistic updates
3. Add proper error handling
4. Test thoroughly to ensure all issues are fixed
