import logging
import json
import os
import re
import time
import traceback
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Union

# Configure logging format
DEFAULT_LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

# Fields that should be redacted in logs
SENSITIVE_FIELDS = [
    'password', 'hashed_password', 'token', 'secret', 'key', 'auth',
    'authorization', 'api_key', 'apikey', 'access_token', 'refresh_token',
    'jwt', 'session', 'cookie', 'credential', 'otp', 'verification_code',
    'credit_card', 'card_number', 'cvv', 'ssn', 'social_security',
    'account_number', 'routing_number', 'email', 'phone', 'address'
]

# Regular expressions for redacting sensitive data
SENSITIVE_PATTERNS = [
    # Credit card numbers
    r'\b(?:\d{4}[ -]?){3}\d{4}\b',
    # Email addresses
    r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
    # Phone numbers
    r'\b(?:\+\d{1,2}\s)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}\b',
    # Social Security Numbers
    r'\b\d{3}-\d{2}-\d{4}\b',
    # API keys and tokens (common formats)
    r'\b[A-Za-z0-9_-]{32,}\b',
    r'\b[A-Za-z0-9_-]{24}\.[A-Za-z0-9_-]{6}\.[A-Za-z0-9_-]{27}\b',  # JWT format
    r'\b[A-Za-z0-9]{40}\b',  # GitHub token format
]

class StructuredLogger:
    """
    Enhanced structured logger with sensitive data redaction for production use.
    
    This logger formats log messages as JSON and redacts sensitive information
    to prevent data leakage in logs.
    """
    
    def __init__(self, name: str, level: int = logging.INFO):
        """
        Initialize the structured logger.
        
        Args:
            name: Logger name
            level: Logging level
        """
        self.logger = logging.getLogger(name)
        self.logger.setLevel(level)
        self.environment = os.getenv("ENVIRONMENT", "development")
        
    def _redact_sensitive_data(self, data: Any) -> Any:
        """
        Redact sensitive data from logs.
        
        Args:
            data: Data to redact
            
        Returns:
            Redacted data
        """
        if isinstance(data, dict):
            redacted_data = {}
            for key, value in data.items():
                # Check if the key contains sensitive information
                if any(sensitive in key.lower() for sensitive in SENSITIVE_FIELDS):
                    redacted_data[key] = "[REDACTED]"
                else:
                    redacted_data[key] = self._redact_sensitive_data(value)
            return redacted_data
        elif isinstance(data, list):
            return [self._redact_sensitive_data(item) for item in data]
        elif isinstance(data, str):
            # Redact sensitive patterns in strings
            redacted_value = data
            for pattern in SENSITIVE_PATTERNS:
                redacted_value = re.sub(pattern, "[REDACTED]", redacted_value)
            return redacted_value
        else:
            return data
            
    def _format_log_entry(self, level: str, message: str, extra: Optional[Dict[str, Any]] = None) -> str:
        """
        Format a log entry as JSON with redacted sensitive data.
        
        Args:
            level: Log level
            message: Log message
            extra: Additional data to include in the log
            
        Returns:
            JSON formatted log entry
        """
        timestamp = datetime.now(timezone.utc).isoformat()
        
        log_entry = {
            "timestamp": timestamp,
            "level": level,
            "message": message,
            "environment": self.environment,
        }
        
        if extra:
            # Redact sensitive data before logging
            redacted_extra = self._redact_sensitive_data(extra)
            log_entry["data"] = redacted_extra
            
        return json.dumps(log_entry)
        
    def info(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
        """Log an info message."""
        self.logger.info(self._format_log_entry("INFO", message, extra))
        
    def warning(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
        """Log a warning message."""
        self.logger.warning(self._format_log_entry("WARNING", message, extra))
        
    def error(self, message: str, extra: Optional[Dict[str, Any]] = None, exc_info: bool = False) -> None:
        """Log an error message."""
        if exc_info:
            extra = extra or {}
            extra["traceback"] = traceback.format_exc()
        self.logger.error(self._format_log_entry("ERROR", message, extra))
        
    def critical(self, message: str, extra: Optional[Dict[str, Any]] = None, exc_info: bool = False) -> None:
        """Log a critical message."""
        if exc_info:
            extra = extra or {}
            extra["traceback"] = traceback.format_exc()
        self.logger.critical(self._format_log_entry("CRITICAL", message, extra))
        
    def debug(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
        """Log a debug message."""
        # Only log debug messages in development environment
        if self.environment == "development":
            self.logger.debug(self._format_log_entry("DEBUG", message, extra))

# Create a factory function to get a structured logger
def get_structured_logger(name: str) -> StructuredLogger:
    """
    Get a structured logger instance.
    
    Args:
        name: Logger name
        
    Returns:
        StructuredLogger instance
    """
    return StructuredLogger(name)
