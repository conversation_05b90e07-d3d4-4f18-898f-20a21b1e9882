#!/bin/bash
# Production deployment script for Cyber Sakha backend
# This script prepares the backend for production deployment

set -e  # Exit on any error

# Display banner
echo "====================================================="
echo "Cyber Sakha Backend Production Deployment"
echo "====================================================="

# Check if running with correct permissions
if [ "$EUID" -ne 0 ]; then
  echo "Please run as root or with sudo"
  exit 1
fi

# Check for required environment variables
if [ -z "$DATABASE_URL" ]; then
  echo "ERROR: DATABASE_URL environment variable is not set"
  echo "Please set DATABASE_URL to your MongoDB Atlas connection string"
  exit 1
fi

if [ -z "$SECRET_KEY" ]; then
  echo "ERROR: SECRET_KEY environment variable is not set"
  echo "Please set SECRET_KEY to a secure random string"
  exit 1
fi

if [ -z "$SESSION_SECRET_KEY" ]; then
  echo "ERROR: SESSION_SECRET_KEY environment variable is not set"
  echo "Please set SESSION_SECRET_KEY to a secure random string"
  exit 1
fi

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
  echo "Creating virtual environment..."
  python3 -m venv venv
fi

# Activate virtual environment
echo "Activating virtual environment..."
source venv/bin/activate

# Install production dependencies
echo "Installing production dependencies..."
pip install --upgrade pip
pip install -r requirements.txt
pip install gunicorn uvicorn[standard]

# Create production .env file if it doesn't exist
if [ ! -f ".env.production" ]; then
  echo "ERROR: .env.production file not found"
  echo "Please create a .env.production file with your production settings"
  exit 1
fi

# Copy production .env file
echo "Setting up production environment..."
cp .env.production .env

# Create logs directory
echo "Creating logs directory..."
mkdir -p logs

# Set proper permissions
echo "Setting proper permissions..."
chmod -R 755 .
chmod -R 644 .env
chmod -R 644 .env.production
chmod -R 755 venv
chmod -R 755 logs

# Create systemd service file
echo "Creating systemd service file..."
cat > /etc/systemd/system/cybersakha.service << EOF
[Unit]
Description=Cyber Sakha Backend
After=network.target

[Service]
User=www-data
Group=www-data
WorkingDirectory=$(pwd)
Environment="PATH=$(pwd)/venv/bin"
ExecStart=$(pwd)/venv/bin/gunicorn main:app --workers 4 --worker-class uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
Restart=always
RestartSec=5
StartLimitInterval=0

[Install]
WantedBy=multi-user.target
EOF

# Reload systemd
echo "Reloading systemd..."
systemctl daemon-reload

# Enable and start service
echo "Enabling and starting service..."
systemctl enable cybersakha.service
systemctl start cybersakha.service

# Check service status
echo "Checking service status..."
systemctl status cybersakha.service

echo "====================================================="
echo "Deployment completed successfully!"
echo "The backend is now running at http://localhost:8000"
echo "====================================================="

# Deactivate virtual environment
deactivate
