import axios, { InternalAxiosRequestConfig, AxiosError, AxiosRequestConfig } from 'axios';
import { User } from '../types/auth';
import { API_URL } from '../config';
import logger from '../utils/logger';

// Extend InternalAxiosRequestConfig to include _retry property
interface ExtendedInternalAxiosRequestConfig extends InternalAxiosRequestConfig {
  _retry?: boolean;
}

// Create axios instance with default config
const api = axios.create({
  baseURL: API_URL,
  timeout: 10000,
  withCredentials: true
});

// Add request interceptor for CSRF token
api.interceptors.request.use(
  async (config: InternalAxiosRequestConfig) => {
    try {
      const csrfToken = getCsrfToken();
      if (csrfToken) {
        config.headers['X-CSRF-Token'] = csrfToken;
      }
      return config;
    } catch (error) {
      logger.error('Error in request interceptor:', error);
      return Promise.reject(error);
    }
  },
  (error) => {
    logger.error('Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Add response interceptor for token refresh and session conflicts
api.interceptors.response.use(
  (response) => response,
  async (error: AxiosError) => {
    const originalRequest = error.config as ExtendedInternalAxiosRequestConfig;

    // If error is 401 and we haven't tried to refresh token yet
    if (error.response?.status === 401 && originalRequest && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Try to refresh the token
        await authService.refreshToken();
        // Retry the original request
        return api(originalRequest as AxiosRequestConfig);
      } catch (refreshError: any) {
        logger.error('Token refresh failed:', refreshError);

        // Check if this is a session conflict error
        if (refreshError.response?.status === 409 &&
            refreshError.response?.data &&
            (refreshError.response.data as any).code === 'SESSION_CONFLICT') {
          // Let the error propagate to be handled by the UI components
          return Promise.reject(refreshError);
        }

        // If refresh fails for other reasons, redirect to login
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }

    // Handle session conflicts at the API level
    if (error.response?.status === 409 &&
        error.response?.data &&
        (error.response.data as any).code === 'SESSION_CONFLICT') {
      logger.warn('Session conflict detected in API call');
      // Let the error propagate to be handled by the UI components
      return Promise.reject(error);
    }

    return Promise.reject(error);
  }
);

// Get CSRF token from cookie
const getCsrfToken = (): string => {
  return document.cookie
    .split('; ')
    .find(row => row.startsWith('csrf_token='))
    ?.split('=')[1] || '';
};

// Simple auth service with enhanced token management
const authService = {
  api,

  // Get CSRF token
  getCsrfToken,

  // Store CSRF token
  storeCSRFToken: (token: string) => {
    // Don't store CSRF token in localStorage for security reasons
    // The token should only be in the HttpOnly cookie set by the backend
    // Just log it for debugging
    logger.debug('CSRF token received:', token);
  },

  // Fetch CSRF token with retry
  fetchCsrfToken: async (retryCount = 0): Promise<string> => {
    try {
      await api.get('/auth/verify-token');
      return getCsrfToken();
    } catch (error) {
      logger.error('Error fetching CSRF token:', error);

      // Retry up to 3 times with exponential backoff
      if (retryCount < 3) {
        const delay = Math.pow(2, retryCount) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
        return authService.fetchCsrfToken(retryCount + 1);
      }

      throw error;
    }
  },

  // Register new user with validation
  register: async (userData: {
    email: string;
    password: string;
    first_name: string;
    last_name: string;
    designation?: string;
  }) => {
    try {
      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(userData.email)) {
        throw new Error('Invalid email format');
      }

      // Validate password strength
      if (userData.password.length < 8) {
        throw new Error('Password must be at least 8 characters long');
      }

      const response = await api.post('/auth/register', userData);
      return response.data;
    } catch (error) {
      logger.error('Registration error:', error);
      throw error;
    }
  },

  // Login user with enhanced error handling
  login: async (email: string, password: string) => {
    try {
      logger.debug('Attempting login with:', { email });
      const formData = new URLSearchParams();
      formData.append('username', email);
      formData.append('password', password);

      // First try regular login directly
      try {
        logger.debug('Attempting regular login');
        const response = await api.post('/auth/login', formData.toString(), {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        });
        logger.debug('Login response:', response.data);
        return response.data;
      } catch (error: any) {
        // If regular login fails with 401, try two-step login
        if (error.response?.status === 401) {
          logger.debug('Regular login failed, trying two-step login');
          try {
            const twoStepResponse = await api.post('/auth/two-step/login', { email, password }, {
              headers: {
                'Content-Type': 'application/json',
              },
            });
            logger.debug('Two-step login response:', twoStepResponse.data);
            return twoStepResponse.data;
          } catch (twoStepError: any) {
            logger.error('Two-step login error:', twoStepError);
            throw twoStepError;
          }
        }
        logger.error('Login error:', error);
        throw error;
      }
    } catch (error) {
      logger.error('Login error:', error);
      throw error;
    }
  },

  // Verify OTP with retry mechanism
  verifyOTP: async (email: string, otp: string, retryCount = 0): Promise<any> => {
    try {
      const response = await api.post('/auth/verify-otp', { email, otp });
      return response.data;
    } catch (error) {
      logger.error('OTP verification error:', error);

      // Retry up to 3 times with exponential backoff
      if (retryCount < 3) {
        const delay = Math.pow(2, retryCount) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
        return authService.verifyOTP(email, otp, retryCount + 1);
      }

      throw error;
    }
  },

  // Two-step verification with enhanced security
  verifyTwoStep: async (email: string, otp: string) => {
    try {
      const response = await api.post('/auth/two-step/verify', { email, otp });
      return response.data;
    } catch (error) {
      logger.error('Two-step verification error:', error);
      throw error;
    }
  },

  // Resend OTP with rate limiting
  resendOTP: async (email: string) => {
    try {
      const response = await api.post('/auth/resend-otp', { email });
      return response.data;
    } catch (error) {
      logger.error('Resend OTP error:', error);
      throw error;
    }
  },

  // Check if user is authenticated with retry
  isAuthenticated: async (retryCount = 0): Promise<boolean> => {
    try {
      const response = await api.get('/auth/verify-token');
      return response.data.valid;
    } catch (error) {
      logger.error('Authentication check error:', error);

      // Retry up to 3 times with exponential backoff
      if (retryCount < 3) {
        const delay = Math.pow(2, retryCount) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
        return authService.isAuthenticated(retryCount + 1);
      }

      return false;
    }
  },

  // Enhanced logout with cleanup
  logout: async () => {
    try {
      await api.post('/auth/logout');

      // Clear only auth-related items from localStorage for better UX
      // This preserves user preferences but removes sensitive data
      localStorage.removeItem('session_id');
      localStorage.removeItem('user');
      localStorage.removeItem('csrf_token');

      // Clear session storage completely
      sessionStorage.clear();

      // Don't redirect here - let the calling component handle redirection
      // This prevents double redirects and allows for proper state cleanup
      return { success: true };
    } catch (error) {
      logger.error('Logout error:', error);
      // Return error but don't throw, to allow graceful handling
      return { success: false, error };
    }
  },

  // Get user data with enhanced error handling
  getUserData: async (): Promise<User | null> => {
    try {
      const response = await api.get('/users/profile');
      const userData = response.data;

      // Ensure all fields are properly mapped
      return {
        id: userData.id,
        email: userData.email,
        first_name: userData.firstName,
        last_name: userData.lastName,
        designation: userData.designation,
        organization: userData.organization,
        paid: Boolean(userData.paid), // Ensure paid is always a boolean
        email_verified: Boolean(userData.emailVerified),
        has_template: Boolean(userData.hasTemplate),
        subscription_expires: userData.subscription_expires,
        complaint_count: userData.complaint_count || 0,
        two_step_enabled: Boolean(userData.twoStepEnabled)
      };
    } catch (error) {
      logger.error('Error fetching user data:', error);
      return null;
    }
  },

  // Enhanced token refresh with retry
  refreshToken: async (retryCount = 0): Promise<any> => {
    try {
      logger.debug('Attempting to refresh token');
      // Make sure we're using the correct endpoint that matches the backend
      const response = await api.post('/auth/refresh', {}, {
        withCredentials: true, // Ensure cookies are sent
        headers: {
          'Content-Type': 'application/json'
        }
      });
      logger.debug('Token refresh successful:', response.data);
      return response.data;
    } catch (error) {
      logger.error('Error refreshing token:', error);

      // Retry up to 3 times with exponential backoff
      if (retryCount < 3) {
        const delay = Math.pow(2, retryCount) * 1000;
        logger.debug(`Retrying token refresh in ${delay}ms (attempt ${retryCount + 1})`);
        await new Promise(resolve => setTimeout(resolve, delay));
        return authService.refreshToken(retryCount + 1);
      }

      throw error;
    }
  }
};

export default authService;
