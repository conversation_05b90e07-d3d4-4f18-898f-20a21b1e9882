import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { FiUpload, FiDatabase } from 'react-icons/fi';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';
import { withLayout } from '../context/LayoutContext';
import authService from '../services/authService';

const DataUpload: React.FC = () => {
  const navigate = useNavigate();
  const [nodalFile, setNodalFile] = useState<File | null>(null);
  const [nodalUploading, setNodalUploading] = useState(false);
  const [nodalMessage, setNodalMessage] = useState('');

  const handleNodalFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setNodalFile(e.target.files[0]);
      setNodalMessage('');
    }
  };

  const uploadNodalData = async () => {
    if (!nodalFile) {
      setNodalMessage('Please select a file first');
      return;
    }

    setNodalUploading(true);
    setNodalMessage('Uploading...');

    try {
      // Ensure we have a CSRF token before making the request
      await authService.fetchCsrfToken();

      const formData = new FormData();
      formData.append('file', nodalFile);

      const response = await authService.api.post('/data/upload/nodal-data', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          'X-CSRF-Token': authService.getCsrfToken() || '',
          'X-Requested-With': 'XMLHttpRequest'
        },
        withCredentials: true // Ensure cookies are sent
      });

      if (response.data.success) {
        setNodalMessage('Upload successful! Processing in background.');
        setNodalFile(null);
      } else {
        setNodalMessage(`Upload failed: ${response.data.message || 'Unknown error'}`);
      }
    } catch (error: any) {
      console.error('Error uploading Nodal Officer data:', error);
      setNodalMessage(`Error: ${error.response?.data?.detail || error.userMessage || error.message || 'Unknown error'}`);
    } finally {
      setNodalUploading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Data Upload</h1>
      <p className="mb-8 text-gray-600 dark:text-gray-300">
        Upload Nodal Officer data for reference. These files will be processed in the background.
      </p>

      <div className="grid grid-cols-1 gap-8">
        {/* Nodal Officer Data Upload */}
        <Card>
          <Card.Header>
            <div className="flex items-center">
              <FiDatabase className="mr-2" />
              <h2 className="text-xl font-semibold">Nodal Officer Data Upload</h2>
            </div>
          </Card.Header>
          <Card.Content>
            <p className="mb-4 text-sm text-gray-600 dark:text-gray-300">
              Upload Excel file containing Nodal Officer data. The file should include columns for name, organization, organization_type, email, phone, and address.
            </p>

            <div className="mb-4">
              <label className="block mb-2 text-sm font-medium">
                Select Nodal Officer Data File (Excel)
              </label>
              <input
                type="file"
                accept=".xlsx,.xls"
                onChange={handleNodalFileChange}
                className="block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 dark:text-gray-400 focus:outline-none dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400"
                disabled={nodalUploading}
              />
              {nodalFile && (
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  Selected: {nodalFile.name} ({Math.round(nodalFile.size / 1024)} KB)
                </p>
              )}
            </div>

            {nodalMessage && (
              <div className={`p-3 mb-4 text-sm rounded-md ${
                nodalMessage.includes('successful')
                  ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300'
                  : nodalMessage.includes('Uploading')
                    ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                    : 'bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300'
              }`}>
                {nodalMessage}
              </div>
            )}

            <Button
              onClick={uploadNodalData}
              disabled={!nodalFile || nodalUploading}
              className="w-full"
              variant="primary"
            >
              {nodalUploading ? 'Uploading...' : 'Upload Nodal Officer Data'}
              <FiUpload className="ml-2" />
            </Button>
          </Card.Content>
        </Card>
      </div>
    </div>
  );
};

export default withLayout(DataUpload, {
  fullWidth: false,
  hideSidebar: false
});
