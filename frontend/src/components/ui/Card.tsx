import React from 'react';
import { twMerge } from 'tailwind-merge';

// Card container
interface CardProps {
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
  hoverable?: boolean;
  style?: React.CSSProperties;
}

export const Card: React.FC<CardProps> = ({
  children,
  className,
  onClick,
  hoverable = false,
  style,
  ...props
}) => {
  // Modern card with theme styling
  const baseStyles = 'backdrop-blur-md rounded-xl shadow-md overflow-hidden border transition-all duration-300';

  // Enhanced hover effects
  const hoverStyles = hoverable
    ? 'transition-all duration-300 hover:shadow-lg transform hover:-translate-y-1'
    : '';

  const clickableStyles = onClick ? 'cursor-pointer' : '';

  return (
    <div
      className={twMerge(baseStyles, hoverStyles, clickableStyles, className)}
      style={{
        backgroundColor: 'var(--theme-bg-card)',
        borderColor: 'var(--theme-border)',
        color: 'var(--theme-text)',
        ...style
      }}
      onClick={onClick}
      data-hoverable={hoverable ? 'true' : 'false'}
      {...props}
    >
      {children}
    </div>
  );
};

// Card header
interface CardHeaderProps {
  children: React.ReactNode;
  className?: string;
  title?: React.ReactNode;
  subtitle?: React.ReactNode;
  action?: React.ReactNode;
}

export const CardHeader: React.FC<CardHeaderProps> = ({
  children,
  className,
  title,
  subtitle,
  action,
  ...props
}) => {
  // If children are provided, render them directly
  if (React.Children.count(children) > 0) {
    return (
      <div
        className={twMerge('px-5 py-4 border-b', className)}
        style={{
          borderColor: 'var(--theme-border)',
          backgroundColor: 'var(--theme-accent)',
          color: 'var(--theme-text)'
        }}
        {...props}>
        {children}
      </div>
    );
  }

  // Otherwise, use the title, subtitle, and action props
  return (
    <div
      className={twMerge('px-5 py-4 border-b', className)}
      style={{
        borderColor: 'var(--theme-border)',
        backgroundColor: 'var(--theme-accent)',
        color: 'var(--theme-text)'
      }}
      {...props}>
      <div className="flex justify-between items-center">
        <div>
          {title && (
            typeof title === 'string'
              ? <h3 className="text-lg font-semibold" style={{ color: 'var(--theme-text)' }}>{title}</h3>
              : title
          )}
          {subtitle && (
            typeof subtitle === 'string'
              ? <p className="text-sm" style={{ color: 'var(--theme-text)', opacity: 0.7 }}>{subtitle}</p>
              : subtitle
          )}
        </div>
        {action && <div>{action}</div>}
      </div>
    </div>
  );
};

// Card content
interface CardContentProps {
  children: React.ReactNode;
  className?: string;
}

export const CardContent: React.FC<CardContentProps> = ({
  children,
  className,
  ...props
}) => {
  return (
    <div
      className={twMerge('px-5 py-4 overflow-auto', className)}
      style={{
        backgroundColor: 'var(--theme-bg-card)',
        color: 'var(--theme-text)',
        scrollbarWidth: 'thin',
        scrollbarColor: 'var(--theme-border) transparent'
      }}
      {...props}
    >
      {children}
    </div>
  );
};

// Card footer
interface CardFooterProps {
  children: React.ReactNode;
  className?: string;
}

export const CardFooter: React.FC<CardFooterProps> = ({
  children,
  className,
  ...props
}) => {
  return (
    <div
      className={twMerge('px-5 py-4 border-t', className)}
      style={{
        borderColor: 'var(--theme-border)',
        backgroundColor: 'var(--theme-bg-card)',
        color: 'var(--theme-text)'
      }}
      {...props}>
      {children}
    </div>
  );
};

export default Object.assign(Card, {
  Header: CardHeader,
  Content: CardContent,
  Footer: CardFooter,
});
