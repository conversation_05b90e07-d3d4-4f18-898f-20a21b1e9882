"""
Input validation utilities to prevent injection attacks and ensure data integrity.

This module provides functions to validate and sanitize user inputs
to prevent various injection attacks and ensure data integrity.
"""
import logging
import re
from typing import Any, Dict, List, Optional, Tuple
from pydantic import BaseModel, ValidationError
from fastapi import HTTPException, status
from utils.html_sanitizer import sanitize_text, sanitize_url

# Setup logging
logger = logging.getLogger(__name__)

def validate_string(value: Optional[str],
                   min_length: int = 0,
                   max_length: int = 1000,
                   pattern: Optional[str] = None,
                   allow_none: bool = False) -> Tuple[bool, Optional[str], Optional[str]]:
    """
    Validate a string value.

    Args:
        value: String to validate
        min_length: Minimum allowed length
        max_length: Maximum allowed length
        pattern: Optional regex pattern to match
        allow_none: Whether None is allowed

    Returns:
        Tuple of (is_valid, sanitized_value, error_message)
    """
    # Check if None is allowed
    if value is None:
        if allow_none:
            return True, None, None
        else:
            return False, None, "Value cannot be None"

    # Ensure value is a string
    if not isinstance(value, str):
        try:
            value = str(value)
        except:
            return False, None, "Value must be a string"

    # Check length constraints
    if len(value) < min_length:
        return False, None, f"Value must be at least {min_length} characters"

    if len(value) > max_length:
        return False, None, f"Value must be at most {max_length} characters"

    # Check pattern if provided
    if pattern and not re.match(pattern, value):
        return False, None, f"Value does not match required pattern"

    # Sanitize the value
    sanitized = sanitize_text(value)

    return True, sanitized, None

def validate_email(email: Optional[str], allow_none: bool = False) -> Tuple[bool, Optional[str], Optional[str]]:
    """
    Validate an email address.

    Args:
        email: Email address to validate
        allow_none: Whether None is allowed

    Returns:
        Tuple of (is_valid, sanitized_value, error_message)
    """
    # Check if None is allowed
    if email is None:
        if allow_none:
            return True, None, None
        else:
            return False, None, "Email cannot be None"

    # Simple regex for email validation
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'

    # Validate using the string validator with email pattern
    is_valid, sanitized, error = validate_string(
        email,
        min_length=5,
        max_length=254,  # Max email length per RFC 5321
        pattern=email_pattern,
        allow_none=allow_none
    )

    if not is_valid:
        return False, None, error or "Invalid email format"

    return True, sanitized, None

def validate_url(url: Optional[str], allow_none: bool = False) -> Tuple[bool, Optional[str], Optional[str]]:
    """
    Validate a URL.

    Args:
        url: URL to validate
        allow_none: Whether None is allowed

    Returns:
        Tuple of (is_valid, sanitized_value, error_message)
    """
    # Check if None is allowed
    if url is None:
        if allow_none:
            return True, None, None
        else:
            return False, None, "URL cannot be None"

    # URL pattern for http and https
    url_pattern = r'^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$'

    # Validate using the string validator with URL pattern
    is_valid, _, error = validate_string(
        url,
        min_length=1,
        max_length=2048,  # Reasonable max URL length
        pattern=url_pattern,
        allow_none=allow_none
    )

    if not is_valid:
        return False, None, error or "Invalid URL format"

    # Use the URL sanitizer for additional security
    sanitized = sanitize_url(url)
    if not sanitized:
        return False, None, "URL contains potentially dangerous content"

    return True, sanitized, None

def validate_numeric(value: Any,
                    min_value: Optional[float] = None,
                    max_value: Optional[float] = None,
                    allow_none: bool = False) -> Tuple[bool, Optional[float], Optional[str]]:
    """
    Validate a numeric value.

    Args:
        value: Numeric value to validate
        min_value: Minimum allowed value
        max_value: Maximum allowed value
        allow_none: Whether None is allowed

    Returns:
        Tuple of (is_valid, sanitized_value, error_message)
    """
    # Check if None is allowed
    if value is None:
        if allow_none:
            return True, None, None
        else:
            return False, None, "Value cannot be None"

    # Convert to float if not already a number
    if not isinstance(value, (int, float)):
        try:
            value = float(value)
        except:
            return False, None, "Value must be a number"

    # Check min value constraint
    if min_value is not None and value < min_value:
        return False, None, f"Value must be at least {min_value}"

    # Check max value constraint
    if max_value is not None and value > max_value:
        return False, None, f"Value must be at most {max_value}"

    return True, value, None

def validate_dict(data: Dict[str, Any],
                 required_fields: Optional[List[str]] = None,
                 field_validators: Optional[Dict[str, Any]] = None) -> Tuple[bool, Dict[str, Any], Dict[str, str]]:
    """
    Validate a dictionary of values.

    Args:
        data: Dictionary to validate
        required_fields: List of required field names
        field_validators: Dictionary mapping field names to validator functions

    Returns:
        Tuple of (is_valid, sanitized_data, error_messages)
    """
    if not isinstance(data, dict):
        return False, {}, {"_general": "Input must be a dictionary"}

    required_fields = required_fields or []
    field_validators = field_validators or {}

    sanitized_data = {}
    errors = {}

    # Check required fields
    for field in required_fields:
        if field not in data or data[field] is None:
            errors[field] = f"Field '{field}' is required"

    # Validate fields with provided validators
    for field, validator in field_validators.items():
        if field in data:
            is_valid, sanitized, error = validator(data[field])
            if is_valid:
                sanitized_data[field] = sanitized
            else:
                errors[field] = error

    # Copy fields without validators
    for field, value in data.items():
        if field not in field_validators:
            sanitized_data[field] = value

    return len(errors) == 0, sanitized_data, errors

def validate_model(data: Dict[str, Any], model_class: type) -> Tuple[bool, Optional[BaseModel], Dict[str, str]]:
    """
    Validate data against a Pydantic model.

    Args:
        data: Data to validate
        model_class: Pydantic model class

    Returns:
        Tuple of (is_valid, model_instance, error_messages)
    """
    try:
        model_instance = model_class(**data)
        return True, model_instance, {}
    except ValidationError as e:
        errors = {}
        for error in e.errors():
            field = ".".join(str(loc) for loc in error["loc"])
            errors[field] = error["msg"]
        return False, None, errors
    except Exception as e:
        logger.error(f"Unexpected error validating model: {str(e)}")
        return False, None, {"_general": f"Validation error: {str(e)}"}

def validate_file(file,
                max_size_mb: float = 10.0,
                allowed_content_types: Optional[List[str]] = None,
                allowed_extensions: Optional[List[str]] = None) -> Tuple[bool, Optional[str]]:
    """
    Validate an uploaded file.

    Args:
        file: The uploaded file object
        max_size_mb: Maximum allowed file size in MB
        allowed_content_types: List of allowed content types
        allowed_extensions: List of allowed file extensions

    Returns:
        Tuple of (is_valid, error_message)
    """
    if file is None:
        return False, "No file provided"

    # Check file size
    max_size_bytes = max_size_mb * 1024 * 1024  # Convert MB to bytes

    # FastAPI's UploadFile doesn't have a direct size attribute
    # We'll need to read the file to check its size
    try:
        file.file.seek(0, 2)  # Seek to the end of the file
        file_size = file.file.tell()  # Get current position (file size)
        file.file.seek(0)  # Reset file position to the beginning

        if file_size > max_size_bytes:
            return False, f"File size exceeds maximum allowed size of {max_size_mb} MB"
    except Exception as e:
        logger.error(f"Error checking file size: {str(e)}")
        return False, "Unable to validate file size"

    # Check file extension if provided
    if allowed_extensions and file.filename:
        file_ext = file.filename.split('.')[-1].lower() if '.' in file.filename else ''
        if file_ext not in [ext.lower().lstrip('.') for ext in allowed_extensions]:
            return False, f"File extension not allowed. Allowed extensions: {', '.join(allowed_extensions)}"

    # Check content type if provided
    if allowed_content_types and file.content_type:
        if file.content_type not in allowed_content_types:
            return False, f"File type not allowed. Allowed types: {', '.join(allowed_content_types)}"

    return True, None

def validate_list(value: Any,
                min_length: int = 0,
                max_length: int = 1000,
                item_validator: Optional[Any] = None) -> Tuple[bool, Optional[List], Optional[str]]:
    """
    Validate a list value.

    Args:
        value: List to validate
        min_length: Minimum allowed length
        max_length: Maximum allowed length
        item_validator: Optional validator function for list items

    Returns:
        Tuple of (is_valid, sanitized_value, error_message)
    """
    # Check if value is a list
    if not isinstance(value, list):
        return False, None, "Value must be a list"

    # Check length constraints
    if len(value) < min_length:
        return False, None, f"List must contain at least {min_length} items"

    if len(value) > max_length:
        return False, None, f"List must contain at most {max_length} items"

    # Validate items if validator is provided
    if item_validator:
        sanitized_items = []
        for i, item in enumerate(value):
            is_valid, sanitized_item, error = item_validator(item)
            if not is_valid:
                return False, None, f"Item at index {i} is invalid: {error}"
            sanitized_items.append(sanitized_item)
        return True, sanitized_items, None

    # If no item validator, return the original list
    return True, value, None

def raise_validation_error(errors: Dict[str, str]):
    """
    Raise an HTTPException with validation errors.

    Args:
        errors: Dictionary of field errors

    Raises:
        HTTPException: With validation error details
    """
    raise HTTPException(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        detail={
            "message": "Validation error",
            "errors": errors
        }
    )
