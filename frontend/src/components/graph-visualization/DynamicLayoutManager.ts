import { Node, <PERSON> } from 'reactflow';

/**
 * DynamicLayoutManager
 *
 * Handles dynamic layout calculations for the transaction graph visualization.
 * Recalculates node positions based on which nodes are currently visible.
 * Maintains a tree-like structure where child nodes appear directly below their parent nodes.
 */

// Interface for node with leaf count information
interface NodeWithLeafCount {
  id: string;
  leafCount: number;
  children: string[];
}

// Constants for layout calculations
const NODE_WIDTH = 350; // Width of each node
const NODE_HEIGHT = 350; // Base height of a node
const HORIZONTAL_SPACING = 150; // Horizontal spacing between nodes in the same row/column
const VERTICAL_SPACING = 600; // Vertical spacing between layers/rows (increased significantly)
const SPACING_FACTOR = 1.5; // Multiplier for spacing (increased for better separation)

// Interface for layout options
interface LayoutOptions {
  isHorizontalLayout: boolean;
  animate: boolean;
}

/**
 * Calculate the optimal layout for visible nodes
 * @param nodes All nodes in the graph
 * @param edges All edges in the graph
 * @param options Layout options
 * @returns Updated nodes with new positions
 */
/**
 * Estimate the height of a node based on its data
 * @param node The node to estimate height for
 * @returns Estimated height of the node
 */
const estimateNodeHeight = (node: Node): number => {
  // Base height for all nodes
  let height = NODE_HEIGHT;

  // Check if the node has multiple transactions
  if (node.data?.hasMultipleTransactions && node.data?.multipleTransactions?.length > 0) {
    // Add extra height for each transaction
    height += node.data.multipleTransactions.length * 150;
  }

  // Check if the node has sub-transactions
  if (node.data?.subTransactions?.length > 0) {
    // Add extra height for each sub-transaction
    height += node.data.subTransactions.length * 180;
  }

  return height;
};

/**
 * Calculate the leaf count for each node in the tree
 * @param nodeId The ID of the node to calculate leaf count for
 * @param childrenMap Map of parent-child relationships
 * @param leafCountMap Map to store leaf counts
 * @returns The leaf count for the node
 */
const calculateLeafCount = (
  nodeId: string,
  childrenMap: Map<string, string[]>,
  leafCountMap: Map<string, number>
): number => {
  // If we've already calculated this node's leaf count, return it
  if (leafCountMap.has(nodeId)) {
    return leafCountMap.get(nodeId)!;
  }

  // Get children of this node
  const children = childrenMap.get(nodeId) || [];

  // If no children, this is a leaf node
  if (children.length === 0) {
    leafCountMap.set(nodeId, 1);
    return 1;
  }

  // Calculate leaf count as sum of children's leaf counts
  const leafCount = children.reduce((sum, childId) => {
    return sum + calculateLeafCount(childId, childrenMap, leafCountMap);
  }, 0);

  // Store and return the leaf count
  leafCountMap.set(nodeId, leafCount);
  return leafCount;
};

/**
 * Build a tree structure with leaf count information
 * @param nodes Visible nodes
 * @param childrenMap Map of parent-child relationships
 * @param rootNodes Root nodes of the tree
 * @returns Map of node IDs to NodeWithLeafCount objects
 */
const buildTreeWithLeafCounts = (
  nodes: Node[],
  childrenMap: Map<string, string[]>,
  rootNodes: Node[]
): Map<string, NodeWithLeafCount> => {
  // Map to store leaf counts
  const leafCountMap = new Map<string, number>();

  // Calculate leaf counts for all root nodes
  rootNodes.forEach(node => {
    calculateLeafCount(node.id, childrenMap, leafCountMap);
  });

  // Build tree with leaf count information
  const treeMap = new Map<string, NodeWithLeafCount>();

  nodes.forEach(node => {
    treeMap.set(node.id, {
      id: node.id,
      leafCount: leafCountMap.get(node.id) || 1, // Default to 1 if not calculated
      children: childrenMap.get(node.id) || []
    });
  });

  return treeMap;
};

export const calculateDynamicLayout = (
  nodes: Node[],
  edges: Edge[],
  options: LayoutOptions
): Node[] => {
  const { isHorizontalLayout, animate } = options;

  // Filter out hidden nodes
  const visibleNodes = nodes.filter(node => !node.hidden);

  // If no visible nodes, return original nodes
  if (visibleNodes.length === 0) return nodes;

  // Create a map of nodes by ID for quick lookup
  const nodeMap = new Map<string, Node>();
  visibleNodes.forEach(node => nodeMap.set(node.id, node));

  // Create a map of parent-child relationships
  const childrenMap = new Map<string, string[]>();
  const parentMap = new Map<string, string>();

  // Initialize maps
  visibleNodes.forEach(node => {
    childrenMap.set(node.id, []);
  });

  // Build parent-child relationships from edges
  edges.forEach(edge => {
    // Skip hidden edges
    if (edge.hidden) return;

    const sourceNode = nodeMap.get(edge.source);
    const targetNode = nodeMap.get(edge.target);

    // Skip if either node is not visible
    if (!sourceNode || !targetNode) return;

    // Add child to parent's children list
    const children = childrenMap.get(edge.source) || [];
    children.push(edge.target);
    childrenMap.set(edge.source, children);

    // Set parent for child
    parentMap.set(edge.target, edge.source);
  });

  // Find root nodes (nodes without parents)
  const rootNodes = visibleNodes.filter(node =>
    node.type === 'metadata' || !parentMap.has(node.id)
  );

  // Build tree with leaf count information
  const treeMap = buildTreeWithLeafCounts(visibleNodes, childrenMap, rootNodes);

  // Calculate node positions based on layout direction
  if (isHorizontalLayout) {
    return calculateHorizontalLayout(nodes, rootNodes, childrenMap, nodeMap, treeMap, animate);
  } else {
    return calculateVerticalLayout(nodes, rootNodes, childrenMap, nodeMap, treeMap, animate);
  }
};

/**
 * Calculate horizontal layout (left to right)
 */
const calculateHorizontalLayout = (
  allNodes: Node[],
  rootNodes: Node[],
  childrenMap: Map<string, string[]>,
  nodeMap: Map<string, Node>,
  treeMap: Map<string, NodeWithLeafCount>,
  animate: boolean
): Node[] => {
  // Create a copy of all nodes to update positions
  const updatedNodes = [...allNodes];

  // Calculate levels (columns) for each node
  const nodeLevels = new Map<string, number>();

  // Set root nodes at level 0
  rootNodes.forEach(node => {
    nodeLevels.set(node.id, 0);
  });

  // Calculate levels for all nodes using BFS
  const calculateLevels = () => {
    const queue = [...rootNodes];
    while (queue.length > 0) {
      const node = queue.shift()!;
      const level = nodeLevels.get(node.id)!;

      // Process children
      const children = childrenMap.get(node.id) || [];
      children.forEach(childId => {
        const childNode = nodeMap.get(childId);
        if (childNode) {
          nodeLevels.set(childId, level + 1);
          queue.push(childNode);
        }
      });
    }
  };

  calculateLevels();

  // Group nodes by level
  const nodesByLevel = new Map<number, Node[]>();
  nodeMap.forEach((node, id) => {
    const level = nodeLevels.get(id) || 0;
    const levelNodes = nodesByLevel.get(level) || [];
    levelNodes.push(node);
    nodesByLevel.set(level, levelNodes);
  });

  // Calculate horizontal position for each level
  let currentX = 0;
  const levelXPositions = new Map<number, number>();

  // Sort levels
  const levels = Array.from(nodesByLevel.keys()).sort((a, b) => a - b);

  // Calculate max node height for each level to ensure proper spacing
  const maxNodeHeightByLevel = new Map<number, number>();
  levels.forEach(level => {
    const levelNodes = nodesByLevel.get(level) || [];
    const maxHeight = levelNodes.reduce((max, node) => {
      const height = estimateNodeHeight(node);
      return Math.max(max, height);
    }, NODE_HEIGHT);
    maxNodeHeightByLevel.set(level, maxHeight);
  });

  // Calculate X position for each level
  levels.forEach(level => {
    levelXPositions.set(level, currentX);

    // Get the maximum node width for this level (use NODE_WIDTH as base)
    const maxNodeWidth = NODE_WIDTH;

    // Add spacing between columns in horizontal layout based on the maximum node width
    // This ensures that wider nodes don't overlap with nodes in the next level
    currentX += Math.max(VERTICAL_SPACING, maxNodeWidth * 1.5);

    // Add extra spacing between hierarchy levels
    if (level === -1) {
      // Extra spacing after metadata node
      currentX += 200;
    } else if (level === 0) {
      // Extra spacing after sender nodes
      currentX += 150;
    } else if (level === 1) {
      // Extra spacing after first layer receivers
      currentX += 100;
    }
  });

  // Map to store vertical positions of nodes
  const nodeYPositions = new Map<string, number>();

  // Process nodes level by level, starting from the root
  // First, handle metadata nodes (if any)
  const metadataNodes = rootNodes.filter(node => node.type === 'metadata');
  if (metadataNodes.length > 0) {
    metadataNodes.forEach(node => {
      const nodeIndex = updatedNodes.findIndex(n => n.id === node.id);
      if (nodeIndex !== -1) {
        const xPos = levelXPositions.get(0) || 0;

        // Position metadata node at the center
        updatedNodes[nodeIndex] = {
          ...updatedNodes[nodeIndex],
          position: {
            x: xPos,
            y: 0
          },
          style: animate ? {
            ...updatedNodes[nodeIndex].style,
            transition: 'all 0.5s cubic-bezier(0.25, 0.1, 0.25, 1.0)'
          } : updatedNodes[nodeIndex].style
        };

        // Store position for reference by children
        nodeYPositions.set(node.id, 0);
      }
    });
  }

  // Process sender nodes (level 0)
  const senderNodes = rootNodes.filter(node => node.type !== 'metadata');

  // Calculate total leaf count for all sender nodes
  const totalSenderLeafCount = senderNodes.reduce((sum, node) => {
    const nodeInfo = treeMap.get(node.id);
    return sum + (nodeInfo ? nodeInfo.leafCount : 1);
  }, 0);

  // Calculate total height needed for all sender nodes
  const totalHeight = totalSenderLeafCount * NODE_HEIGHT;
  const spacing = HORIZONTAL_SPACING;
  const totalSpacing = (totalSenderLeafCount - 1) * spacing;

  // Start from the top, centered vertically
  let currentY = -(totalHeight + totalSpacing) / 2;

  // Process sender nodes
  senderNodes.forEach(node => {
    const nodeIndex = updatedNodes.findIndex(n => n.id === node.id);
    if (nodeIndex !== -1) {
      const xPos = levelXPositions.get(0) || 0;
      const nodeInfo = treeMap.get(node.id);
      const leafCount = nodeInfo ? nodeInfo.leafCount : 1;

      // Calculate space needed for this sender node based on leaf count
      const nodeSpace = leafCount * NODE_HEIGHT + (leafCount - 1) * spacing;

      // Position sender node at the center of its allocated space
      const yPos = currentY + nodeSpace / 2;

      updatedNodes[nodeIndex] = {
        ...updatedNodes[nodeIndex],
        position: {
          x: xPos,
          y: yPos
        },
        style: animate ? {
          ...updatedNodes[nodeIndex].style,
          transition: 'all 0.5s cubic-bezier(0.25, 0.1, 0.25, 1.0)'
        } : updatedNodes[nodeIndex].style
      };

      // Store position for reference by children
      nodeYPositions.set(node.id, yPos);

      // Move down for next sender node
      currentY += nodeSpace + spacing;
    }
  });

  // Process remaining levels
  for (let level = 1; level < levels.length; level++) {
    const levelNodes = nodesByLevel.get(level) || [];

    // Process nodes in this level
    levelNodes.forEach(node => {
      const nodeIndex = updatedNodes.findIndex(n => n.id === node.id);
      if (nodeIndex !== -1) {
        const xPos = levelXPositions.get(level) || 0;

        // Find parent node
        const parentId = Array.from(childrenMap.entries())
          .find(([_, children]) => children.includes(node.id))?.[0];

        if (parentId && nodeYPositions.has(parentId)) {
          // Get parent's position
          const parentY = nodeYPositions.get(parentId)!;

          // Get parent's children
          const siblings = childrenMap.get(parentId) || [];

          // Calculate total leaf count for all siblings
          const totalSiblingLeafCount = siblings.reduce((sum, siblingId) => {
            const siblingInfo = treeMap.get(siblingId);
            return sum + (siblingInfo ? siblingInfo.leafCount : 1);
          }, 0);

          // Calculate space needed for all siblings
          const siblingSpace = totalSiblingLeafCount * NODE_HEIGHT + (totalSiblingLeafCount - 1) * spacing;

          // Calculate starting position for siblings
          const siblingStartY = parentY - siblingSpace / 2;

          // Calculate position for this node based on its index among siblings
          let siblingCurrentY = siblingStartY;

          // Process siblings in order to calculate position
          for (let i = 0; i < siblings.indexOf(node.id); i++) {
            const siblingId = siblings[i];
            const siblingInfo = treeMap.get(siblingId);
            const siblingLeafCount = siblingInfo ? siblingInfo.leafCount : 1;

            // Move down by the space needed for this sibling
            siblingCurrentY += siblingLeafCount * NODE_HEIGHT + (siblingLeafCount - 1) * spacing;
          }

          // Get leaf count for this node
          const nodeInfo = treeMap.get(node.id);
          const leafCount = nodeInfo ? nodeInfo.leafCount : 1;

          // Calculate space needed for this node
          const nodeSpace = leafCount * NODE_HEIGHT + (leafCount - 1) * spacing;

          // Position node at the center of its allocated space
          const yPos = siblingCurrentY + nodeSpace / 2;

          updatedNodes[nodeIndex] = {
            ...updatedNodes[nodeIndex],
            position: {
              x: xPos,
              y: yPos
            },
            style: animate ? {
              ...updatedNodes[nodeIndex].style,
              transition: 'all 0.5s cubic-bezier(0.25, 0.1, 0.25, 1.0)'
            } : updatedNodes[nodeIndex].style
          };

          // Store position for reference by children
          nodeYPositions.set(node.id, yPos);
        }
      }
    });
  }

  return updatedNodes;
};

/**
 * Calculate vertical layout (top to bottom)
 */
const calculateVerticalLayout = (
  allNodes: Node[],
  rootNodes: Node[],
  childrenMap: Map<string, string[]>,
  nodeMap: Map<string, Node>,
  treeMap: Map<string, NodeWithLeafCount>,
  animate: boolean
): Node[] => {
  // Create a copy of all nodes to update positions
  const updatedNodes = [...allNodes];

  // Calculate levels (rows) for each node
  const nodeLevels = new Map<string, number>();

  // Set root nodes at level 0
  rootNodes.forEach(node => {
    nodeLevels.set(node.id, 0);
  });

  // Calculate levels for all nodes using BFS
  const calculateLevels = () => {
    const queue = [...rootNodes];
    while (queue.length > 0) {
      const node = queue.shift()!;
      const level = nodeLevels.get(node.id)!;

      // Process children
      const children = childrenMap.get(node.id) || [];
      children.forEach(childId => {
        const childNode = nodeMap.get(childId);
        if (childNode) {
          nodeLevels.set(childId, level + 1);
          queue.push(childNode);
        }
      });
    }
  };

  calculateLevels();

  // Group nodes by level
  const nodesByLevel = new Map<number, Node[]>();
  nodeMap.forEach((node, id) => {
    const level = nodeLevels.get(id) || 0;
    const levelNodes = nodesByLevel.get(level) || [];
    levelNodes.push(node);
    nodesByLevel.set(level, levelNodes);
  });

  // Calculate vertical position for each level
  let currentY = 0;
  const levelYPositions = new Map<number, number>();

  // Sort levels
  const levels = Array.from(nodesByLevel.keys()).sort((a, b) => a - b);

  // Calculate max node height for each level to ensure proper spacing
  const maxNodeHeightByLevel = new Map<number, number>();
  levels.forEach(level => {
    const levelNodes = nodesByLevel.get(level) || [];
    const maxHeight = levelNodes.reduce((max, node) => {
      const height = estimateNodeHeight(node);
      return Math.max(max, height);
    }, NODE_HEIGHT);
    maxNodeHeightByLevel.set(level, maxHeight);
  });

  // Calculate Y position for each level
  levels.forEach(level => {
    levelYPositions.set(level, currentY);

    // Get the maximum node height for this level
    const maxNodeHeight = maxNodeHeightByLevel.get(level) || NODE_HEIGHT;

    // Add spacing between rows in vertical layout based on the maximum node height
    // This ensures that taller nodes don't overlap with nodes in the next level
    currentY += Math.max(VERTICAL_SPACING, maxNodeHeight * 1.5);

    // Add extra spacing between hierarchy levels
    if (level === -1) {
      // Extra spacing after metadata node
      currentY += 200;
    } else if (level === 0) {
      // Extra spacing after sender nodes
      currentY += 150;
    } else if (level === 1) {
      // Extra spacing after first layer receivers
      currentY += 100;
    }
  });

  // Map to store horizontal positions of nodes
  const nodeXPositions = new Map<string, number>();

  // Process nodes level by level, starting from the root
  // First, handle metadata nodes (if any)
  const metadataNodes = rootNodes.filter(node => node.type === 'metadata');
  if (metadataNodes.length > 0) {
    metadataNodes.forEach(node => {
      const nodeIndex = updatedNodes.findIndex(n => n.id === node.id);
      if (nodeIndex !== -1) {
        const yPos = levelYPositions.get(0) || 0;

        // Position metadata node at the center
        updatedNodes[nodeIndex] = {
          ...updatedNodes[nodeIndex],
          position: {
            x: 0,
            y: yPos
          },
          style: animate ? {
            ...updatedNodes[nodeIndex].style,
            transition: 'all 0.5s cubic-bezier(0.25, 0.1, 0.25, 1.0)'
          } : updatedNodes[nodeIndex].style
        };

        // Store position for reference by children
        nodeXPositions.set(node.id, 0);
      }
    });
  }

  // Process sender nodes (level 0)
  const senderNodes = rootNodes.filter(node => node.type !== 'metadata');

  // Calculate total leaf count for all sender nodes
  const totalSenderLeafCount = senderNodes.reduce((sum, node) => {
    const nodeInfo = treeMap.get(node.id);
    return sum + (nodeInfo ? nodeInfo.leafCount : 1);
  }, 0);

  // Calculate total width needed for all sender nodes
  const totalWidth = totalSenderLeafCount * NODE_WIDTH;
  const spacing = HORIZONTAL_SPACING;
  const totalSpacing = (totalSenderLeafCount - 1) * spacing;

  // Start from the left, centered horizontally
  let currentX = -(totalWidth + totalSpacing) / 2;

  // Process sender nodes
  senderNodes.forEach(node => {
    const nodeIndex = updatedNodes.findIndex(n => n.id === node.id);
    if (nodeIndex !== -1) {
      const yPos = levelYPositions.get(0) || 0;
      const nodeInfo = treeMap.get(node.id);
      const leafCount = nodeInfo ? nodeInfo.leafCount : 1;

      // Calculate space needed for this sender node based on leaf count
      const nodeSpace = leafCount * NODE_WIDTH + (leafCount - 1) * spacing;

      // Position sender node at the center of its allocated space
      const xPos = currentX + nodeSpace / 2;

      updatedNodes[nodeIndex] = {
        ...updatedNodes[nodeIndex],
        position: {
          x: xPos,
          y: yPos
        },
        style: animate ? {
          ...updatedNodes[nodeIndex].style,
          transition: 'all 0.5s cubic-bezier(0.25, 0.1, 0.25, 1.0)'
        } : updatedNodes[nodeIndex].style
      };

      // Store position for reference by children
      nodeXPositions.set(node.id, xPos);

      // Move right for next sender node
      currentX += nodeSpace + spacing;
    }
  });

  // Process remaining levels
  for (let level = 1; level < levels.length; level++) {
    const levelNodes = nodesByLevel.get(level) || [];

    // Process nodes in this level
    levelNodes.forEach(node => {
      const nodeIndex = updatedNodes.findIndex(n => n.id === node.id);
      if (nodeIndex !== -1) {
        const yPos = levelYPositions.get(level) || 0;

        // Find parent node
        const parentId = Array.from(childrenMap.entries())
          .find(([_, children]) => children.includes(node.id))?.[0];

        if (parentId && nodeXPositions.has(parentId)) {
          // Get parent's position
          const parentX = nodeXPositions.get(parentId)!;

          // Get parent's children
          const siblings = childrenMap.get(parentId) || [];

          // Calculate total leaf count for all siblings
          const totalSiblingLeafCount = siblings.reduce((sum, siblingId) => {
            const siblingInfo = treeMap.get(siblingId);
            return sum + (siblingInfo ? siblingInfo.leafCount : 1);
          }, 0);

          // Calculate space needed for all siblings
          const siblingSpace = totalSiblingLeafCount * NODE_WIDTH + (totalSiblingLeafCount - 1) * spacing;

          // Calculate starting position for siblings
          const siblingStartX = parentX - siblingSpace / 2;

          // Calculate position for this node based on its index among siblings
          let siblingCurrentX = siblingStartX;

          // Process siblings in order to calculate position
          for (let i = 0; i < siblings.indexOf(node.id); i++) {
            const siblingId = siblings[i];
            const siblingInfo = treeMap.get(siblingId);
            const siblingLeafCount = siblingInfo ? siblingInfo.leafCount : 1;

            // Move right by the space needed for this sibling
            siblingCurrentX += siblingLeafCount * NODE_WIDTH + (siblingLeafCount - 1) * spacing;
          }

          // Get leaf count for this node
          const nodeInfo = treeMap.get(node.id);
          const leafCount = nodeInfo ? nodeInfo.leafCount : 1;

          // Calculate space needed for this node
          const nodeSpace = leafCount * NODE_WIDTH + (leafCount - 1) * spacing;

          // Position node at the center of its allocated space
          const xPos = siblingCurrentX + nodeSpace / 2;

          updatedNodes[nodeIndex] = {
            ...updatedNodes[nodeIndex],
            position: {
              x: xPos,
              y: yPos
            },
            style: animate ? {
              ...updatedNodes[nodeIndex].style,
              transition: 'all 0.5s cubic-bezier(0.25, 0.1, 0.25, 1.0)'
            } : updatedNodes[nodeIndex].style
          };

          // Store position for reference by children
          nodeXPositions.set(node.id, xPos);
        }
      }
    });
  }

  return updatedNodes;
};
