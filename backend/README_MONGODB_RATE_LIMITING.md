# MongoDB-based Rate Limiting and Token Blacklisting

This document explains how rate limiting and token blacklisting are implemented using MongoDB instead of Redis in this application.

## Overview

Instead of using Redis for rate limiting and token blacklisting, this application uses MongoDB collections to achieve the same functionality. This approach has the following advantages:

1. No need for an additional Redis server
2. Consistent database technology across the application
3. Automatic cleanup of expired entries using MongoDB TTL indexes

## Implementation Details

### Token Blacklisting

Token blacklisting is implemented using a MongoDB collection called `blacklisted_tokens`. When a user logs out or a token needs to be invalidated, the token's JTI (JWT ID) is stored in this collection along with an expiration timestamp.

#### Key Components:

1. **Collection Structure**:
   ```
   {
     "jti": "unique-token-id",
     "expires_at": ISODate("2023-05-01T12:00:00Z"),
     "created_at": ISODate("2023-04-30T12:00:00Z")
   }
   ```

2. **Indexes**:
   - A unique index on `jti` to prevent duplicate entries
   - A TTL index on `expires_at` to automatically remove expired tokens

3. **Usage**:
   - When verifying a token, the application checks if the token's J<PERSON> exists in the blacklisted_tokens collection
   - If the token is found and not expired, the token is considered blacklisted

### Rate Limiting

Rate limiting is implemented using a MongoDB collection called `rate_limits`. Each request from a client increments a counter in this collection, and if the counter exceeds the defined limit within a specific time window, the request is rejected.

#### Key Components:

1. **Collection Structure**:
   ```
   {
     "key": "hashed-client-identifier",
     "timestamp": ISODate("2023-05-01T12:00:00Z"),
     "expires_at": ISODate("2023-05-01T12:01:00Z")
   }
   ```

2. **Indexes**:
   - A compound index on `key` and `timestamp` for efficient querying
   - A TTL index on `expires_at` to automatically remove expired entries

3. **Usage**:
   - Each client is identified by a unique key (typically based on IP address)
   - For each request, the application counts how many entries exist for that key within the time window
   - If the count exceeds the limit, the request is rejected with a 429 status code

## How to Use

### Token Blacklisting

```python
# To blacklist a token
await blacklist_token(jti, expires_in_seconds)

# To check if a token is blacklisted
is_blacklisted = await is_token_blacklisted(jti)
```

### Rate Limiting

```python
# Apply rate limiting to an endpoint (3 requests per minute)
@router.post("/endpoint", dependencies=[Depends(create_limiter(times=3, seconds=60))])
async def rate_limited_endpoint():
    return {"message": "This endpoint is rate limited"}
```

## Configuration

The rate limiting and token blacklisting functionality is automatically configured when the application starts. The necessary MongoDB indexes are created during application startup in the `create_sync_indexes()` function.

## Maintenance

The MongoDB TTL indexes automatically clean up expired entries, so no manual maintenance is required. However, you may want to monitor the size of these collections in a production environment to ensure they don't grow too large.
