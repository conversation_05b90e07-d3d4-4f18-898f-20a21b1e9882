#!/usr/bin/env python3
"""
Cleanup Script for Cyber Sakha

This script cleans up unnecessary files and directories from the project:
1. Removes temporary files and directories
2. Cleans up build artifacts
3. Removes log files
4. Removes unnecessary sample data

Usage:
    python cleanup.py [--dry-run]
"""

import os
import shutil
import argparse
from pathlib import Path
from typing import List, Set

# ANSI color codes for output formatting
GREEN = "\033[92m"
YELLOW = "\033[93m"
RED = "\033[91m"
RESET = "\033[0m"
BOLD = "\033[1m"

# Directories to clean up
DIRS_TO_CLEAN = [
    # Build artifacts
    "frontend/dist",
    "frontend/.vite",
    "frontend/node_modules/.vite",
    
    # Temporary directories
    "backend/temp",
    "backend/uploads",
    
    # Cache directories
    "__pycache__",
    ".pytest_cache",
    ".coverage",
    "htmlcov",
    
    # Sample data (only if confirmed)
    # "sample_data",
]

# File patterns to clean up
FILE_PATTERNS = [
    # Log files
    "*.log",
    "*.log.*",
    
    # Temporary files
    "*.tmp",
    "*.bak",
    "*.swp",
    "*.swo",
    
    # Cache files
    ".DS_Store",
    "Thumbs.db",
    "desktop.ini",
    
    # Python cache
    "*.pyc",
    "*.pyo",
    "*.pyd",
    
    # JavaScript cache
    ".eslintcache",
]

def print_header(text: str) -> None:
    """Print a formatted header."""
    print(f"\n{BOLD}{text}{RESET}")
    print("=" * len(text))

def find_files_to_clean(base_dir: str) -> List[str]:
    """Find all files matching the patterns to clean."""
    files_to_clean = []
    
    for root, dirs, files in os.walk(base_dir):
        # Check for directory patterns
        for dir_pattern in DIRS_TO_CLEAN:
            if os.path.basename(root) == os.path.basename(dir_pattern) and os.path.exists(os.path.join(base_dir, dir_pattern)):
                files_to_clean.append(root)
                break
        
        # Check for file patterns
        for file in files:
            file_path = os.path.join(root, file)
            for pattern in FILE_PATTERNS:
                if pattern.startswith("*."):
                    # Extension pattern
                    if file.endswith(pattern[1:]):
                        files_to_clean.append(file_path)
                        break
                elif pattern in file:
                    # Substring pattern
                    files_to_clean.append(file_path)
                    break
    
    return files_to_clean

def clean_files(files_to_clean: List[str], dry_run: bool = False) -> None:
    """Clean up the specified files and directories."""
    for path in files_to_clean:
        try:
            if os.path.isdir(path):
                if dry_run:
                    print(f"{YELLOW}Would remove directory:{RESET} {path}")
                else:
                    shutil.rmtree(path)
                    print(f"{GREEN}Removed directory:{RESET} {path}")
            else:
                if dry_run:
                    print(f"{YELLOW}Would remove file:{RESET} {path}")
                else:
                    os.remove(path)
                    print(f"{GREEN}Removed file:{RESET} {path}")
        except Exception as e:
            print(f"{RED}Error removing {path}:{RESET} {e}")

def main() -> None:
    """Main function to run cleanup."""
    parser = argparse.ArgumentParser(description="Clean up unnecessary files from the project")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be deleted without actually deleting")
    args = parser.parse_args()
    
    print(f"{BOLD}Cyber Sakha Cleanup{RESET}")
    print("=" * 30)
    
    if args.dry_run:
        print(f"{YELLOW}Running in dry-run mode. No files will be deleted.{RESET}")
    
    print_header("Finding files to clean")
    files_to_clean = find_files_to_clean(".")
    
    if not files_to_clean:
        print(f"{GREEN}No files found to clean up.{RESET}")
        return
    
    print(f"Found {len(files_to_clean)} files/directories to clean up.")
    
    if not args.dry_run:
        confirm = input(f"{YELLOW}Are you sure you want to delete these files? (y/n):{RESET} ")
        if confirm.lower() != 'y':
            print("Cleanup cancelled.")
            return
    
    print_header("Cleaning up files")
    clean_files(files_to_clean, args.dry_run)
    
    print(f"\n{BOLD}Cleanup completed{RESET}")
    if args.dry_run:
        print(f"{YELLOW}This was a dry run. Run without --dry-run to actually delete files.{RESET}")

if __name__ == "__main__":
    main()
