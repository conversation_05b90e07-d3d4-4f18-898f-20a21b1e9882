import logging
from typing import List, Dict, Any, Optional
from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorClient
from fastapi import HTTPException
import base64
from services.csv_service import parse_csv_to_transactions
from services.encryption_service import decrypt_complaint

# Configure logging
logger = logging.getLogger(__name__)

async def find_common_accounts(complaint_ids: List[str], user_id: str, db: AsyncIOMotorClient) -> Dict[str, Any]:
    """
    Find common accounts across multiple complaints with detailed transaction data.

    Args:
        complaint_ids: List of complaint IDs to analyze
        user_id: ID of the user making the request
        db: Database connection

    Returns:
        Dictionary containing common accounts and their detailed transactions
    """
    try:
        logger.info(f"Finding common accounts across {len(complaint_ids)} complaints for user {user_id}")

        # Validate that all complaints belong to the user
        for complaint_id in complaint_ids:
            complaint = await db.complaints.find_one({"_id": ObjectId(complaint_id), "user_id": user_id})
            if not complaint:
                logger.warning(f"Complaint {complaint_id} not found or does not belong to user {user_id}")
                raise HTTPException(status_code=404, detail=f"Complaint {complaint_id} not found or access denied")

        # Process each complaint to extract accounts and transactions
        all_accounts = {}
        complaint_details = {}
        all_transactions = {}

        for complaint_id in complaint_ids:
            # Fetch the complaint
            complaint = await db.complaints.find_one({"_id": ObjectId(complaint_id)})
            if not complaint:
                continue

            # Decrypt the complaint
            decrypted_complaint = decrypt_complaint(complaint)

            # Store complaint details for reference
            complaint_details[complaint_id] = {
                "complaint_number": decrypted_complaint.get("complaint_number", ""),
                "complainant_name": decrypted_complaint.get("complainant_name", ""),
                "date": decrypted_complaint.get("date", ""),
                "amount": decrypted_complaint.get("amount", 0),
                "category": decrypted_complaint.get("category", ""),
                "status": decrypted_complaint.get("status", ""),
            }

            # Get all transactions from the complaint
            transactions = []

            # Try to get transactions from CSV data first
            if decrypted_complaint.get("csv_data_base64"):
                csv_data = decrypted_complaint.get("csv_data_base64")
                if isinstance(csv_data, str):
                    transactions = extract_transactions_from_csv(csv_data)
            # Then try to get from graph_data
            elif decrypted_complaint.get("graph_data") and isinstance(decrypted_complaint["graph_data"], dict) and "transactions" in decrypted_complaint["graph_data"]:
                transactions = decrypted_complaint["graph_data"]["transactions"]

            # Store all transactions for this complaint
            all_transactions[complaint_id] = transactions

            # Extract receiver accounts from transactions
            for transaction in transactions:
                if transaction.get("receiver_account"):
                    receiver_account = transaction["receiver_account"].strip()
                    if receiver_account and not receiver_account.startswith("-:"):  # Skip empty or cleaned accounts
                        # Initialize account if not already in the collection
                        if receiver_account not in all_accounts:
                            all_accounts[receiver_account] = {
                                "account": receiver_account,
                                "account_type": "receiver",
                                "bank": transaction.get("receiver_bank", ""),
                                "transactions": [],
                                "total_occurrences": 0
                            }

                        # Add this transaction
                        all_accounts[receiver_account]["transactions"].append({
                            "complaint_id": complaint_id,
                            "complaint_number": complaint_details[complaint_id]["complaint_number"],
                            "transaction_ids": [transaction.get("receiver_transaction_id") or transaction.get("txn_id", "")],
                            "amount": float(transaction.get("amount", 0)),
                            "sender_account": transaction.get("sender_account", ""),
                            "sender_bank": transaction.get("sender_bank", ""),
                            "receiver_account": receiver_account,
                            "receiver_bank": transaction.get("receiver_bank", ""),
                            "date": transaction.get("date", ""),
                            "receiver_info": transaction.get("receiver_info", transaction.get("remarks", "")),
                            "txn_id": transaction.get("txn_id", ""),
                            "txn_type": transaction.get("txn_type", transaction.get("type", ""))
                        })

                        # Count unique complaints for this account
                        complaint_ids_for_account = set(tx["complaint_id"] for tx in all_accounts[receiver_account]["transactions"])
                        all_accounts[receiver_account]["total_occurrences"] = len(complaint_ids_for_account)

        # Filter to only include accounts that appear in multiple complaints
        common_accounts = {
            account: details
            for account, details in all_accounts.items()
            if details["total_occurrences"] > 1
        }

        # Sort by number of occurrences (descending)
        sorted_accounts = sorted(
            common_accounts.values(),
            key=lambda x: x["total_occurrences"],
            reverse=True
        )

        return {
            "common_accounts": sorted_accounts,
            "complaint_details": complaint_details,
            "total_complaints_analyzed": len(complaint_ids),
            "total_common_accounts": len(sorted_accounts)
        }

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error finding common accounts: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to analyze complaints: {str(e)}")



def extract_transactions_from_csv(csv_data_base64: str) -> List[Dict[str, Any]]:
    """
    Extract transactions from base64 encoded CSV data.

    Args:
        csv_data_base64: Base64 encoded CSV string

    Returns:
        List of transaction dictionaries
    """
    try:
        # Log the input data type and length
        logger.info(f"CSV Analysis: Processing CSV data_base64 (type: {type(csv_data_base64).__name__}, length: {len(csv_data_base64)})")

        # Check if the input is actually base64 encoded
        is_base64 = True
        try:
            # Check if the string contains only valid base64 characters
            if not all(c in 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=' for c in csv_data_base64):
                is_base64 = False
                logger.warning("CSV Analysis: Input contains non-base64 characters, treating as raw CSV")
        except Exception as e:
            is_base64 = False
            logger.warning(f"CSV Analysis: Error checking base64 encoding: {str(e)}")

        csv_text = ""
        if is_base64:
            try:
                # Decode base64 CSV data
                logger.info("CSV Analysis: Attempting to decode base64 data")
                csv_bytes = base64.b64decode(csv_data_base64)
                csv_text = csv_bytes.decode('utf-8')
                logger.info(f"CSV Analysis: Successfully decoded base64 data (decoded length: {len(csv_text)})")

                # Log a sample of the decoded data
                sample = csv_text[:100] + "..." if len(csv_text) > 100 else csv_text
                logger.info(f"CSV Analysis: Sample of decoded data: {sample}")
            except Exception as e:
                logger.error(f"CSV Analysis: Failed to decode base64 data: {str(e)}")
                # If decoding fails, try using the input directly as CSV
                csv_text = csv_data_base64
                logger.info("CSV Analysis: Falling back to using input directly as CSV")
        else:
            # Use the input directly as CSV
            csv_text = csv_data_base64
            logger.info("CSV Analysis: Using input directly as CSV (not base64 encoded)")

        # Parse CSV to transactions
        logger.info("CSV Analysis: Parsing CSV text to transactions")
        transactions = parse_csv_to_transactions(csv_text)

        # Log the results
        logger.info(f"CSV Analysis: Extracted {len(transactions)} transactions from CSV data")
        if transactions:
            # Log details of the first transaction
            first_txn = transactions[0]
            logger.info(f"CSV Analysis: First transaction fields: {list(first_txn.keys())}")

            # Check if we need to rename fields
            field_mapping = {}
            if 'type' in first_txn and 'txn_type' not in first_txn:
                field_mapping['type'] = 'txn_type'
                logger.info("CSV Analysis: Need to rename 'type' to 'txn_type'")

            if 'remarks' in first_txn and 'receiver_info' not in first_txn:
                field_mapping['remarks'] = 'receiver_info'
                logger.info("CSV Analysis: Need to rename 'remarks' to 'receiver_info'")

            # Apply field mapping if needed
            if field_mapping:
                logger.info(f"CSV Analysis: Applying field mapping: {field_mapping}")
                for txn in transactions:
                    for old_field, new_field in field_mapping.items():
                        if old_field in txn:
                            txn[new_field] = txn[old_field]

                # Log the updated first transaction
                if transactions:
                    logger.info(f"CSV Analysis: Updated first transaction fields: {list(transactions[0].keys())}")

            # Log sample data from first transaction
            if transactions:
                first_txn = transactions[0]
                logger.info(f"CSV Analysis: First transaction sample data: sender={first_txn.get('sender_account', 'N/A')}, receiver={first_txn.get('receiver_account', 'N/A')}, amount={first_txn.get('amount', 'N/A')}, txn_type={first_txn.get('txn_type', 'N/A')}, receiver_info={first_txn.get('receiver_info', 'N/A')}")

        return transactions
    except Exception as e:
        logger.error(f"CSV Analysis: Error extracting transactions from CSV: {str(e)}", exc_info=True)
        return []
