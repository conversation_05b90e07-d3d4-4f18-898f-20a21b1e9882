import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';

// Import our modular components
import { TransactionGraphVisualizer } from '../components/graph-visualization';

import { useAlert } from '../context/TailwindAlertContext';
import { useComplaintData } from '../hooks/useComplaintData';
import { FiFileText } from 'react-icons/fi';

// Main component
const GraphVisualization: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { showError } = useAlert();
  const { data: complaintData, loading, error } = useComplaintData(id as string, true);

  // State to store processed transactions and metadata
  const [graphData, setGraphData] = useState<{ transactions: any[], metadata: any } | null>(null);

  useEffect(() => {
    if (!complaintData) return;

    try {
      console.log('Processing complaint data for graph visualization', complaintData);

      // Extract transactions and metadata
      let transactions: any[] = [];
      let metadata: any = {};

      // Case 1: We have transactions and metadata directly
      if (complaintData.transactions && complaintData.metadata) {
        console.log('Using Case 1: Direct transactions and metadata');
        transactions = complaintData.transactions;
        metadata = complaintData.metadata;
      }
      // Case 2: We have graph_data already generated
      else if (complaintData.graph_data) {
        console.log('Using Case 2: Pre-generated graph_data', complaintData.graph_data);
        let graphDataObj = complaintData.graph_data;
        if (typeof graphDataObj === 'string') {
          try {
            graphDataObj = JSON.parse(graphDataObj);
            console.log('Parsed graph_data from string:', graphDataObj);
          } catch (e) {
            console.error('Failed to parse graph_data JSON:', e);
            showError('Invalid graph data format');
            return;
          }
        }

        if (graphDataObj.transactions && graphDataObj.metadata) {
          transactions = graphDataObj.transactions;
          metadata = graphDataObj.metadata;
          console.log('Extracted from graph_data:', {
            transactionCount: transactions.length,
            metadata
          });
        } else {
          console.error('graph_data missing transactions or metadata:', graphDataObj);
        }
      }
      // Case 3: We have raw data
      else if (complaintData.data) {
        console.log('Using Case 3: Raw data');
        const rawData = complaintData.data;

        if (rawData.transactions && Array.isArray(rawData.transactions)) {
          transactions = rawData.transactions;
        }

        if (rawData.metadata) {
          metadata = rawData.metadata;
        } else {
          metadata = {
            complaint_number: rawData.complaint_number || id || 'Unknown',
            complainant_name: rawData.complainant_name || 'Unknown',
            date_of_complaint: rawData.date || '',
            total_amount: rawData.amount || '0'
          };
        }
        console.log('Extracted from raw data:', {
          transactionCount: transactions.length,
          metadata
        });
      } else {
        console.error('No recognizable data structure found in complaintData:', complaintData);
      }

      if (transactions.length === 0) {
        console.error('No transactions found in any data source');
        showError('No transaction data found');
        return;
      }

      console.log(`Processing ${transactions.length} transactions for graph`);
      console.log('Sample transaction:', transactions[0]);

      // Set the graph data for the TransactionGraphVisualizer
      setGraphData({ transactions, metadata });

    } catch (error) {
      console.error('Error processing graph data:', error);
      showError('Failed to process graph data: ' + (error instanceof Error ? error.message : String(error)));
    }
  }, [complaintData, id, showError]);

  return (
    <div className="h-screen w-full flex flex-col overflow-hidden">
      {/* Navigation buttons outside the graph */}
      <div className="bg-white p-3 border-b border-gray-200 flex flex-col sm:flex-row justify-between gap-3">
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
          <h1 className="text-lg sm:text-xl font-semibold">
            Transaction Flow Visualization
            {complaintData?.metadata?.complaint_number && ` - ${complaintData.metadata.complaint_number}`}
          </h1>
        </div>
        <div className="flex flex-wrap gap-2">
          <button
            className="px-3 py-1.5 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors flex items-center gap-2 text-sm"
            onClick={() => navigate(`/complaint/${id}`)}
          >
            <FiFileText className="w-3.5 h-3.5" />
            Back to Complaint
          </button>

          <button
            className="px-3 py-1.5 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors flex items-center gap-2 text-sm"
            onClick={() => navigate(`/notice-generation/${id}`)}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="w-3.5 h-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Generate Notice
          </button>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-full">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : error ? (
        <div className="p-6">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-red-800">
            <p>{error}</p>
          </div>
        </div>
      ) : !graphData ? (
        <div className="flex justify-center items-center h-full">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-yellow-800">
            <p>No graph data available</p>
          </div>
        </div>
      ) : (
        <div className="flex-1 relative react-flow-wrapper" style={{ height: 'calc(100vh - 60px)', width: '100%' }}>
          {/* Use the TransactionGraphVisualizer component */}
          <TransactionGraphVisualizer
            transactions={graphData.transactions}
            metadata={graphData.metadata}
          />
        </div>
      )}
    </div>
  );
};

export default GraphVisualization;
