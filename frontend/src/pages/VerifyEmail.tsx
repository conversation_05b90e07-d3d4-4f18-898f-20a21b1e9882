import React, { useEffect, useState } from 'react';
import { usePara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { API_BASE_URL } from '../config';
import { useAlert } from '../context/TailwindAlertContext';
import { useAuth } from '../context/AuthContext';
import ResendVerificationButton from '../components/ResendVerificationButton';
import { FaCheckCircle, FaExclamationCircle } from 'react-icons/fa';

const VerifyEmail: React.FC = () => {
  const { token } = useParams<{ token: string }>();
  const [verifying, setVerifying] = useState(true);
  const [verified, setVerified] = useState(false);
  const [error, setError] = useState('');
  const [retryCount, setRetryCount] = useState(0);
  const [redirectCountdown, setRedirectCountdown] = useState(5);
  const { showSuccess, showError } = useAlert();
  const { refreshUserData } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    const verifyEmail = async () => {
      if (!token) {
        setVerifying(false);
        setError('No verification token provided');
        return;
      }

      try {
        console.log(`Attempting to verify email with token: ${token.substring(0, 10)}... (Attempt ${retryCount + 1}/3)`);

        // Use fetch instead of axios to avoid CORS issues with credentials
        const response = await fetch(`${API_BASE_URL}/auth/verify-email/${token}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          mode: 'cors', // Explicitly request CORS
          cache: 'no-cache', // Don't cache the response
        });

        console.log(`Verification response status: ${response.status}`);

        if (response.ok) {
          let data;
          try {
            data = await response.json();
            console.log('Verification response data:', data);
          } catch (e) {
            console.warn('Could not parse JSON response, but status was OK');
            data = { message: 'Email verified successfully!' };
          }

          setVerified(true);
          showSuccess(data.message || 'Email verified successfully!');

          // Try to refresh user data in case they're already logged in
          try {
            await refreshUserData();
          } catch (e) {
            console.log('User not logged in yet, refresh not needed');
          }

          // Start countdown for automatic redirect
          const countdownInterval = setInterval(() => {
            setRedirectCountdown(prev => {
              if (prev <= 1) {
                clearInterval(countdownInterval);
                navigate('/login', { replace: true });
                return 0;
              }
              return prev - 1;
            });
          }, 1000);

          // Clear interval on component unmount
          return () => clearInterval(countdownInterval);
        } else {
          let errorMessage = 'Failed to verify email';
          try {
            const data = await response.json();
            console.error('Error response data:', data);
            errorMessage = data.detail || errorMessage;
          } catch (jsonError) {
            // If we can't parse JSON, use the status text
            console.error('Could not parse error JSON:', jsonError);
            errorMessage = `${errorMessage}: ${response.statusText}`;
          }
          throw new Error(errorMessage);
        }
      } catch (error: any) {
        console.error('Verification error:', error);

        // Only show error and stop verifying after all retries are exhausted
        if (retryCount >= 2) {
          setError(error.message || 'Failed to verify email. The link may be invalid or expired.');
          showError('Email verification failed. Please try again or request a new verification link.');
          setVerifying(false);
        } else {
          // Retry after a short delay
          console.log(`Retrying verification in 1 second (attempt ${retryCount + 1}/3)`);
          setTimeout(() => {
            setRetryCount(prev => prev + 1);
          }, 1000);
        }
        return;
      }

    };

    verifyEmail();
  }, [token, showSuccess, showError, retryCount]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-3 bg-gray-100">
      <div className="p-6 bg-white rounded-lg shadow-md flex flex-col items-center max-w-md w-full">
        <h1 className="text-2xl font-bold mb-4">
          Email Verification
        </h1>

        {verifying ? (
          <div className="text-center my-8">
            <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4">
              Verifying your email{retryCount > 0 ? ` (attempt ${retryCount + 1}/3)` : ''}...
            </p>
          </div>
        ) : verified ? (
          <div className="text-center my-8">
            <FaCheckCircle className="text-green-500 text-5xl mx-auto" />
            <h2 className="text-xl font-semibold mt-4">
              Your email has been verified successfully!
            </h2>
            <p className="text-gray-600 mt-2">
              Redirecting to login page in {redirectCountdown} seconds...
            </p>
            <Link
              to="/login"
              className="mt-6 inline-block px-4 py-2 bg-blue-600 text-white font-medium rounded hover:bg-blue-700 transition-colors duration-300"
            >
              Go to Login Now
            </Link>
          </div>
        ) : (
          <div className="text-center my-8">
            <FaExclamationCircle className="text-red-500 text-5xl mx-auto" />
            <h2 className="text-xl font-semibold mt-4">
              Verification Failed
            </h2>
            <p className="text-gray-600 mt-2">
              {error}
            </p>
            <div className="mt-6 flex flex-col gap-4">
              <ResendVerificationButton />
              <Link
                to="/login"
                className="inline-block px-4 py-2 border border-blue-600 text-blue-600 font-medium rounded hover:bg-blue-50 transition-colors duration-300"
              >
                Return to Login
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default VerifyEmail;
