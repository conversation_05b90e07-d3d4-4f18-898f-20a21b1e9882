/**
 * Debug utilities for troubleshooting React Query mutations and data flow
 */

// Enable debug mode by setting this to true
const DEBUG_ENABLED = false;

export const debugLog = (category: string, message: string, data?: any) => {
  if (!DEBUG_ENABLED) return;

  const timestamp = new Date().toISOString();
  const prefix = `[${timestamp}] [${category}]`;

  if (data) {
    console.group(`${prefix} ${message}`);
    console.log('Data:', data);
    console.groupEnd();
  } else {
    console.log(`${prefix} ${message}`);
  }
};

export const debugMutation = (mutationName: string, variables: any, result?: any, error?: any) => {
  if (!DEBUG_ENABLED) return;

  const timestamp = new Date().toISOString();
  console.group(`[${timestamp}] [MUTATION] ${mutationName}`);

  console.log('Variables:', variables);

  if (result) {
    console.log('✅ Success Result:', result);
  }

  if (error) {
    console.error('❌ Error:', error);
  }

  console.groupEnd();
};

export const debugApiCall = (method: string, url: string, data?: any, response?: any, error?: any) => {
  if (!DEBUG_ENABLED) return;

  const timestamp = new Date().toISOString();
  console.group(`[${timestamp}] [API] ${method.toUpperCase()} ${url}`);

  if (data) {
    console.log('Request Data:', data);
  }

  if (response) {
    console.log('✅ Response:', response);
  }

  if (error) {
    console.error('❌ Error:', error);
    if (error.response) {
      console.error('Error Response:', error.response);
    }
  }

  console.groupEnd();
};

export const debugQueryCache = (queryKey: any, data: any) => {
  if (!DEBUG_ENABLED) return;

  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] [CACHE] Query Key:`, queryKey, 'Data:', data);
};

// Helper to log React Query state changes
export const debugQueryState = (queryKey: any, state: any) => {
  if (!DEBUG_ENABLED) return;

  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] [QUERY_STATE]`, {
    queryKey,
    isLoading: state.isLoading,
    isFetching: state.isFetching,
    isError: state.isError,
    error: state.error?.message,
    dataUpdatedAt: state.dataUpdatedAt,
    status: state.status
  });
};
