import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useLocation } from 'react-router-dom';

// Define the layout configuration interface
export interface LayoutConfig {
  fullWidth?: boolean;
  hideSidebar?: boolean;
  hideHeader?: boolean;
  customPadding?: string;
  customBackground?: string;
  customLeftMargin?: string;
}

// Define the context interface
interface LayoutContextType {
  layoutConfig: LayoutConfig;
  setLayoutConfig: (config: LayoutConfig) => void;
  resetLayout: () => void;
  updateLayout: (config: Partial<LayoutConfig>) => void;
}

// Create the context with default values
const LayoutContext = createContext<LayoutContextType>({
  layoutConfig: {},
  setLayoutConfig: () => {},
  resetLayout: () => {},
  updateLayout: () => {},
});

// Default layout configuration
const defaultLayoutConfig: LayoutConfig = {
  fullWidth: false,
  hideSidebar: false,
  hideHeader: false,
  customPadding: undefined,
  customBackground: undefined,
  customLeftMargin: undefined,
};

// Store persistent layout configurations for specific routes
interface RouteLayoutMap {
  [routePath: string]: LayoutConfig;
}

// Provider component
interface LayoutProviderProps {
  children: ReactNode;
}

export const LayoutProvider: React.FC<LayoutProviderProps> = ({ children }) => {
  const [layoutConfig, setLayoutConfig] = useState<LayoutConfig>(defaultLayoutConfig);
  const [persistentLayouts] = useState<RouteLayoutMap>({
    // Define routes that should have persistent layout settings
    '/complaint/': {
      customPadding: '0 2rem',
      customLeftMargin: '3rem' // Specific left margin for complaint detail pages
    },
    '/complaints/': {
      customPadding: '0 2rem',
      customLeftMargin: '3rem' // Specific left margin for complaint detail pages
    }
  });
  const location = useLocation();

  // Reset to default layout when route changes
  useEffect(() => {
    resetLayout();
  }, [location.pathname]);

  // Reset to default layout
  const resetLayout = () => {
    setLayoutConfig(defaultLayoutConfig);
  };

  // Update only specific layout properties
  const updateLayout = (config: Partial<LayoutConfig>) => {
    setLayoutConfig(prevConfig => ({
      ...prevConfig,
      ...config,
    }));
  };

  return (
    <LayoutContext.Provider
      value={{
        layoutConfig,
        setLayoutConfig,
        resetLayout,
        updateLayout,
      }}
    >
      {children}
    </LayoutContext.Provider>
  );
};

// Custom hook to use the layout context
export const useLayout = () => {
  const context = useContext(LayoutContext);
  if (!context) {
    throw new Error('useLayout must be used within a LayoutProvider');
  }
  return context;
};

// HOC to set layout configuration for a component
export const withLayout = (
  Component: React.ComponentType<any>,
  layoutConfig: LayoutConfig
) => {
  const WithLayoutComponent = (props: any) => {
    const { updateLayout } = useLayout();

    // Set layout config only when component mounts
    // This prevents infinite loops from location changes
    useEffect(() => {
      updateLayout(layoutConfig);
    }, []);

    return <Component {...props} />;
  };

  // Set display name for debugging
  WithLayoutComponent.displayName = `WithLayout(${Component.displayName || Component.name || 'Component'})`;

  return WithLayoutComponent;
};

// Hook to set layout config in functional components
export const useLayoutEffect = (config: LayoutConfig) => {
  const { updateLayout } = useLayout();

  useEffect(() => {
    updateLayout(config);
  }, []);
};

export default LayoutContext;
