import logging
import os
import asyncio
from fastapi import <PERSON><PERSON><PERSON>, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from starlette.middleware.sessions import SessionMiddleware
from middleware.robust_session import RobustSessionMiddleware
from middleware.robust_csrf import RobustCSRFMiddleware
from starlette.middleware.httpsredirect import HTTPSRedirectMiddleware
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from dotenv import load_dotenv
from contextlib import asynccontextmanager
from routes import complaints, extraction, user_templates, notices, reference_data
from routes.simple_data_upload import router as data_upload_router
from routes.auth import router as auth_router
from routes.user_subscription import router as subscription_router
from routes.security import router as security_router
from routes.analysis import router as analysis_router
from routes.health import router as health_router
# Import database functions when needed to avoid circular imports
from services.auth_service import SECRET_KEY
# Import cleanup_service functions when needed to avoid circular imports
from middleware.logging_middleware import RequestLoggingMiddleware
from middleware.security_middleware import SecurityHeadersMiddleware
from middleware.rate_limit_middleware import RateLimitMiddleware
# Removed CSRFMiddleware import as we're using RobustCSRFMiddleware instead
from pathlib import Path
from utils.encoders import custom_jsonable_encoder
import fastapi.encoders

# Setup logging
log_level = os.getenv("LOG_LEVEL", "DEBUG")  # Set default to DEBUG for more detailed logs
log_dir = os.path.join(os.path.dirname(__file__), 'logs')
os.makedirs(log_dir, exist_ok=True)  # Create logs directory if it doesn't exist

# Configure basic logging first
logging.basicConfig(
    level=getattr(logging, log_level),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]  # Start with just console logging
)

# Set up log rotation
from utils.log_rotation import setup_log_rotation, cleanup_old_logs

# Clean up old logs first
cleanup_old_logs(log_dir, max_age_days=30)

# Set up log rotation with different settings based on environment
if os.getenv("ENVIRONMENT", "development") == "production":
    # Production: 50MB max file size, 20 backups
    setup_log_rotation(log_dir, max_bytes=52428800, backup_count=20)

    # Use structured logger for production
    from utils.structured_logger import get_structured_logger
    logger = get_structured_logger(__name__)
else:
    # Development: 10MB max file size, 5 backups
    setup_log_rotation(log_dir, max_bytes=10485760, backup_count=5)
    logger = logging.getLogger(__name__)

# Patch FastAPI's jsonable_encoder with our custom version that handles ObjectId
fastapi.encoders.jsonable_encoder = custom_jsonable_encoder
logger.info("Patched FastAPI's jsonable_encoder to handle MongoDB ObjectId")

# Load environment variables from multiple possible locations
env_paths = [
    Path('.env'),                # Current directory
    Path('backend/.env'),        # From project root
    Path(os.path.dirname(__file__) + '/.env')  # Same directory as this file
]

env_loaded = False
for env_path in env_paths:
    if env_path.exists():
        logger.info(f"Loading environment from: {env_path.absolute()}")
        load_dotenv(dotenv_path=env_path, verbose=True)
        env_loaded = True
        break

if not env_loaded:
    logger.warning("No .env file found in any of the expected locations")

# Background task for scheduled cleanup
async def scheduled_cleanup_task():
    """
    Background task that runs cleanup operations periodically
    """
    # Run every 6 hours
    cleanup_interval = 6 * 60 * 60  # 6 hours in seconds

    while True:
        try:
            logger.info("Running scheduled database cleanup")
            # Import cleanup functions here to avoid circular imports
            from services.cleanup_service import run_cleanup_tasks

            # Call the async cleanup function
            cleanup_result = await run_cleanup_tasks()
            if cleanup_result:
                logger.info(f"Scheduled cleanup completed successfully, next run in {cleanup_interval} seconds")
            else:
                logger.warning(f"Scheduled cleanup completed with issues, next run in {cleanup_interval} seconds")
        except Exception as e:
            logger.error(f"Error in scheduled cleanup: {str(e)}", exc_info=True)
            logger.warning(f"Scheduled cleanup completed with issues, next run in {cleanup_interval} seconds")

        # Wait for the next interval
        await asyncio.sleep(cleanup_interval)

# Lifespan context manager for startup/shutdown events
@asynccontextmanager
async def lifespan(_: FastAPI):
    # Startup: Create DB indexes
    logger.info("Starting application...")

    # Debug: Print DATABASE_URL from environment
    db_url = os.getenv("DATABASE_URL")
    logger.info(f"DATABASE_URL from environment: {db_url[:20]}..." if db_url else "DATABASE_URL not set")

    # Check database connection
    try:
        # Import check_connection function here to avoid circular imports
        from database import check_connection

        # Always await the connection check to ensure proper async handling
        connection_result = await check_connection()

        # Process the result
        db_connected = connection_result if isinstance(connection_result, bool) else connection_result.get('connected', False)
        if not db_connected:
            logger.error("Failed to connect to database")
            # In production, fail fast
            if os.getenv("ENVIRONMENT") == "production":
                raise RuntimeError("Cannot start application without database connection")
            else:
                # In development, show warning but continue (for easier local development)
                logger.warning("⚠️ Running with limited functionality - database connection failed")
    except Exception as e:
        logger.error(f"Error checking database connection: {str(e)}")
        db_connected = False
        # In production, fail fast
        if os.getenv("ENVIRONMENT") == "production":
            raise RuntimeError(f"Cannot start application: {str(e)}")
        else:
            # In development, show warning but continue
            logger.warning(f"⚠️ Running with limited functionality - database error: {str(e)}")

    # Create database indexes if connected
    if db_connected:
        # Try to create indexes but continue even if there are errors
        try:
            # Import create_indexes function here to avoid circular imports
            from database import create_indexes

            # Always await the function to ensure proper async handling
            index_result = await create_indexes()

            if not index_result:
                logger.warning("Some database indexes could not be created, but continuing startup")
            else:
                logger.info("All database indexes created successfully")
        except Exception as e:
            logger.error(f"Error creating database indexes: {str(e)}")
            logger.warning("Continuing startup despite index creation error")

        # Start the scheduled cleanup task
        # Only start in production or if explicitly enabled
        if os.getenv("ENVIRONMENT") == "production" or os.getenv("ENABLE_CLEANUP") == "true":
            logger.info("Starting scheduled cleanup task")
            # Start the background task
            asyncio.create_task(scheduled_cleanup_task())
        else:
            logger.info("Scheduled cleanup task disabled in development mode")

        # Run an initial cleanup
        try:
            logger.info("Running initial database cleanup")
            # Import cleanup functions here to avoid circular imports
            from services.cleanup_service import run_cleanup_tasks

            # Call the async cleanup function
            try:
                cleanup_result = await run_cleanup_tasks()
                if cleanup_result:
                    logger.info("Initial cleanup completed successfully")
                else:
                    logger.warning("Initial cleanup completed with issues")
            except Exception as e:
                logger.error(f"Error running initial cleanup: {str(e)}")
                # Continue with startup even if cleanup fails
        except Exception as e:
            logger.error(f"Error in initial cleanup: {str(e)}", exc_info=True)
            logger.warning("Initial cleanup completed with issues")

    yield

    # Shutdown: Close connections
    logger.info("Shutting down application...")

# Create FastAPI app
app = FastAPI(
    title="Cyber Sakha API",
    description="Backend API for Cyber Sakha application",
    version="1.0.0",
    lifespan=lifespan
)

# Update CORS settings
FRONTEND_ORIGIN = os.getenv("FRONTEND_ORIGIN", "http://localhost:3001")
# Add production domain if specified
PRODUCTION_DOMAIN = os.getenv("PRODUCTION_DOMAIN", "https://cybersakha.in")

# In development mode, allow more origins for easier debugging
if os.getenv("ENVIRONMENT", "development") == "development":
    # Allow multiple origins in development mode
    allowed_origins = [
        FRONTEND_ORIGIN,
        "http://127.0.0.1:3001",
        "http://localhost:3001",
        "http://localhost:8080",  # Common webpack port
        "http://127.0.0.1:8080"
    ]
    # Log the allowed origins for debugging
    logger.info(f"CORS allowed origins in development mode: {allowed_origins}")
else:
    # In production, allow the specified frontend origin and the production domain
    allowed_origins = [FRONTEND_ORIGIN, PRODUCTION_DOMAIN]
    # Also allow www subdomain
    if PRODUCTION_DOMAIN.startswith("https://"):
        allowed_origins.append(PRODUCTION_DOMAIN.replace("https://", "https://www."))
    logger.info(f"CORS allowed origins in production mode: {allowed_origins}")

# Add CORS middleware FIRST to ensure CORS headers are applied before any other middleware
# This is critical for handling preflight requests and ensuring CORS headers are present
app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    allow_headers=["*"],
    expose_headers=["Content-Type", "X-CSRF-Token", "Authorization", "X-Requested-With", "Access-Control-Allow-Origin"],
    max_age=3600  # Cache preflight requests for 60 minutes (increased from 10 minutes)
)

# Log CORS configuration for debugging
logger.info(f"CORS middleware configured with origins: {allowed_origins}")
logger.info("CORS middleware configured with credentials allowed")

# Add robust session middleware after CORS but before other middleware
# This ensures that the session is always available for all requests
app.add_middleware(
    RobustSessionMiddleware,
    secret_key=os.getenv("SESSION_SECRET_KEY") or str(SECRET_KEY),
    max_age=60 * 24 * 7 * 60,  # 7 days in seconds
    same_site="lax",  # Prevents CSRF in modern browsers while allowing normal navigation
    https_only=os.getenv("ENVIRONMENT") == "production",  # Secure cookies in production
    path="/",  # Restrict cookies to our domain only
)

# Log session middleware configuration
logger.info("Robust session middleware configured with 7-day session timeout")

# Add security middlewares
ALLOWED_HOSTS = os.getenv("ALLOWED_HOSTS", "localhost,127.0.0.1").split(",")
app.add_middleware(TrustedHostMiddleware, allowed_hosts=ALLOWED_HOSTS)

# Add custom middlewares - order is important!
# First add the request logging middleware
app.add_middleware(RequestLoggingMiddleware)

# Then add security headers middleware
app.add_middleware(SecurityHeadersMiddleware)

# Add robust CSRF middleware - this doesn't rely on session middleware
# so it's more robust and less prone to errors
app.add_middleware(
    RobustCSRFMiddleware,
    secret_key=os.getenv("SESSION_SECRET_KEY") or str(SECRET_KEY),
    cookie_name="csrf_token",
    header_name="X-CSRF-Token",
    cookie_max_age=3600,  # 1 hour
    safe_methods=["GET", "HEAD", "OPTIONS"],
    exempt_paths=[
        "/health",
        "/security/csp-report",  # CSP reports come from browsers and can't include CSRF tokens
        "/users/template",  # Temporarily exempt template uploads until we fix the CSRF issue
        "/csrf/token",
        "/auth/login",
        "/auth/two-step/login",
        "/auth/register",
        "/auth/verify-token"
    ],
    same_site="lax",
    https_only=os.getenv("ENVIRONMENT") == "production",
    path="/"
)

# Log CSRF middleware configuration
logger.info("CSRF middleware configured with double-submit pattern")
logger.info("CSRF cookie is NOT httponly to allow JavaScript to read it for the double-submit pattern")

# Set log level for CSRF middleware to INFO to reduce verbosity in production
csrf_log_level = logging.DEBUG if os.getenv("ENVIRONMENT") == "development" else logging.INFO
logging.getLogger("middleware.robust_csrf").setLevel(csrf_log_level)

# Log middleware setup
logger.info("Middleware setup complete with robust CSRF protection")

# Rate limiting middleware disabled for simplicity
# Uncomment the following code to enable rate limiting if needed
"""
if os.getenv("ENVIRONMENT", "development") == "production":
    # Adjusted limits in production for 500 users
    app.add_middleware(
        RateLimitMiddleware,
        general_rate_limit=120,  # 120 requests per minute for general endpoints (increased for 500 users)
        auth_rate_limit=30,      # 30 requests per minute for auth endpoints (increased for 500 users)
        window_seconds=60,       # 1 minute window
        enable_ip_blocking=True, # Enable IP blocking for repeated violations
        block_duration=1800      # Block for 30 minutes after severe violations
    )
else:
    # Very permissive in development
    app.add_middleware(
        RateLimitMiddleware,
        general_rate_limit=1000,  # 1000 requests per minute for general endpoints
        auth_rate_limit=500,      # 500 requests per minute for auth endpoints
        window_seconds=60,        # 1 minute window
        enable_ip_blocking=False  # Disable IP blocking in development
    )
"""

# Force HTTPS in production - disabled for local development
if os.getenv("ENVIRONMENT", "development") == "production":
    logger.info("Enabling HTTPS redirect middleware for production")
    app.add_middleware(HTTPSRedirectMiddleware)

    # Log production mode startup
    logger.info("Starting Cyber Sakha API in PRODUCTION mode")
    logger.info(f"Using production domain: {PRODUCTION_DOMAIN}")
    logger.info(f"CORS allowed origins: {allowed_origins}")
else:
    logger.info("Starting Cyber Sakha API in DEVELOPMENT mode")

# Include routers
app.include_router(auth_router, prefix="/auth", tags=["Authentication"])
app.include_router(complaints.router, prefix="/complaints", tags=["Complaints"])
app.include_router(extraction.router, prefix="/extraction", tags=["Extraction"])
app.include_router(user_templates.router, tags=["User Templates"])
app.include_router(notices.router, prefix="/notices", tags=["Notices"])
app.include_router(reference_data.router, prefix="/reference", tags=["Reference Data"])
app.include_router(data_upload_router, prefix="/data", tags=["Data Upload"])
app.include_router(subscription_router, tags=["User Subscription"])
app.include_router(security_router, prefix="/security", tags=["Security"])
app.include_router(analysis_router, prefix="/analysis", tags=["Analysis"])

# Include health check router - no prefix to keep endpoints simple
app.include_router(health_router, tags=["Health"])

# Import and include CSRF router
from routes.csrf import router as csrf_router
app.include_router(csrf_router, tags=["CSRF"])

# Import standardized error handling utilities
from utils.error_utils import create_error_response
from utils.cors_utils import add_cors_headers

# Custom exception handlers
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(_: Request, exc: RequestValidationError):
    """
    Handle validation errors with standardized format
    """
    # Clean up validation errors to be more user-friendly
    errors = []
    for error in exc.errors():
        # Extract only necessary information
        errors.append({
            "loc": error.get("loc", []),
            "msg": error.get("msg", "Validation error")
        })

    # Use standardized error response format
    error_response = create_error_response(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        message="Validation error",
        error_type="validation_error",
        details={"errors": errors},
        log_error=True,
        log_level="warning"
    )

    # Create response with CORS headers
    response = JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=error_response
    )

    # Add CORS headers to ensure frontend can read the error
    return add_cors_headers(response, _)

# Add a generic exception handler to prevent exposing sensitive information
@app.exception_handler(Exception)
async def generic_exception_handler(request: Request, exc: Exception):
    """
    Enhanced global exception handler with standardized error responses

    - Provides better error categorization
    - Includes more detailed logging
    - Handles database connection errors gracefully
    - Prevents sensitive information leakage
    """
    # Log the actual error for debugging with traceback
    logger.error(f"Unhandled exception: {str(exc)}", exc_info=True)

    # Get request information for better debugging
    request_id = request.headers.get("X-Request-ID", "unknown")
    path = request.url.path
    method = request.method
    client_ip = request.client.host if request.client else "unknown"
    user_agent = request.headers.get("User-Agent", "unknown")

    # Log detailed request information
    logger.error(
        f"Exception occurred in {method} {path} (Request ID: {request_id}, "
        f"IP: {client_ip}, User-Agent: {user_agent[:50]}{'...' if len(user_agent) > 50 else ''})"
    )

    # Common details to include in all error responses
    common_details = {
        "request_id": request_id,
        "path": path,
        "method": method
    }

    # Determine if this is a known error type that we can provide more specific messaging for
    if isinstance(exc, asyncio.TimeoutError):
        error_response = create_error_response(
            status_code=status.HTTP_504_GATEWAY_TIMEOUT,
            message="The request timed out. Please try again with a smaller dataset or contact support.",
            error_type="timeout_error",
            details=common_details,
            log_error=False  # Already logged above
        )
        response = JSONResponse(
            status_code=status.HTTP_504_GATEWAY_TIMEOUT,
            content=error_response
        )
        return add_cors_headers(response, request)
    elif isinstance(exc, MemoryError):
        error_response = create_error_response(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            message="The request is too large to process. Please try with a smaller dataset.",
            error_type="memory_limit_error",
            details=common_details,
            log_error=False  # Already logged above
        )
        response = JSONResponse(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            content=error_response
        )
        return add_cors_headers(response, request)
    elif "database" in str(exc).lower() or "mongo" in str(exc).lower():
        # Handle database connection errors
        logger.critical(f"Database error: {str(exc)}")
        error_response = create_error_response(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            message="Database service is currently unavailable. Please try again later.",
            error_type="database_error",
            details=common_details,
            log_error=False  # Already logged above
        )
        response = JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content=error_response
        )
        return add_cors_headers(response, request)
    elif "permission" in str(exc).lower() or "access" in str(exc).lower():
        # Handle permission errors
        error_response = create_error_response(
            status_code=status.HTTP_403_FORBIDDEN,
            message="You don't have permission to perform this action.",
            error_type="permission_error",
            details=common_details,
            log_error=False  # Already logged above
        )
        response = JSONResponse(
            status_code=status.HTTP_403_FORBIDDEN,
            content=error_response
        )
        return add_cors_headers(response, request)
    elif "not found" in str(exc).lower() or "does not exist" in str(exc).lower():
        # Handle not found errors
        error_response = create_error_response(
            status_code=status.HTTP_404_NOT_FOUND,
            message="The requested resource was not found.",
            error_type="not_found_error",
            details=common_details,
            log_error=False  # Already logged above
        )
        response = JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content=error_response
        )
        return add_cors_headers(response, request)

    # Return a generic error message for all other exceptions
    error_response = create_error_response(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        message="An unexpected error occurred. Please try again or contact support if the issue persists.",
        error_type="server_error",
        details=common_details,
        log_error=False  # Already logged above
    )
    response = JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=error_response
    )
    return add_cors_headers(response, request)

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint that doesn't require authentication"""
    from datetime import datetime, timezone

    # Check database connection
    db_status = "unknown"
    try:
        from database import check_connection

        # Always await the connection check
        try:
            connection_result = await check_connection()

            # Process the result
            if isinstance(connection_result, bool):
                db_status = "connected" if connection_result else "disconnected"
            elif isinstance(connection_result, dict):
                db_status = "connected" if connection_result.get('connected', False) else "disconnected"
            else:
                db_status = "unknown"
        except Exception as e:
            logger.error(f"Error during database connection check: {str(e)}")
            db_status = "error"
    except Exception as e:
        db_status = "error"
        logger.error(f"Error checking database connection in health check: {str(e)}")

    # Get environment
    environment = os.getenv("ENVIRONMENT", "development")

    # Get version information
    version = "1.0.0"  # Default version

    # Get hostname for debugging
    import socket
    hostname = socket.gethostname()

    # Get additional information for production environment
    additional_info = {}
    if environment == "production":
        additional_info = {
            "domain": os.getenv("PRODUCTION_DOMAIN", "https://cybersakha.in"),
            "allowed_hosts": os.getenv("ALLOWED_HOSTS", "").split(","),
            "hostname": hostname
        }

    return {
        "status": "ok",
        "message": "Cyber Sakha API is running",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "environment": environment,
        "version": version,
        "database_status": db_status,
        "additional_info": additional_info,
        "success": True
    }

# CSP Report endpoint
@app.post("/api/csp-report")
async def csp_report(request: Request):
    """
    Endpoint to receive Content Security Policy violation reports.

    This helps identify and fix CSP issues in the application.
    """
    try:
        report_data = await request.json()

        # Log the CSP violation report
        logger.warning(
            "CSP Violation Report",
            extra={
                "csp_report": report_data,
                "user_agent": request.headers.get("User-Agent"),
                "remote_addr": request.client.host if request.client else "unknown"
            }
        )

        return {"status": "received"}
    except Exception as e:
        logger.error(f"Error processing CSP report: {str(e)}")
        return {"status": "error", "message": "Failed to process report"}

# Database health check endpoint (admin only)
@app.get("/admin/db-health")
async def db_health_check(request: Request):
    """
    Detailed database health check endpoint

    This endpoint provides detailed information about the database connection,
    performance metrics, and server status. It's intended for admin use only.
    """
    from database import get_db_metrics
    from datetime import datetime, timezone
    from fastapi import HTTPException

    # Check for admin authorization
    # In a production environment, this should be properly secured
    # For now, we'll use a simple API key check
    api_key = request.headers.get("X-Admin-API-Key")
    admin_api_key = os.getenv("ADMIN_API_KEY")

    if not api_key or api_key != admin_api_key:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin API key required"
        )

    # Get detailed database metrics
    try:
        metrics = await get_db_metrics()
        return {
            "success": True,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "database_metrics": metrics
        }
    except Exception as e:
        logger.error(f"Error getting database metrics: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
