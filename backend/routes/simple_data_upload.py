from fastapi import APIRouter, UploadFile, File, HTTPException, Depends, BackgroundTasks
from typing import Dict, List
from pydantic import BaseModel
import os
import tempfile
import shutil
import pandas as pd
import logging
from routes.auth import get_current_user
from database import get_db
from motor.motor_asyncio import AsyncIOMotorClient

# Configure logging
logger = logging.getLogger(__name__)

router = APIRouter()

class UploadResponse(BaseModel):
    success: bool
    message: str

# ATM upload functionality has been removed as per requirements

@router.post("/upload/nodal-data", response_model=UploadResponse)
async def upload_nodal_data(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    current_user: dict = Depends(get_current_user),
    db: AsyncIOMotorClient = Depends(get_db)
):
    """
    Simple endpoint to upload Nodal Officer data from Excel file
    """
    # Create temp file
    file_extension = os.path.splitext(file.filename)[1].lower() if file.filename else ".xlsx"
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=file_extension)
    temp_file_path = temp_file.name

    try:
        # Save uploaded file to temp file
        with open(temp_file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)

        # Process file in background
        background_tasks.add_task(
            process_nodal_file,
            temp_file_path,
            db
        )

        return UploadResponse(
            success=True,
            message="Nodal officer data upload started. Processing in background."
        )

    except Exception as e:
        logger.error(f"Error uploading nodal officer data: {str(e)}")
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)
        raise HTTPException(status_code=500, detail=f"Error uploading nodal officer data: {str(e)}")

# ATM file processing functionality has been removed as per requirements

async def process_nodal_file(file_path: str, db: AsyncIOMotorClient):
    """Process an Excel file with Nodal Officer data and insert directly into MongoDB"""
    try:
        # Read Excel file
        logger.info(f"Reading Nodal Officer data from {file_path}")
        df = pd.read_excel(file_path)

        # Process in batches for better performance
        batch_size = 1000
        total_rows = len(df)
        processed = 0

        for i in range(0, total_rows, batch_size):
            batch = df.iloc[i:min(i+batch_size, total_rows)]

            # Convert batch to list of dictionaries
            records = batch.to_dict('records')

            # Clean up NaN values
            for record in records:
                # Replace NaN with None
                for key, value in list(record.items()):
                    if pd.isna(value):
                        record[key] = None

                # Ensure we have a unique identifier
                if not record.get('email') and not record.get('phone') and not record.get('id'):
                    record['id'] = f"NODAL_{i+processed}"

                # Set default values for critical fields
                for field in ['name', 'organization', 'organization_type']:
                    if not record.get(field):
                        record[field] = f"Unknown {field.title()}"

            # Insert batch into MongoDB
            if records:
                await db.nodal_officers.insert_many(records, ordered=False)

            processed += len(records)
            logger.info(f"Processed {processed}/{total_rows} Nodal Officer records")

        logger.info(f"Completed processing {processed} Nodal Officer records")

    except Exception as e:
        logger.error(f"Error processing Nodal Officer file: {str(e)}")

    finally:
        # Clean up the temp file
        if os.path.exists(file_path):
            os.unlink(file_path)
