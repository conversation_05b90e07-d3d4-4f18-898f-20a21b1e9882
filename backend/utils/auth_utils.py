from fastapi import HTTPException
from bson import ObjectId
import logging
from typing import Any, Dict, Optional, Union

# Setup logging
logger = logging.getLogger(__name__)

async def verify_resource_ownership(
    resource_id: str,
    user_id: Optional[Union[str, ObjectId, Any]],
    collection_name: str,
    db,
    id_field: str = "_id",
    user_id_field: str = "user_id",
    raise_exception: bool = True
) -> Dict[str, Any]:
    """
    Verify that a resource belongs to the current user.

    Args:
        resource_id: ID of the resource to check
        user_id: ID of the current user
        collection_name: Name of the MongoDB collection
        db: Database connection
        id_field: Field name for the resource ID (default: "_id")
        user_id_field: Field name for the user ID in the resource (default: "user_id")
        raise_exception: Whether to raise an exception if ownership verification fails

    Returns:
        The resource document if ownership is verified, None otherwise

    Raises:
        HTTPException: If ownership verification fails and raise_exception is True
    """
    # Handle None user_id
    if user_id is None:
        if raise_exception:
            logger.warning(f"Attempted to verify resource ownership with None user_id")
            raise HTTPException(
                status_code=401,
                detail="Authentication required"
            )
        # Return an empty dictionary instead of None to avoid NoneType errors
        return {}

    # Convert user_id to string if it's an ObjectId
    if isinstance(user_id, ObjectId):
        user_id = str(user_id)

    # Try to find the resource
    resource = None

    try:
        # First try with ObjectId for resource_id
        try:
            query = {id_field: ObjectId(resource_id), user_id_field: user_id}
            resource = await db[collection_name].find_one(query)
        except:
            # If ObjectId conversion fails, try with string ID
            query = {id_field: resource_id, user_id_field: user_id}
            resource = await db[collection_name].find_one(query)

        # If resource not found, try alternate ID fields
        if not resource and id_field == "_id":
            # Try with "id" field
            try:
                query = {"id": resource_id, user_id_field: user_id}
                resource = await db[collection_name].find_one(query)
            except:
                pass

        # If still not found, try with other common ID fields
        if not resource and collection_name == "complaints":
            # Try with complaint_number for complaints
            query = {"complaint_number": resource_id, user_id_field: user_id}
            resource = await db[collection_name].find_one(query)

    except Exception as e:
        logger.error(f"Error verifying resource ownership: {str(e)}")
        if raise_exception:
            raise HTTPException(
                status_code=500,
                detail="Error verifying resource ownership"
            )
        # Return an empty dictionary instead of None to avoid NoneType errors
        return {}

    # If resource not found or doesn't belong to user
    if not resource:
        if raise_exception:
            logger.warning(
                f"Unauthorized access attempt to {collection_name}/{resource_id} "
                f"by user {user_id}"
            )
            raise HTTPException(
                status_code=403,
                detail="You don't have permission to access this resource"
            )
        # Return an empty dictionary instead of None to avoid NoneType errors
        return {}

    return resource
