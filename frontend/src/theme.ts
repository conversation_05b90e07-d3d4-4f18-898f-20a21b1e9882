import { createTheme, alpha } from '@mui/material/styles';

// Create a theme instance with cyberpunk-inspired aesthetics
const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#00AAFF', // Electric Blue (main)
      light: '#00DDFF', // <PERSON>an (hover)
      dark: '#0088FF', // Medium Blue
      contrastText: '#FFFFFF',
    },
    secondary: {
      main: '#FF00FF', // Neon Magenta (main)
      light: '#FF5CFF', // Neon Magenta (hover)
      dark: '#CC00CC', // Dark Magenta
      contrastText: '#FFFFFF',
    },
    error: {
      main: '#FF0066', // Neon Pink
      light: '#FF4D94', // Light Neon Pink
      dark: '#CC0052', // Dark Neon Pink
      contrastText: '#FFFFFF',
    },
    info: {
      main: '#00DDFF', // Bright Cyan
      light: '#66EEFF', // Light Cyan
      dark: '#00AACC', // Dark Cyan
      contrastText: '#FFFFFF',
    },
    success: {
      main: '#00FF88', // Neon Green
      light: '#66FFBB', // Light Neon Green
      dark: '#00CC66', // Dark Neon Green
      contrastText: '#FFFFFF',
    },
    warning: {
      main: '#FF9900', // Neon Orange
      light: '#FFCC66', // Light Neon Orange
      dark: '#CC7A00', // Dark Neon Orange
      contrastText: '#FFFFFF',
    },
    background: {
      default: '#F8FAFF', // Off-White with Blue Tint (light mode)
      paper: '#FFFFFF', // White (light mode)
    },
    text: {
      primary: '#0A0A1A', // Near-black with blue tint
      secondary: '#4A5568', // Dark Gray
      disabled: '#A0AEC0',
    },
  },
  typography: {
    fontFamily: '"Inter", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    h1: {
      fontWeight: 700,
      letterSpacing: '-0.025em',
      lineHeight: 1.2,
    },
    h2: {
      fontWeight: 700,
      letterSpacing: '-0.025em',
      lineHeight: 1.3,
    },
    h3: {
      fontWeight: 600,
      letterSpacing: '-0.015em',
      lineHeight: 1.4,
    },
    h4: {
      fontWeight: 600,
      letterSpacing: '-0.015em',
      lineHeight: 1.4,
    },
    h5: {
      fontWeight: 600,
      lineHeight: 1.5,
    },
    h6: {
      fontWeight: 600,
      lineHeight: 1.5,
    },
    subtitle1: {
      letterSpacing: '-0.011em',
      fontWeight: 500,
    },
    subtitle2: {
      letterSpacing: '-0.011em',
      fontWeight: 500,
    },
    body1: {
      letterSpacing: '-0.011em',
      lineHeight: 1.6,
    },
    body2: {
      letterSpacing: '-0.011em',
      lineHeight: 1.6,
    },
    button: {
      textTransform: 'none',
      fontWeight: 500,
      letterSpacing: '-0.011em',
    },
  },
  shape: {
    borderRadius: 12,
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        body: {
          scrollbarWidth: 'thin',
          '&::-webkit-scrollbar': {
            width: '8px',
            height: '8px',
          },
          '&::-webkit-scrollbar-track': {
            background: '#f1f1f1',
          },
          '&::-webkit-scrollbar-thumb': {
            background: '#888',
            borderRadius: '4px',
          },
          '&::-webkit-scrollbar-thumb:hover': {
            background: '#555',
          },
          '.dark &::-webkit-scrollbar-track': {
            background: '#1E293B',
          },
          '.dark &::-webkit-scrollbar-thumb': {
            background: '#475569',
          },
          '.dark &::-webkit-scrollbar-thumb:hover': {
            background: '#64748B',
          },
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          textTransform: 'none',
          fontWeight: 500,
          transition: 'all 0.2s ease-in-out',
          '&:hover': {
            transform: 'translateY(-2px)',
          },
        },
        contained: {
          boxShadow: 'none',
          '&:hover': {
            boxShadow: '0 4px 10px rgba(0, 0, 0, 0.15)',
          },
          '&.Mui-disabled': {
            backgroundColor: alpha('#00AAFF', 0.12),
            color: alpha('#00AAFF', 0.3),
          },
        },
        containedPrimary: {
          background: 'linear-gradient(135deg, #00AAFF 0%, #0088FF 100%)',
          '&:hover': {
            background: 'linear-gradient(135deg, #00DDFF 0%, #00AAFF 100%)',
          },
        },
        containedSecondary: {
          background: 'linear-gradient(135deg, #FF00FF 0%, #CC00CC 100%)',
          '&:hover': {
            background: 'linear-gradient(135deg, #FF5CFF 0%, #FF00FF 100%)',
          },
        },
        outlined: {
          borderWidth: 2,
          '&:hover': {
            borderWidth: 2,
          },
        },
        outlinedPrimary: {
          borderColor: '#00AAFF',
          '&:hover': {
            borderColor: '#00DDFF',
            backgroundColor: alpha('#00AAFF', 0.05),
          },
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 16,
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
          overflow: 'hidden',
          transition: 'transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out',
          '&:hover': {
            transform: 'translateY(-5px)',
            boxShadow: '0 8px 30px rgba(0, 0, 0, 0.12)',
          },
          '.dark &': {
            backgroundColor: alpha('#1E293B', 0.8),
            backdropFilter: 'blur(10px)',
            borderColor: alpha('#334155', 0.5),
          },
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          backgroundImage: 'none',
        },
        elevation1: {
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.1)',
        },
        elevation2: {
          boxShadow: '0 3px 6px rgba(0, 0, 0, 0.08), 0 3px 6px rgba(0, 0, 0, 0.12)',
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: 8,
            transition: 'box-shadow 0.2s ease-in-out',
            '&:hover': {
              boxShadow: '0 0 0 1px rgba(0, 170, 255, 0.2)',
            },
            '&.Mui-focused': {
              boxShadow: '0 0 0 2px rgba(0, 170, 255, 0.3)',
            },
          },
        },
      },
    },
    MuiSwitch: {
      styleOverrides: {
        root: {
          width: 42,
          height: 26,
          padding: 0,
          margin: 8,
        },
        switchBase: {
          padding: 1,
          '&.Mui-checked': {
            transform: 'translateX(16px)',
            color: '#fff',
            '& + .MuiSwitch-track': {
              backgroundColor: '#00AAFF',
              opacity: 1,
              border: 'none',
            },
          },
          '&.Mui-focusVisible .MuiSwitch-thumb': {
            color: '#00AAFF',
            border: '6px solid #fff',
          },
        },
        thumb: {
          width: 24,
          height: 24,
        },
        track: {
          borderRadius: 26 / 2,
          backgroundColor: '#E9E9EA',
          opacity: 1,
        },
      },
    },
  },
});

export default theme;
