import React, { useState, useEffect } from 'react';
import { useUser } from '../context/UserContext';
import { useAlert } from '../context/TailwindAlertContext';
import { useThemeContext } from '../context/TailwindThemeContext';
import userService from '../services/userService';
import { useLayout } from '../context/LayoutContext';

// UI Components
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';
import TextField from '../components/ui/TextField';

// Icons
import {
  FiUser,
  FiMail,
  FiBriefcase,
  FiEdit,
  FiSave,
  FiX,
  FiUpload,
  FiFileText,
  FiCheck,
  FiSettings,
  FiHelpCircle,
  FiCreditCard,
  FiCalendar,
  FiPackage
} from 'react-icons/fi';
import { Link } from 'react-router-dom';

const ProfilePage: React.FC = () => {
  const { userInfo, fetchUserInfo } = useUser();
  const { showSuccess, showError } = useAlert();
  const { isDark } = useThemeContext();
  const { setLayoutConfig } = useLayout();

  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    designation: '',
    organization: '',
  });

  // Template upload state
  const [templateFile, setTemplateFile] = useState<File | null>(null);
  const [uploadingTemplate, setUploadingTemplate] = useState(false);

  // Help dialog state
  const [helpDialogOpen, setHelpDialogOpen] = useState(false);

  // Subscription details state
  const [subscriptionDetails, setSubscriptionDetails] = useState({
    planType: '',
    status: '',
    startDate: '',
    endDate: '',
    billingCycle: ''
  });
  const [loadingSubscription, setLoadingSubscription] = useState(false);

  // Set layout configuration
  useEffect(() => {
    setLayoutConfig({
      hideSidebar: false,
      fullWidth: false,
      customPadding: '1rem'
    });
  }, [setLayoutConfig]);

  // Fetch user data on mount
  useEffect(() => {
    if (!userInfo) {
      fetchUserInfo();
    }
  }, [userInfo, fetchUserInfo]);

  useEffect(() => {
    if (userInfo) {
      console.log('UserInfo received:', userInfo); // Debug log
      const newFormData = {
        firstName: userInfo.first_name || '',
        lastName: userInfo.last_name || '',
        email: userInfo.email || '',
        designation: userInfo.designation || '',
        organization: userInfo.organization || '',
      };
      console.log('Setting form data:', newFormData); // Debug log
      setFormData(newFormData);

      // Fetch subscription details
      fetchSubscriptionDetails();
    }
  }, [userInfo]);

  // Function to fetch subscription details
  const fetchSubscriptionDetails = async () => {
    if (!userInfo) return;

    try {
      setLoadingSubscription(true);
      const response = await userService.getSubscriptionDetails();

      setSubscriptionDetails({
        planType: response.planType || (userInfo.paid ? 'Premium' : 'Basic'),
        status: response.status || (userInfo.paid ? 'Active' : 'Free'),
        startDate: response.startDate || '',
        endDate: response.subscriptionEndDate || userInfo.subscription_expires || '',
        billingCycle: response.billingCycle || 'Annual'
      });
    } catch (error) {
      console.error('Error fetching subscription details:', error);
      // Set default values based on userInfo if API fails
      setSubscriptionDetails({
        planType: userInfo.paid ? 'Premium' : 'Basic',
        status: userInfo.paid ? 'Active' : 'Free',
        startDate: '',
        endDate: userInfo.subscription_expires || '',
        billingCycle: 'Annual'
      });
    } finally {
      setLoadingSubscription(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleCancel = () => {
    // Reset form data to original values
    if (userInfo) {
      setFormData({
        firstName: userInfo.first_name || '',
        lastName: userInfo.last_name || '',
        email: userInfo.email || '',
        designation: userInfo.designation || '',
        organization: userInfo.organization || '',
      });
    }
    setIsEditing(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setLoading(true);

      // Create the update payload with the correct property names
      const updatePayload = {
        first_name: formData.firstName,
        last_name: formData.lastName,
        designation: formData.designation,
        organization: formData.organization || '', // Ensure organization is included
      };

      console.log('Updating profile with payload:', updatePayload); // Debug log

      const response = await userService.updateUserProfile(updatePayload);
      console.log('Profile update response:', response); // Debug log

      await fetchUserInfo(); // Refresh user data
      showSuccess('Profile updated successfully');
      setIsEditing(false);
    } catch (error) {
      console.error('Error updating profile:', error);
      showError('Failed to update profile');
    } finally {
      setLoading(false);
    }
  };

  const handleTemplateFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setTemplateFile(e.target.files[0]);
    }
  };

  const handleTemplateUpload = async () => {
    if (!templateFile) {
      showError('Please select a template file');
      return;
    }

    try {
      setUploadingTemplate(true);

      // Validate file type
      if (!templateFile.name.toLowerCase().endsWith('.docx')) {
        showError('Only .docx files are allowed');
        return;
      }

      // Validate file size (max 5MB)
      const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
      if (templateFile.size > MAX_FILE_SIZE) {
        showError('File size exceeds the 5MB limit');
        return;
      }

      // Create form data with the template file
      const formData = new FormData();
      formData.append('template', templateFile);

      // Upload the template using the userService
      const response = await userService.uploadTemplate(formData);

      // Refresh user data to update the hasTemplate flag
      await fetchUserInfo();

      showSuccess(response.message || 'Template uploaded successfully');
      setTemplateFile(null);

      // Reset file input
      const fileInput = document.getElementById('template-upload') as HTMLInputElement;
      if (fileInput) fileInput.value = '';
    } catch (error: any) {
      console.error('Error uploading template:', error);

      // Show a more specific error message if available
      const errorMessage = error.message || 'Failed to upload template. Please try again or refresh the page.';
      showError(errorMessage);
    } finally {
      setUploadingTemplate(false);
    }
  };

  if (!userInfo) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className={`max-w-7xl mx-auto p-6 h-[calc(100vh-64px)] ${isDark ? 'cyber-grid-bg' : ''}`}>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold" style={{ color: 'var(--theme-text)' }}>
          <span className={isDark ? 'cyber-text-gradient' : ''}>My Profile</span>
        </h1>
        <Link to="/settings">
          <Button
            variant="outlined"
            size="sm"
            startIcon={<FiSettings />}
          >
            Settings
          </Button>
        </Link>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-12 gap-4">
        {/* Profile Information */}
        <Card className="dark:shadow-glow-blue-sm md:col-span-6">
          <Card.Header className="flex justify-between items-center">
            <h2 className="text-lg font-bold" style={{ color: 'var(--theme-text)' }}>
              <span className={isDark ? 'cyber-text-gradient' : ''}>Profile Information</span>
            </h2>
            {!isEditing ? (
              <Button
                variant="outlined"
                size="sm"
                onClick={handleEdit}
                startIcon={<FiEdit />}
              >
                Edit
              </Button>
            ) : (
              <div className="flex flex-wrap gap-2">
                <Button
                  variant="outlined"
                  size="sm"
                  onClick={handleCancel}
                  startIcon={<FiX />}
                >
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  size="sm"
                  onClick={handleSubmit}
                  loading={loading}
                  startIcon={<FiSave />}
                >
                  Save
                </Button>
              </div>
            )}
          </Card.Header>

          <Card.Content>
            <form onSubmit={handleSubmit} className="space-y-3">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {/* First Name */}
                <div>
                  <TextField
                    label="First Name"
                    name="firstName"
                    value={formData.firstName}
                    onChange={handleChange}
                    disabled={!isEditing}
                    required
                    fullWidth
                    startIcon={<FiUser />}
                    style={{ '--input-icon-spacing': '10px' } as React.CSSProperties}
                  />
                </div>

                {/* Last Name */}
                <div>
                  <TextField
                    label="Last Name"
                    name="lastName"
                    value={formData.lastName}
                    onChange={handleChange}
                    disabled={!isEditing}
                    required
                    fullWidth
                    startIcon={<FiUser />}
                    style={{ '--input-icon-spacing': '10px' } as React.CSSProperties}
                  />
                </div>
              </div>

              {/* Email */}
              <TextField
                label="Email"
                name="email"
                value={formData.email}
                disabled={true}
                fullWidth
                startIcon={<FiMail />}
                helperText="Email cannot be changed"
                style={{ '--input-icon-spacing': '10px' } as React.CSSProperties}
              />

              {/* Designation */}
              <TextField
                label="Designation"
                name="designation"
                value={formData.designation}
                onChange={handleChange}
                disabled={!isEditing}
                fullWidth
                startIcon={<FiBriefcase />}
                style={{ '--input-icon-spacing': '10px' } as React.CSSProperties}
              />

              {/* Organization */}
              <TextField
                label="Organization"
                name="organization"
                value={formData.organization}
                onChange={handleChange}
                disabled={!isEditing}
                fullWidth
                startIcon={<FiBriefcase />}
                style={{ '--input-icon-spacing': '10px' } as React.CSSProperties}
              />
            </form>
          </Card.Content>
        </Card>

        {/* Template Upload */}
        <Card className="dark:shadow-glow-purple-sm md:col-span-6">
          <Card.Header>
            <h2 className="text-lg font-bold" style={{ color: 'var(--theme-text)' }}>
              <span className={isDark ? 'cyber-text-gradient-purple' : ''}>Notice Templates</span>
            </h2>
          </Card.Header>

          <Card.Content>
            <div className="space-y-3">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Upload your organization's letterhead template for generating notices.
              </p>

              <div className="border-2 border-dashed border-gray-300/70 dark:border-[rgba(255,0,255,0.3)] rounded-lg p-4 text-center transition-all duration-300 hover:border-secondary-400 dark:hover:border-secondary-500 hover:bg-gray-50/50 dark:hover:bg-[rgba(255,0,255,0.1)] hover:-translate-y-1 transform dark:shadow-glow-purple-sm">
                <input
                  type="file"
                  id="template-upload"
                  accept=".docx"
                  onChange={handleTemplateFileChange}
                  className="hidden"
                />

                <label htmlFor="template-upload" className="cursor-pointer block w-full">
                  <div className="flex flex-col items-center justify-center space-y-3">
                    <div className="p-3 bg-secondary-100/80 dark:bg-[rgba(255,0,255,0.15)] rounded-full text-secondary-600 dark:text-secondary-400 dark:shadow-glow-purple-sm">
                      <FiUpload className="w-6 h-6" />
                    </div>
                    <span className="text-sm font-medium break-words w-full">
                      {templateFile ? templateFile.name : 'Click to upload template'}
                    </span>
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      .docx format only
                    </span>
                  </div>
                </label>
              </div>

              {templateFile && (
                <div className="flex items-center justify-between gap-2 bg-gray-50/80 dark:bg-[rgba(255,0,255,0.08)] p-3.5 rounded-lg border border-gray-200/50 dark:border-[rgba(255,0,255,0.2)] dark:shadow-glow-purple-sm">
                  <div className="flex items-center min-w-0 flex-1">
                    <FiFileText className="w-4 h-4 text-secondary-500 dark:text-secondary-400 mr-2 flex-shrink-0" />
                    <span className="text-sm truncate">{templateFile.name}</span>
                  </div>
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={handleTemplateUpload}
                    loading={uploadingTemplate}
                    startIcon={<FiCheck />}
                  >
                    Upload
                  </Button>
                </div>
              )}

              <div className="mt-5">
                <h3 className="text-sm font-bold mb-3" style={{ color: 'var(--theme-text)' }}>Current Template</h3>
                {userInfo.has_template ? (
                  <div className="flex items-center justify-between bg-gray-50/80 dark:bg-[rgba(0,255,136,0.08)] p-3.5 rounded-lg border border-gray-200/50 dark:border-[rgba(0,255,136,0.2)] dark:shadow-glow-green-sm">
                    <div className="flex items-center min-w-0 flex-1">
                      <FiFileText className="w-4 h-4 text-success-500 dark:text-success-400 mr-2 flex-shrink-0" />
                      <span className="text-sm truncate" style={{ color: 'var(--theme-text)' }}>Template uploaded</span>
                    </div>
                    <Button
                      variant="success"
                      size="sm"
                      onClick={() => {
                        // Open file dialog to replace template
                        const fileInput = document.getElementById('template-upload') as HTMLInputElement;
                        if (fileInput) fileInput.click();
                      }}
                      startIcon={<FiUpload />}
                    >
                      Replace
                    </Button>
                  </div>
                ) : (
                  <div className="text-sm italic p-3 border border-dashed border-gray-200/50 dark:border-gray-700/50 rounded-lg" style={{ color: 'var(--theme-text)', opacity: 0.7 }}>
                    No template uploaded yet
                  </div>
                )}
              </div>
            </div>
          </Card.Content>
        </Card>

        {/* User Plan Details */}
        <Card className="dark:shadow-glow-green-sm md:col-span-6">
          <Card.Header>
            <h2 className="text-lg font-bold" style={{ color: 'var(--theme-text)' }}>
              <span className={isDark ? 'cyber-text-gradient-green' : ''}>Subscription Plan</span>
            </h2>
          </Card.Header>
          <Card.Content>
            {loadingSubscription ? (
              <div className="flex justify-center items-center h-32">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-success-500"></div>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex items-center p-4 bg-gray-50/80 dark:bg-[rgba(0,255,136,0.08)] rounded-lg border border-gray-200/50 dark:border-[rgba(0,255,136,0.2)] dark:shadow-glow-green-sm">
                  <div className="p-3 bg-success-100/80 dark:bg-[rgba(0,255,136,0.15)] rounded-full text-success-600 dark:text-success-400 mr-4 dark:shadow-glow-green-sm">
                    <FiPackage className="w-5 h-5" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-base font-medium" style={{ color: 'var(--theme-text)' }}>
                      {subscriptionDetails.planType || (userInfo.paid ? 'Premium Plan' : 'Basic Plan')}
                    </h3>
                    <p className="text-sm mt-1" style={{ color: 'var(--theme-text)', opacity: 0.7 }}>
                      {userInfo.paid
                        ? 'Full access to all features including complaint processing and visualization'
                        : 'Limited access to basic features'}
                    </p>
                  </div>
                  <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                    userInfo.paid
                      ? 'bg-success-100 text-success-800 dark:bg-[rgba(0,255,136,0.2)] dark:text-success-400'
                      : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                  }`}>
                    {subscriptionDetails.status || (userInfo.paid ? 'Active' : 'Free')}
                  </div>
                </div>

                {userInfo.paid && (
                  <>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div className="p-3 rounded-lg border border-gray-200/50 dark:border-[rgba(0,255,136,0.2)] bg-white/90 dark:bg-[rgba(16,16,30,0.6)]">
                        <div className="flex items-center mb-1">
                          <FiCalendar className="w-4 h-4 text-success-500 dark:text-success-400 mr-2" />
                          <span className="text-xs font-medium" style={{ color: 'var(--theme-text)', opacity: 0.7 }}>Start Date</span>
                        </div>
                        <p className="text-sm font-medium" style={{ color: 'var(--theme-text)' }}>
                          {subscriptionDetails.startDate
                            ? new Date(subscriptionDetails.startDate).toLocaleDateString()
                            : 'Not available'}
                        </p>
                      </div>

                      <div className="p-3 rounded-lg border border-gray-200/50 dark:border-[rgba(0,255,136,0.2)] bg-white/90 dark:bg-[rgba(16,16,30,0.6)]">
                        <div className="flex items-center mb-1">
                          <FiCalendar className="w-4 h-4 text-success-500 dark:text-success-400 mr-2" />
                          <span className="text-xs font-medium" style={{ color: 'var(--theme-text)', opacity: 0.7 }}>Expiry Date</span>
                        </div>
                        <p className="text-sm font-medium" style={{ color: 'var(--theme-text)' }}>
                          {subscriptionDetails.endDate
                            ? new Date(subscriptionDetails.endDate).toLocaleDateString()
                            : userInfo.subscription_expires
                              ? new Date(userInfo.subscription_expires).toLocaleDateString()
                              : 'Not available'}
                        </p>
                      </div>

                      <div className="p-3 rounded-lg border border-gray-200/50 dark:border-[rgba(0,255,136,0.2)] bg-white/90 dark:bg-[rgba(16,16,30,0.6)]">
                        <div className="flex items-center mb-1">
                          <FiCreditCard className="w-4 h-4 text-success-500 dark:text-success-400 mr-2" />
                          <span className="text-xs font-medium" style={{ color: 'var(--theme-text)', opacity: 0.7 }}>Billing</span>
                        </div>
                        <p className="text-sm font-medium" style={{ color: 'var(--theme-text)' }}>
                          {subscriptionDetails.billingCycle || 'Annual'}
                        </p>
                      </div>
                    </div>

                    <div className="text-xs text-gray-500 dark:text-gray-400 italic">
                      For billing inquiries or plan changes, please contact support.
                    </div>
                  </>
                )}

                {!userInfo.paid && (
                  <div className="mt-4">
                    <Button
                      variant="success"
                      size="sm"
                      className="w-full"
                      onClick={() => window.open('mailto:<EMAIL>?subject=Premium Plan Inquiry', '_blank')}
                    >
                      Upgrade to Premium
                    </Button>
                  </div>
                )}
              </div>
            )}
          </Card.Content>
        </Card>

        {/* Help & Support */}
        <Card className="dark:shadow-glow-blue-sm md:col-span-6">
          <Card.Header>
            <h2 className="text-lg font-bold" style={{ color: 'var(--theme-text)' }}>
              <span className={isDark ? 'cyber-text-gradient' : ''}>Help & Support</span>
            </h2>
          </Card.Header>
          <Card.Content>
            <div className="space-y-4">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Need assistance with Cyber Sakha? Our support team is here to help you.
              </p>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div
                  className="p-4 rounded-lg border border-gray-200/50 dark:border-[rgba(0,170,255,0.2)] bg-white/90 dark:bg-[rgba(16,16,30,0.6)] hover:shadow-md dark:hover:shadow-glow-blue-sm transition-all duration-300 cursor-pointer"
                  onClick={() => setHelpDialogOpen(true)}
                >
                  <div className="flex flex-col items-center text-center">
                    <div className="p-3 bg-primary-100/80 dark:bg-[rgba(0,170,255,0.15)] rounded-full text-primary-600 dark:text-primary-400 mb-3 dark:shadow-glow-blue-sm">
                      <FiHelpCircle className="w-5 h-5" />
                    </div>
                    <h3 className="text-sm font-medium mb-1" style={{ color: 'var(--theme-text)' }}>Contact Support</h3>
                    <p className="text-xs" style={{ color: 'var(--theme-text)', opacity: 0.7 }}>
                      Get in touch with our support team
                    </p>
                  </div>
                </div>

                <a
                  href="mailto:<EMAIL>"
                  className="block p-4 rounded-lg border border-gray-200/50 dark:border-[rgba(0,170,255,0.2)] bg-white/90 dark:bg-[rgba(16,16,30,0.6)] hover:shadow-md dark:hover:shadow-glow-blue-sm transition-all duration-300"
                >
                  <div className="flex flex-col items-center text-center">
                    <div className="p-3 bg-primary-100/80 dark:bg-[rgba(0,170,255,0.15)] rounded-full text-primary-600 dark:text-primary-400 mb-3 dark:shadow-glow-blue-sm">
                      <FiMail className="w-5 h-5" />
                    </div>
                    <h3 className="text-sm font-medium mb-1" style={{ color: 'var(--theme-text)' }}>Email Us</h3>
                    <p className="text-xs" style={{ color: 'var(--theme-text)', opacity: 0.7 }}>
                      <EMAIL>
                    </p>
                  </div>
                </a>
              </div>

              <div className="text-xs italic text-center mt-2" style={{ color: 'var(--theme-text)', opacity: 0.7 }}>
                Our support team is available Monday to Friday, 9 AM to 6 PM IST
              </div>
            </div>
          </Card.Content>
        </Card>
      </div>

      {/* Help Dialog */}
      {helpDialogOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70 backdrop-blur-md animate-fade-in">
          <div className="w-full max-w-md rounded-xl shadow-xl overflow-hidden animate-slide-up bg-white/90 dark:bg-[rgba(16,16,30,0.8)] backdrop-blur-lg border border-gray-100/50 dark:border-[rgba(0,170,255,0.3)] dark:shadow-glow-blue-sm">
            {/* Subtle scan line effect in dark mode */}
            <div className="absolute inset-0 overflow-hidden opacity-10 pointer-events-none dark:block hidden">
              <div className="w-full h-full animate-scan-line bg-gradient-to-b from-transparent via-primary-400/20 to-transparent"></div>
            </div>

            <div className="p-5 relative">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-bold text-gray-900 dark:text-white dark:cyber-text-gradient">Contact Support</h3>
                <button
                  onClick={() => setHelpDialogOpen(false)}
                  className="p-1.5 rounded-full transition-all duration-200 transform hover:scale-110 text-gray-400 hover:text-gray-500 hover:bg-gray-100/80 dark:text-primary-400 dark:hover:text-primary-300 dark:hover:bg-[rgba(0,170,255,0.1)] dark:hover:shadow-glow-blue-sm"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="p-4 bg-primary-50/80 dark:bg-[rgba(0,170,255,0.08)] rounded-lg mb-5 border border-primary-100/50 dark:border-[rgba(0,170,255,0.2)]">
                <div className="flex items-start">
                  <div className="flex-shrink-0 p-3 bg-primary-100/80 dark:bg-[rgba(0,170,255,0.15)] rounded-full mr-3 text-primary-600 dark:text-primary-400 dark:shadow-glow-blue-sm">
                    <FiMail className="w-5 h-5" />
                  </div>
                  <div>
                    <h4 className="text-base font-medium text-gray-900 dark:text-white mb-2">Email Support</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                      For assistance with complaint processing or any other issues, please contact our support team.
                    </p>
                    <a
                      href="mailto:<EMAIL>"
                      className="inline-flex items-center text-sm font-medium text-primary-600 dark:text-primary-400 hover:underline"
                    >
                      <EMAIL>
                    </a>
                  </div>
                </div>
              </div>

              <div className="flex justify-end">
                <Button
                  variant="primary"
                  size="sm"
                  onClick={() => setHelpDialogOpen(false)}
                >
                  Close
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProfilePage;
