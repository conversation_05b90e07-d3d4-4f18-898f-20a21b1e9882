from fastapi import APIRouter, HTTPException, Depends, Query
from typing import Optional
import logging
from routes.auth import get_current_user
from middleware.paid_access_middleware import check_paid_access
from database import get_db
from motor.motor_asyncio import AsyncIOMotorClient
from services.reference_data_service import get_nodal_officers
from models import SearchResponse

# Setup logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter()

# Nodal Officer Search endpoint
@router.get("/nodal-officers", response_model=SearchResponse, dependencies=[Depends(check_paid_access)])
async def search_nodal_officers(
    search: Optional[str] = None,
    organization: Optional[str] = None,
    organization_type: Optional[str] = None,
    state: Optional[str] = None,
    page: int = Query(1, ge=1, description="Page number, starting from 1"),
    limit: int = Query(10, ge=1, le=100, description="Number of items per page"),
    user: dict = Depends(get_current_user),
    db: AsyncIOMotorClient = Depends(get_db)
):
    """
    Search for Nodal Officers with optional filters

    Args:
        search: General search query to match against multiple fields
        organization: Filter by organization name
        organization_type: Filter by organization type (Bank/Wallet/E-commerce)
        state: Filter by state
        page: Page number for pagination
        limit: Number of items per page
        user: Current authenticated user
        db: Database connection

    Returns:
        SearchResponse with Nodal Officer data and pagination info
    """
    try:
        logger.info(f"User {user['email']} searching Nodal Officers with query: {search}")

        # Get Nodal Officer data from service
        result = await get_nodal_officers(
            search_query=search,
            organization=organization,
            organization_type=organization_type,
            state=state,
            page=page,
            limit=limit,
            db=db
        )

        return result
    except Exception as e:
        logger.error(f"Error in Nodal Officer search: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to search Nodal Officers")
