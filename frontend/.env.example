# API Configuration
# For development
VITE_API_BASE_URL=http://localhost:8000
# For production
# VITE_API_BASE_URL=https://api.cybersakha.in

# Environment
VITE_ENV=development  # Set to 'production' for production deployment

# Feature flags
VITE_FEATURE_EMAIL_VERIFICATION=true
VITE_FEATURE_PASSWORD_RESET=true
VITE_FEATURE_TEMPLATE_MANAGEMENT=true

# Logging configuration
VITE_DEBUG_LOGS=true  # Set to false in production
VITE_REMOTE_LOGGING=false  # Set to true in production
VITE_REMOTE_LOG_ENDPOINT=/api/logs

# Performance monitoring
VITE_ENABLE_PERFORMANCE_MONITORING=false  # Set to true in production

# Application info
VITE_APP_VERSION=1.0.0
VITE_APP_NAME=Cyber Sakha

# Security settings
VITE_ENABLE_CSP=true
VITE_SESSION_TIMEOUT_MINUTES=60
