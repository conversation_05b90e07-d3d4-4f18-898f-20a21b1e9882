import React, { useCallback, useEffect } from 'react';
import { ReactFlowInstance } from 'reactflow';
import { jsPDF } from 'jspdf';
import * as htmlToImage from 'html-to-image';
import { useAlert } from '../../context/TailwindAlertContext';
import { useThemeContext } from '../../context/TailwindThemeContext';
import { calculateDynamicLayout } from './DynamicLayoutManager';

// Define nodeTypes and edgeTypes outside the component to prevent React Flow warnings
const nodeTypes = {
  metadata: (props: any) => <div {...props} />, // Replace with your actual metadata node component
  transaction: (props: any) => <div {...props} />, // Replace with your actual transaction node component
};

const edgeTypes = {
  default: (props: any) => <path {...props} />, // Replace with your actual edge component
};

interface GraphExporterProps {
  reactFlowRef: React.RefObject<HTMLDivElement>;
  isHorizontalLayout: boolean;
  reactFlowInstance: ReactFlowInstance | null;
}

/**
 * GraphExporter Component
 * Exports React Flow graph to PDF with high-quality SVG rendering and error handling
 */
const GraphExporter: React.FC<GraphExporterProps> = ({
  reactFlowRef,
  isHorizontalLayout,
  reactFlowInstance,
}) => {
  const { showError, showInfo, showSuccess } = useAlert();
  const { isDark } = useThemeContext();

  // Ensure component is mounted before export
  useEffect(() => {
    if (!reactFlowRef.current || !reactFlowInstance) {
      showError('Graph is not ready for export');
    }
  }, [reactFlowRef, reactFlowInstance, showError]);

  // Ask user for export preferences
  const askExportPreferences = useCallback(() => {
    const modalContainer = document.createElement('div');
    modalContainer.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modalContainer.style.zIndex = '9999';

    modalContainer.innerHTML = `
      <div class="${isDark ? 'bg-gray-800 text-white' : 'bg-white text-gray-900'} rounded-lg shadow-xl p-6 w-96 max-w-full">
        <h3 class="text-lg font-medium mb-4">Export Options</h3>
        <div class="mb-4">
          <label class="block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'} mb-1">Page Layout</label>
          <select id="export-layout" class="w-full border ${isDark ? 'border-gray-600 bg-gray-700 text-white' : 'border-gray-300 bg-white text-gray-800'} rounded-md px-3 py-2">
            <option value="single" selected>Single Page (Entire Graph)</option>
            <option value="multi">Multiple Pages (One Sender Per Page)</option>
          </select>
        </div>
        <div class="flex justify-end gap-3 mt-6">
          <button id="export-cancel" class="px-4 py-2 ${isDark ? 'bg-gray-700 text-white hover:bg-gray-600' : 'bg-gray-200 text-gray-800 hover:bg-gray-300'} rounded-md">Cancel</button>
          <button id="export-confirm" class="px-4 py-2 ${isDark ? 'bg-green-800 text-white hover:bg-green-700' : 'bg-green-600 text-white hover:bg-green-700'} rounded-md">Export</button>
        </div>
      </div>
    `;

    document.body.appendChild(modalContainer);

    return new Promise<{ layout: string } | null>((resolve) => {
      const confirmButton = document.getElementById('export-confirm');
      const cancelButton = document.getElementById('export-cancel');

      const cleanup = () => document.body.removeChild(modalContainer);

      confirmButton?.addEventListener('click', () => {
        const layoutSelect = document.getElementById('export-layout') as HTMLSelectElement;
        const preferences = { layout: layoutSelect?.value || 'single' };
        cleanup();
        resolve(preferences);
      });

      cancelButton?.addEventListener('click', () => {
        cleanup();
        resolve(null);
      });

      modalContainer.addEventListener('click', (e) => {
        if (e.target === modalContainer) {
          cleanup();
          resolve(null);
        }
      });
    });
  }, [isDark]);

  // Wait for SVG edges to render
  const waitForEdges = async (container: HTMLElement, maxWait = 5000): Promise<void> => {
    const start = Date.now();
    return new Promise((resolve) => {
      const checkEdges = () => {
        const edges = container.querySelectorAll('.react-flow__edge path');
        const allRendered = Array.from(edges).every((path) => {
          try {
            return (path as SVGPathElement).getTotalLength() > 0;
          } catch {
            return false;
          }
        });
        if (allRendered || Date.now() - start >= maxWait) resolve();
        else setTimeout(checkEdges, 100);
      };
      checkEdges();
    });
  };

  // Capture graph and add to PDF
  const captureAndAddToPage = async (
    pdf: jsPDF,
    nodes: any[],
    edges: any[],
    container: HTMLElement,
    reactFlowInstance: ReactFlowInstance,
    complaintNumber: string = '',
    pageTitle?: string,
  ) => {
    try {
      const reactFlowContainer = container.querySelector('.react-flow') as HTMLElement;
      if (!reactFlowContainer) throw new Error('React Flow container not found');

      // Reset styles to avoid duplicates
      const nodesElements = reactFlowContainer.querySelectorAll('.react-flow__node');
      const edgesElements = reactFlowContainer.querySelectorAll('.react-flow__edge');
      nodesElements.forEach((node) => {
        const nodeEl = node as HTMLElement;
        nodeEl.style.display = 'block';
        nodeEl.style.visibility = 'visible';
        nodeEl.style.opacity = '1';
        nodeEl.style.backgroundColor = 'white';
        nodeEl.style.boxShadow = 'none';
      });

      edgesElements.forEach((edge) => {
        const edgeEl = edge as HTMLElement;
        edgeEl.style.display = 'none'; // Hide original edges to prevent duplicates
      });

      // Create SVG overlay for edges
      const svgOverlay = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
      svgOverlay.setAttribute('width', reactFlowContainer.offsetWidth.toString());
      svgOverlay.setAttribute('height', reactFlowContainer.offsetHeight.toString());
      svgOverlay.style.position = 'absolute';
      svgOverlay.style.top = '0';
      svgOverlay.style.left = '0';
      svgOverlay.style.zIndex = '1000';
      svgOverlay.classList.add('edge-overlay-svg');

      edges.forEach((edge) => {
        const sourceNode = nodes.find((node) => node.id === edge.source);
        const targetNode = nodes.find((node) => node.id === edge.target);
        if (sourceNode && targetNode) {
          const sourceElement = reactFlowContainer.querySelector(`[data-id="${sourceNode.id}"]`) as HTMLElement;
          const targetElement = reactFlowContainer.querySelector(`[data-id="${targetNode.id}"]`) as HTMLElement;
          if (sourceElement && targetElement) {
            const sourceRect = sourceElement.getBoundingClientRect();
            const targetRect = targetElement.getBoundingClientRect();
            const containerRect = reactFlowContainer.getBoundingClientRect();

            const sourceX = sourceRect.left + sourceRect.width / 2 - containerRect.left;
            const sourceY = sourceRect.top + sourceRect.height - containerRect.top;
            const targetX = targetRect.left + targetRect.width / 2 - containerRect.left;
            const targetY = targetRect.top - containerRect.top;

            const edgeGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
            const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            const midY = (sourceY + targetY) / 2;
            path.setAttribute('d', `M${sourceX},${sourceY} C${sourceX},${midY} ${targetX},${midY} ${targetX},${targetY}`);
            path.setAttribute('stroke', '#3b82f6');
            path.setAttribute('stroke-width', '1.5');
            path.setAttribute('fill', 'none');
            edgeGroup.appendChild(path);
            svgOverlay.appendChild(edgeGroup);
          }
        }
      });

      reactFlowContainer.appendChild(svgOverlay);
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Capture using html-to-image
      const svgData = await htmlToImage.toSvg(reactFlowContainer, {
        filter: (node) => node.tagName !== 'SCRIPT',
        backgroundColor: 'white',
      });
      const imgData = await htmlToImage.toPng(svgData);
      const img = new Image();
      img.src = imgData;
      await img.decode();

      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();
      const sideMargin = 2;
      const topMargin = pageTitle ? 12 : 2;
      const availableWidth = pageWidth - 2 * sideMargin;
      const availableHeight = pageHeight - topMargin - 2;

      const imgWidth = availableWidth;
      const imgHeight = imgWidth * (img.height / img.width);
      const xOffset = sideMargin;
      const yOffset = topMargin;

      if (pageTitle) {
        pdf.setFontSize(12);
        pdf.setTextColor(100, 100, 100);
        pdf.text(pageTitle, pageWidth / 2, 10, { align: 'center' });
      }

      pdf.addImage(imgData, 'PNG', xOffset, yOffset, imgWidth, imgHeight);
      pdf.setFontSize(8);
      pdf.setTextColor(150, 150, 150);
      pdf.text('Generated by Cyber Sakha', pageWidth - 40, pageHeight - 3);

      // Cleanup
      reactFlowContainer.removeChild(svgOverlay);
      nodesElements.forEach((node) => {
        const nodeEl = node as HTMLElement;
        nodeEl.style.display = '';
        nodeEl.style.visibility = '';
        nodeEl.style.opacity = '';
        nodeEl.style.backgroundColor = '';
        nodeEl.style.boxShadow = '';
      });
      edgesElements.forEach((edge) => {
        const edgeEl = edge as HTMLElement;
        edgeEl.style.display = '';
      });
    } catch (error) {
      console.error('Error capturing graph:', error);
      pdf.setFontSize(16);
      pdf.text('Failed to capture graph.', pdf.internal.pageSize.getWidth() / 2, pdf.internal.pageSize.getHeight() / 2, { align: 'center' });
    }
  };

  // Export graph to PDF
  const exportGraph = useCallback(async () => {
    if (!reactFlowRef.current || !reactFlowInstance) {
      showError('Graph is not ready for export');
      return;
    }

    try {
      const preferences = await askExportPreferences();
      if (!preferences) return;

      showInfo('Preparing graph for export...');
      const originalViewport = reactFlowInstance.getViewport();
      const originalNodes = reactFlowInstance.getNodes();
      const originalEdges = reactFlowInstance.getEdges();
      const metadataNode = originalNodes.find((node) => node.type === 'metadata');
      const complaintNumber = metadataNode?.data?.complaint_number || '';

      const pdf = new jsPDF({ orientation: 'landscape', unit: 'mm', format: 'a4', compress: true });
      const senderNodes = originalNodes.filter((node) => node.data?.layer === 0 || node.data?.isSenderNode === true);

      reactFlowInstance.setNodes(originalNodes.map((node) => ({ ...node, hidden: false })));
      reactFlowInstance.setEdges(originalEdges.map((edge) => ({ ...edge, hidden: false })));
      reactFlowInstance.setViewport({ x: 0, y: 0, zoom: 1.0 });
      await new Promise((resolve) => setTimeout(resolve, 1000));
      reactFlowInstance.fitView({ padding: 0.2, duration: 0 });
      await new Promise((resolve) => setTimeout(resolve, 1000));

      if (preferences.layout === 'multi' && senderNodes.length > 1) {
        for (let i = 0; i < senderNodes.length; i++) {
          const senderNode = senderNodes[i];
          if (i > 0) pdf.addPage();

          reactFlowInstance.setNodes(originalNodes);
          reactFlowInstance.setEdges(originalEdges);
          reactFlowInstance.setViewport({ x: 0, y: 0, zoom: 1.0 });
          await new Promise((resolve) => setTimeout(resolve, 500));

          const connectedNodeIds = new Set<string>([senderNode.id]);
          const directConnections = originalEdges.filter((edge) => edge.source === senderNode.id);
          directConnections.forEach((edge) => connectedNodeIds.add(edge.target));

          let newNodesAdded = true;
          while (newNodesAdded) {
            newNodesAdded = false;
            originalEdges.forEach((edge) => {
              if (connectedNodeIds.has(edge.source) && !connectedNodeIds.has(edge.target)) {
                connectedNodeIds.add(edge.target);
                newNodesAdded = true;
              }
            });
          }

          let senderSubgraphNodes = originalNodes
            .filter((node) => connectedNodeIds.has(node.id))
            .map((node) => ({ ...node, hidden: false }));
          const senderSubgraphEdges = originalEdges
            .filter((edge) => connectedNodeIds.has(edge.source) && connectedNodeIds.has(edge.target))
            .map((edge) => ({ ...edge, hidden: false }));

          const nodesByLayer: { [key: number]: any[] } = { 0: [senderNode] };
          senderSubgraphNodes.forEach((node) => {
            if (node.id !== senderNode.id) {
              const layer = node.data?.layer || 0;
              nodesByLayer[layer] = nodesByLayer[layer] || [];
              nodesByLayer[layer].push(node);
            }
          });

          const MAX_NODES_PER_ROW = 20;
          const newNodesByLayer: { [key: number]: any[] } = {};
          Object.keys(nodesByLayer).forEach((layerStr) => {
            const layer = parseInt(layerStr);
            const nodesInLayer = nodesByLayer[layer];
            if (nodesInLayer.length <= MAX_NODES_PER_ROW) {
              newNodesByLayer[layer] = nodesInLayer;
            } else {
              const nodesByParent: { [key: string]: any[] } = {};
              nodesInLayer.forEach((node) => {
                const parentEdges = senderSubgraphEdges.filter((edge) => edge.target === node.id);
                const parentId = parentEdges[0]?.source || 'default';
                nodesByParent[parentId] = nodesByParent[parentId] || [];
                nodesByParent[parentId].push(node);
              });

              let currentRow = 0;
              let nodesInCurrentRow = 0;
              Object.keys(nodesByParent).forEach((parentId) => {
                const childNodes = nodesByParent[parentId];
                if (nodesInCurrentRow + childNodes.length > MAX_NODES_PER_ROW && nodesInCurrentRow > 0) {
                  currentRow++;
                  nodesInCurrentRow = 0;
                }
                const newLayer = layer + currentRow * 0.1;
                newNodesByLayer[newLayer] = newNodesByLayer[newLayer] || [];
                childNodes.forEach((node) => {
                  newNodesByLayer[newLayer].push({
                    ...node,
                    data: { ...node.data, layer: newLayer },
                  });
                });
                nodesInCurrentRow += childNodes.length;
              });
            }
          });

          senderSubgraphNodes = Object.values(newNodesByLayer).flat();
          const hasMoreThan20Children = senderSubgraphNodes.filter((node) => node.data?.parentId === senderNode.id).length > 20;

          const nodesWithProperLayout = calculateDynamicLayout(senderSubgraphNodes, senderSubgraphEdges, {
            isHorizontalLayout: hasMoreThan20Children || isHorizontalLayout,
            animate: false,
          });

          reactFlowInstance.setNodes(nodesWithProperLayout);
          reactFlowInstance.setEdges(senderSubgraphEdges);
          await new Promise((resolve) => setTimeout(resolve, 1000));
          reactFlowInstance.fitView({ padding: 0.2, duration: 0 });
          await new Promise((resolve) => setTimeout(resolve, 1000));

          await captureAndAddToPage(
            pdf,
            nodesWithProperLayout,
            senderSubgraphEdges,
            reactFlowRef.current,
            reactFlowInstance,
            complaintNumber,
            `Sender: ${senderNode.data?.account || senderNode.data?.label || 'Unknown'} (${i + 1}/${senderNodes.length})`,
          );
        }
      } else {
        const nodesWithProperLayout = calculateDynamicLayout(originalNodes, originalEdges, {
          isHorizontalLayout,
          animate: false,
        });
        reactFlowInstance.setNodes(nodesWithProperLayout);
        reactFlowInstance.setEdges(originalEdges);
        await new Promise((resolve) => setTimeout(resolve, 1000));
        reactFlowInstance.fitView({ padding: 0.2, duration: 0 });
        await new Promise((resolve) => setTimeout(resolve, 1000));
        await waitForEdges(reactFlowRef.current);
        await captureAndAddToPage(pdf, nodesWithProperLayout, originalEdges, reactFlowRef.current, reactFlowInstance, complaintNumber);
      }

      reactFlowInstance.setNodes(originalNodes);
      reactFlowInstance.setEdges(originalEdges);
      reactFlowInstance.setViewport(originalViewport);

      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      const filename = complaintNumber ? `transaction-flow-${complaintNumber}.pdf` : `transaction-flow-${timestamp}.pdf`;
      pdf.save(filename);
      showSuccess('Graph exported successfully!');
    } catch (error) {
      console.error('Error exporting graph:', error);
      showError('Failed to export graph: ' + (error instanceof Error ? error.message : String(error)));
    }
  }, [reactFlowRef, reactFlowInstance, isHorizontalLayout, showError, showInfo, showSuccess, askExportPreferences]);

  return (
    <button
      className={`px-3 py-1.5 ${isDark ? 'bg-green-800 hover:bg-green-700' : 'bg-green-600 hover:bg-green-700'} text-white rounded-md transition-colors flex items-center gap-2 text-sm`}
      onClick={exportGraph}
      title="Export graph to PDF"
    >
      <svg xmlns="http://www.w3.org/2000/svg" className="w-3.5 h-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
      </svg>
      Export Graph
    </button>
  );
};

export default GraphExporter;