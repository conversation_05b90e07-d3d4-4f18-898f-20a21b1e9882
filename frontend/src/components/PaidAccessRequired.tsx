import React from 'react';
import { useNavigate } from 'react-router-dom';
import { FiLock } from 'react-icons/fi';
import Button from './ui/Button';

interface PaidAccessRequiredProps {
  featureName?: string;
}

const PaidAccessRequired: React.FC<PaidAccessRequiredProps> = ({ featureName = 'This feature' }) => {
  const navigate = useNavigate();

  return (
    <div className="flex justify-center items-center min-h-[60vh] p-4">
      <div className="bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm rounded-lg shadow-lg p-6 max-w-xl w-full text-center border border-gray-100 dark:border-gray-700 animate-fade-in">
        <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
          <FiLock className="w-8 h-8 text-blue-600 dark:text-blue-400" />
        </div>

        <h1 className="text-xl font-bold mb-3 bg-clip-text text-transparent bg-gradient-to-r from-gray-900 to-gray-700 dark:from-white dark:to-gray-300">
          Premium Feature
        </h1>

        <p className="text-gray-700 dark:text-gray-300 mb-3">
          {featureName} requires a paid subscription to access.
        </p>

        <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">
          Please contact the administrator to upgrade your account and unlock all premium features.
        </p>

        <div className="mt-6 flex flex-wrap justify-center gap-3">
          <Button
            variant="primary"
            onClick={() => navigate('/dashboard')}
          >
            Go to Dashboard
          </Button>

          <Button
            variant="outlined"
            onClick={() => navigate('/profile')}
          >
            View Profile
          </Button>
        </div>
      </div>
    </div>
  );
};

export default PaidAccessRequired;
