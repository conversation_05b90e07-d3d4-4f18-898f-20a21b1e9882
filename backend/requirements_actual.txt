# Generated requirements based on actual usage
# Generated by generate_requirements.py

# Web Framework
fastapi-cli==0.0.7
fastapi-limiter==0.1.5
fastapi==0.115.12
gunicorn==23.0.0
starlette==0.46.1
uvicorn==0.34.0

# Database
aioredis==2.0.1
dnspython==2.7.0
hiredis==3.1.0
motor==3.7.0
pymongo==4.11.3
redis==4.6.0

# Security
argon2-cffi==23.1.0
bleach==6.2.0
cryptography==44.0.3
html-sanitizer==2.5.0
passlib==1.7.4
python-jose==3.4.0

# Utilities
aiofiles==24.1.0
httpx==0.28.1
jinja2==3.1.6
pydantic-settings==2.9.1
pydantic==2.9.2
pydantic_core==2.23.4
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
python-multipart==0.0.20
python-multipart==0.0.20
regex==2024.11.6

# Data Processing
beautifulsoup4==4.13.3
docxcompose==1.4.0
docxtpl==0.19.1
lxml==5.3.1
lxml_html_clean==0.4.2
pandas==2.2.3
python-docx==1.1.2
xlsxwriter==3.2.3

# Email
aiosmtplib==4.0.1
email_validator==2.2.0

# Other
aiohappyeyeballs==2.6.1
aiohttp==3.11.18
aiosignal==1.3.2
annotated-types==0.7.0
anyio==4.9.0
async-timeout==5.0.1
authlib==1.5.2
babel==2.17.0
bandit==1.8.3
bcrypt==4.3.0
certifi==2025.1.31
cffi==1.17.1
charset-normalizer==3.4.1
click==8.1.8
dparse==0.6.4
ecdsa==0.19.1
filelock==3.16.1
flake8==7.2.0
frozenlist==1.6.0
h11==0.14.0
httpcore==1.0.7
httptools==0.6.4
idna==3.10
iniconfig==2.1.0
itsdangerous==2.2.0
joblib==1.5.0
markupsafe==3.0.2
marshmallow==4.0.0
mdurl==0.1.2
multidict==6.4.4
nltk==3.9.1
numpy==2.2.4
orjson==3.10.16
packaging==25.0
pbr==6.1.1
pip==25.1.1
pluggy==1.5.0
propcache==0.3.1
psutil==6.1.1
pyasn1==0.4.8
pycodestyle==2.13.0
pycparser==2.22
pyflakes==3.3.2
pygments==2.19.1
pyjwt==2.10.1
pytest==8.3.5
pytz==2025.2
pyyaml==6.0.2
requests==2.32.3
rich-toolkit==0.14.0
rich==13.9.4
rsa==4.9.1
setuptools==78.1.0
shellingham==1.5.4
sib-api-v3-sdk==7.6.0
six==1.17.0
sniffio==1.3.1
soupsieve==2.6
stevedore==5.4.1
tenacity==9.1.2
tomlkit==0.13.2
tqdm==4.67.1
typer==0.15.2
typing-inspection==0.4.0
typing_extensions==4.13.0
ujson==5.10.0
urllib3==2.4.0
uvloop==0.21.0
watchfiles==1.0.4
webencodings==0.5.1
websockets==15.0.1
yarl==1.20.0

