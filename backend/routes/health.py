from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import JSONResponse
import time
import os
import logging
import psutil
from typing import Dict, Any
from database import check_connection
from services.cleanup_service import check_cleanup_status

# Setup logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter()

@router.get("/health", tags=["Health"])
async def health_check() -> Dict[str, Any]:
    """
    Health check endpoint for monitoring and load balancers
    
    Returns:
        dict: Health status information
    """
    start_time = time.time()
    
    # Basic health information
    health_info = {
        "status": "healthy",
        "timestamp": time.time(),
        "environment": os.getenv("ENVIRONMENT", "development"),
        "version": os.getenv("APP_VERSION", "1.0.0"),
        "components": {}
    }
    
    # Check database connection
    try:
        db_result = await check_connection()
        health_info["components"]["database"] = {
            "status": "up" if db_result.get("connected", False) else "down",
            "latency_ms": round((time.time() - start_time) * 1000, 2)
        }
        
        # If database is down, mark overall status as degraded
        if not db_result.get("connected", False):
            health_info["status"] = "degraded"
    except Exception as e:
        logger.error(f"Database health check failed: {str(e)}")
        health_info["components"]["database"] = {
            "status": "down",
            "error": str(e)
        }
        health_info["status"] = "degraded"
    
    # Check cleanup service status
    try:
        cleanup_status = check_cleanup_status()
        health_info["components"]["cleanup_service"] = {
            "status": "up" if cleanup_status.get("running", False) else "inactive",
            "last_run": cleanup_status.get("last_run", None),
            "next_run": cleanup_status.get("next_run", None)
        }
    except Exception as e:
        logger.error(f"Cleanup service health check failed: {str(e)}")
        health_info["components"]["cleanup_service"] = {
            "status": "unknown",
            "error": str(e)
        }
    
    # Add system metrics (only in non-production environments or if explicitly enabled)
    if os.getenv("ENVIRONMENT") != "production" or os.getenv("ENABLE_DETAILED_HEALTH", "false").lower() == "true":
        try:
            health_info["system"] = {
                "cpu_usage": psutil.cpu_percent(interval=0.1),
                "memory_usage": psutil.virtual_memory().percent,
                "disk_usage": psutil.disk_usage('/').percent,
            }
        except Exception as e:
            logger.error(f"System metrics collection failed: {str(e)}")
            health_info["system"] = {
                "status": "error",
                "error": str(e)
            }
    
    # Calculate total response time
    health_info["response_time_ms"] = round((time.time() - start_time) * 1000, 2)
    
    # Log health check result
    log_level = logging.WARNING if health_info["status"] != "healthy" else logging.DEBUG
    logger.log(log_level, f"Health check: {health_info['status']}, response time: {health_info['response_time_ms']}ms")
    
    # Return appropriate status code based on health
    status_code = status.HTTP_200_OK if health_info["status"] == "healthy" else status.HTTP_503_SERVICE_UNAVAILABLE
    
    return JSONResponse(
        content=health_info,
        status_code=status_code
    )

@router.get("/readiness", tags=["Health"])
async def readiness_check() -> Dict[str, Any]:
    """
    Readiness check endpoint for Kubernetes/container orchestration
    
    This endpoint checks if the application is ready to serve traffic
    
    Returns:
        dict: Readiness status information
    """
    # Check database connection as the primary readiness indicator
    try:
        db_result = await check_connection()
        if db_result.get("connected", False):
            return {
                "status": "ready",
                "timestamp": time.time()
            }
        else:
            return JSONResponse(
                content={
                    "status": "not_ready",
                    "reason": "Database connection failed",
                    "timestamp": time.time()
                },
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE
            )
    except Exception as e:
        logger.error(f"Readiness check failed: {str(e)}")
        return JSONResponse(
            content={
                "status": "not_ready",
                "reason": str(e),
                "timestamp": time.time()
            },
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE
        )

@router.get("/liveness", tags=["Health"])
async def liveness_check() -> Dict[str, Any]:
    """
    Liveness check endpoint for Kubernetes/container orchestration
    
    This endpoint verifies that the application is running and not deadlocked
    
    Returns:
        dict: Liveness status information
    """
    # For liveness, we just check if the application can respond
    # We don't check external dependencies like the database
    return {
        "status": "alive",
        "timestamp": time.time(),
        "uptime_seconds": time.time() - psutil.boot_time()
    }
