"""
CSRF token endpoint for getting CSRF tokens.
"""
import logging
import secrets
from fastapi import APIRouter, Request, Response
from typing import Dict

# Setup logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/csrf",
    tags=["csrf"],
)

@router.get("/token")
async def get_csrf_token(request: Request, response: Response) -> Dict[str, str]:
    """
    Get a CSRF token for protecting against CSRF attacks.

    This endpoint sets a CSRF token cookie and returns the token in the response.
    The frontend should include this token in the X-CSRF-Token header for all
    state-changing requests (POST, PUT, DELETE, etc.).

    Args:
        request: The incoming request
        response: The outgoing response

    Returns:
        A dictionary containing the CSRF token
    """
    # Generate a new CSRF token
    csrf_token = secrets.token_hex(32)

    # Set the CSRF token cookie
    cookie_name = "csrf_token"
    cookie_max_age = 3600  # 1 hour

    # Log request headers for debugging
    logger.info(f"CSRF token request headers: {dict(request.headers)}")
    logger.info(f"CSRF token request cookies: {request.cookies}")

    # Set the cookie using response.set_cookie for better compatibility
    # Important: For CSRF protection, the cookie should NOT be httponly
    # This is an exception to the rule because we need JavaScript to read this cookie
    # for the double-submit pattern to work

    # Check if we're in production
    import os
    is_production = os.getenv("ENVIRONMENT") == "production"

    # Set the cookie with appropriate security settings
    # Always use 'lax' for samesite to ensure cookies work properly in modern browsers
    # 'none' requires secure=True which doesn't work in development without HTTPS
    samesite_setting = "lax"

    response.set_cookie(
        key=cookie_name,
        value=csrf_token,
        max_age=cookie_max_age,
        httponly=False,  # Must be readable by JavaScript for double-submit pattern
        samesite=samesite_setting,
        secure=is_production,  # Only set secure flag in production
        path="/"
    )

    # Log the cookie settings
    logger.info(f"Setting CSRF cookie: name={cookie_name}, secure={is_production}, samesite={samesite_setting}, httponly=False")

    # Also set the token in the response header for clients that prefer that method
    response.headers["X-CSRF-Token"] = csrf_token

    # Set in alternate headers for better compatibility
    alternate_headers = [
        "x-csrf-token",
        "x-xsrf-token",
        "csrf-token",
        "xsrf-token"
    ]

    for header_name in alternate_headers:
        response.headers[header_name] = csrf_token

    # Log that we're using the double-submit pattern
    logger.info("Setting CSRF cookie using double-submit pattern")
    logger.info(f"CSRF token generated: {csrf_token[:8]}...")

    # Log all response headers for debugging
    logger.info(f"Response headers: {dict(response.headers)}")

    # Return the CSRF token in the response
    return {"csrf_token": csrf_token}
