# FastAPI and ASGI server
fastapi>=0.104.0
uvicorn[standard]>=0.23.2
gunicorn>=21.2.0

# Security
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
python-multipart>=0.0.6
argon2-cffi>=23.1.0
cryptography>=41.0.4
pydantic[email]>=2.6.0,<2.10.0
pydantic-settings>=2.0.3
html-sanitizer>=2.2.0  # HTML sanitization for XSS protection
bleach>=6.1.0  # Additional HTML sanitization library

# Database
motor>=3.3.1
pymongo>=4.5.0
dnspython>=2.4.2

# Utilities
python-dotenv>=1.0.0
email-validator>=2.0.0
Jinja2>=3.1.2
aiofiles>=23.2.1
python-dateutil>=2.8.2
beautifulsoup4>=4.12.2
lxml>=4.9.3
httpx>=0.25.0
pandas>=2.1.1
regex>=2023.8.8
python-docx>=1.0.0
xlsxwriter>=3.1.2

# Email
aiosmtplib>=2.0.2

# Caching and rate limiting
redis>=4.2.0rc1,<5.0.0
aioredis>=2.0.1

# Logging and monitoring

# CORS
starlette>=0.27.0

# Production
uvloop>=0.17.0; sys_platform != 'win32'
httptools>=0.6.0
