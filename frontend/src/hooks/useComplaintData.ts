import { useState, useCallback, useEffect } from 'react';
import complaintService from '../services/complaintService';
import { useAlert } from '../context/TailwindAlertContext';
import logger from '../utils/logger';

// Re-export useComplaints for backward compatibility
export { default as useComplaints } from './useComplaints';

/**
 * Custom hook for fetching and managing a single complaint's data
 * @param complaintId The ID of the complaint to fetch
 * @param includeDetails Whether to include all details (true) or just basic info (false)
 * @returns Object containing loading state, error state, complaint data, and refetch function
 */
export const useComplaintData = (complaintId: string, _includeDetails: boolean = false) => {
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<any>(null);
  const { showError } = useAlert();

  // Function to fetch complaint data
  const fetchComplaintData = useCallback(async (_forceRefresh: boolean = false) => {
    if (!complaintId) {
      setError('No complaint ID provided');
      setLoading(false);
      return null;
    }

    try {
      setLoading(true);
      setError(null);
      logger.debug('useComplaintData: Fetching complaint data for ID:', complaintId);

      // Fetch complaint data from API
      const response = await complaintService.getComplaintById(complaintId);
      logger.debug('useComplaintData: Complaint data received', response);

      if (!response) {
        throw new Error('No data received from server');
      }

      // Set the data
      setData(response);
      setLoading(false);
      return response;
    } catch (err: any) {
      logger.error('useComplaintData: Error fetching complaint data:', err);
      setError(err.message || 'Failed to load complaint data');
      showError('Failed to load complaint data: ' + (err.message || 'Unknown error'));
      setLoading(false);
      return null;
    }
  }, [complaintId, showError]);

  // Initial fetch
  useEffect(() => {
    fetchComplaintData();
  }, [complaintId, fetchComplaintData]);

  return {
    loading,
    error,
    data,
    refetch: fetchComplaintData
  };
};

// Default export for direct imports
export default useComplaintData;
