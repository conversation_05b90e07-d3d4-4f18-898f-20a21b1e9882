"""
Cleanup service for database maintenance tasks
"""
import logging
import os
import time
from datetime import datetime, timezone, timedelta
from typing import Dict, Any
from database import get_db

# Setup logging
logger = logging.getLogger(__name__)

# Global variables to track cleanup status
_last_cleanup_run = None
_next_cleanup_run = None
_cleanup_running = False

async def cleanup_expired_tokens():
    """
    Remove expired tokens from the blacklist - simplified version
    """
    try:
        db = get_db()
        if db is None:
            logger.error("Database connection error during token cleanup")
            return False

        # Get current time
        now = datetime.now(timezone.utc)

        # Blacklisted tokens functionality removed for simplicity

        # Delete expired refresh tokens
        refresh_result = await db.user_refresh_tokens.delete_many({
            "expires_at": {"$lt": now}
        })

        if refresh_result.deleted_count > 0:
            logger.info(f"Cleaned up {refresh_result.deleted_count} expired refresh tokens")

        return True
    except Exception as e:
        logger.error(f"Error cleaning up expired tokens: {str(e)}")
        return False

async def cleanup_revoked_tokens():
    """
    Remove old revoked refresh tokens (older than 30 days)
    """
    try:
        db = get_db()
        if db is None:
            logger.error("Database connection error during revoked token cleanup")
            return False

        # Get current time minus 30 days
        thirty_days_ago = datetime.now(timezone.utc) - timedelta(days=30)

        # Delete old revoked tokens
        result = await db.user_refresh_tokens.delete_many({
            "is_revoked": True,
            "revoked_at": {"$lt": thirty_days_ago}
        })

        if result.deleted_count > 0:
            logger.info(f"Cleaned up {result.deleted_count} old revoked refresh tokens")

        return True
    except Exception as e:
        logger.error(f"Error cleaning up revoked tokens: {str(e)}")
        return False

async def cleanup_operation_records():
    """
    Clean up old operation records
    """
    try:
        # Import here to avoid circular imports
        from services.operation_protection_service import cleanup_old_operation_records

        # Run the cleanup
        result = await cleanup_old_operation_records()

        if result:
            logger.info("Successfully cleaned up old operation records")
        else:
            logger.warning("Failed to clean up old operation records")

        return result
    except Exception as e:
        logger.error(f"Error cleaning up operation records: {str(e)}")
        return False

def run_scheduled_cleanup():
    """
    Run all scheduled cleanup tasks
    This is a synchronous function that doesn't actually run the async tasks
    but just logs that they should be run. The actual cleanup is done elsewhere.
    """
    global _last_cleanup_run, _next_cleanup_run, _cleanup_running

    try:
        # Mark cleanup as running
        _cleanup_running = True

        # Record start time
        start_time = time.time()
        _last_cleanup_run = datetime.now(timezone.utc)

        # Log that we're skipping the actual cleanup in this context
        logger.info("Scheduled cleanup triggered - actual cleanup will be performed by background tasks")

        # Calculate next run time (6 hours from now by default)
        cleanup_interval = int(os.getenv("CLEANUP_INTERVAL_HOURS", "6"))
        _next_cleanup_run = datetime.now(timezone.utc) + timedelta(hours=cleanup_interval)

        # Log completion time
        duration = time.time() - start_time
        logger.info(f"Scheduled cleanup completed in {duration:.2f} seconds")

        return True
    except Exception as e:
        logger.error(f"Error running scheduled cleanup: {str(e)}")
        return False
    finally:
        # Mark cleanup as not running
        _cleanup_running = False

async def run_cleanup_tasks():
    """
    Run all async cleanup tasks
    This should be called directly from an async context
    """
    try:
        # Clean up expired tokens
        token_result = await cleanup_expired_tokens()
        if not token_result:
            logger.warning("Failed to clean up expired tokens")

        # Clean up old revoked tokens
        revoked_result = await cleanup_revoked_tokens()
        if not revoked_result:
            logger.warning("Failed to clean up revoked tokens")

        # Clean up old operation records
        op_result = await cleanup_operation_records()
        if not op_result:
            logger.warning("Failed to clean up operation records")

        # Add more cleanup tasks here as needed

        return True
    except Exception as e:
        logger.error(f"Error running async cleanup tasks: {str(e)}")
        return False

def check_cleanup_status() -> Dict[str, Any]:
    """
    Check the status of the cleanup service

    Returns:
        Dict containing status information about the cleanup service
    """
    # We need the global declaration because we're modifying _next_cleanup_run
    global _next_cleanup_run

    # Get cleanup interval from environment
    cleanup_interval = int(os.getenv("CLEANUP_INTERVAL_HOURS", "6"))

    # Calculate next run if not set
    if _next_cleanup_run is None and _last_cleanup_run is not None:
        _next_cleanup_run = _last_cleanup_run + timedelta(hours=cleanup_interval)

    return {
        "running": _cleanup_running,
        "last_run": _last_cleanup_run.isoformat() if _last_cleanup_run else None,
        "next_run": _next_cleanup_run.isoformat() if _next_cleanup_run else None,
        "interval_hours": cleanup_interval,
        "enabled": os.getenv("ENABLE_CLEANUP", "true").lower() == "true"
    }
