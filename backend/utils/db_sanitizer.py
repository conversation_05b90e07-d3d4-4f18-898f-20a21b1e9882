"""
Database query sanitization utilities.

This module provides functions to sanitize database queries and prevent
NoSQL injection attacks.
"""
import logging
import re
from typing import Any, Dict, List, Optional, Union, Callable
from bson import ObjectId

# Setup logging
logger = logging.getLogger(__name__)

# Import existing query sanitizer
from utils.query_sanitizer import sanitize_query, sanitize_mongodb_id

# List of allowed MongoDB operators
ALLOWED_OPERATORS = {
    # Comparison operators
    '$eq', '$gt', '$gte', '$in', '$lt', '$lte', '$ne', '$nin',
    
    # Logical operators
    '$and', '$not', '$nor', '$or',
    
    # Element operators
    '$exists', '$type',
    
    # Array operators
    '$all', '$elemMatch', '$size',
    
    # Text search operators
    '$text', '$search',
    
    # Projection operators
    '$slice',
    
    # Aggregation operators
    '$count', '$group', '$limit', '$match', '$project', '$skip', '$sort', '$unwind',
    
    # Safe update operators
    '$set', '$unset', '$inc', '$push', '$pull', '$addToSet'
}

# Dangerous operators that should never be allowed
DANGEROUS_OPERATORS = {
    '$where',        # Allows arbitrary JavaScript execution
    '$expr',         # Can be used for complex expressions
    '$function',     # Allows arbitrary JavaScript execution
    '$accumulator',  # Custom aggregation stages
    '$merge',        # Can modify other collections
    '$out',          # Can overwrite collections
    '$lookup',       # Can be used for data leakage
    '$graphLookup',  # Can be used for data leakage
    '$facet',        # Can be used for complex nested operations
    '$geoNear',      # Can be expensive and leak location data
    '$indexStats',   # Reveals database structure
    '$listSessions', # Reveals session information
    '$collStats',    # Reveals collection statistics
    '$currentOp',    # Reveals current operations
    '$eval',         # Deprecated but dangerous if available
    '$mapReduce'     # Can be used for arbitrary code execution
}

def sanitize_find_query(query: Optional[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Sanitize a MongoDB find query.
    
    Args:
        query: The query to sanitize
        
    Returns:
        Sanitized query
    """
    if query is None:
        return {}
    
    # Use the existing sanitize_query function
    sanitized = sanitize_query(query)
    
    # If sanitization removed all query parameters, return empty dict
    if not sanitized:
        return {}
    
    return sanitized

def sanitize_update_query(update: Optional[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Sanitize a MongoDB update query.
    
    Args:
        update: The update query to sanitize
        
    Returns:
        Sanitized update query
    """
    if update is None:
        return {}
    
    # Use the existing sanitize_query function
    sanitized = sanitize_query(update)
    
    # If sanitization removed all update parameters, return empty dict
    if not sanitized:
        return {}
    
    # Ensure update operators are present and valid
    if not any(key.startswith('$') for key in sanitized.keys()):
        # If no update operators, wrap in $set
        return {'$set': sanitized}
    
    return sanitized

def sanitize_aggregate_pipeline(pipeline: Optional[List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
    """
    Sanitize a MongoDB aggregation pipeline.
    
    Args:
        pipeline: The aggregation pipeline to sanitize
        
    Returns:
        Sanitized aggregation pipeline
    """
    if not pipeline:
        return []
    
    # Use the existing sanitize_query function for each stage
    sanitized_pipeline = []
    for stage in pipeline:
        sanitized_stage = sanitize_query(stage)
        if sanitized_stage:  # Only add non-empty stages
            sanitized_pipeline.append(sanitized_stage)
    
    return sanitized_pipeline

def sanitize_sort_params(sort_params: Optional[Union[Dict[str, Any], List[tuple]]]) -> Dict[str, int]:
    """
    Sanitize MongoDB sort parameters.
    
    Args:
        sort_params: Sort parameters as dict or list of tuples
        
    Returns:
        Sanitized sort parameters as dict
    """
    if not sort_params:
        return {'_id': 1}  # Default sort by _id ascending
    
    sanitized_sort = {}
    
    if isinstance(sort_params, dict):
        # Process dict format
        for field, direction in sort_params.items():
            # Sanitize field name
            if field and isinstance(field, str) and not field.startswith('$'):
                # Normalize direction to 1 or -1
                if direction in (1, -1, 'asc', 'desc', 'ascending', 'descending'):
                    if direction == 'asc' or direction == 'ascending':
                        sanitized_sort[field] = 1
                    elif direction == 'desc' or direction == 'descending':
                        sanitized_sort[field] = -1
                    else:
                        sanitized_sort[field] = direction
    
    elif isinstance(sort_params, list):
        # Process list of tuples format
        for item in sort_params:
            if isinstance(item, tuple) and len(item) == 2:
                field, direction = item
                # Sanitize field name
                if field and isinstance(field, str) and not field.startswith('$'):
                    # Normalize direction to 1 or -1
                    if direction in (1, -1, 'asc', 'desc', 'ascending', 'descending'):
                        if direction == 'asc' or direction == 'ascending':
                            sanitized_sort[field] = 1
                        elif direction == 'desc' or direction == 'descending':
                            sanitized_sort[field] = -1
                        else:
                            sanitized_sort[field] = direction
    
    # If sanitization removed all sort parameters, return default
    if not sanitized_sort:
        return {'_id': 1}
    
    return sanitized_sort

def sanitize_projection(projection: Optional[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Sanitize MongoDB projection parameters.
    
    Args:
        projection: Projection parameters
        
    Returns:
        Sanitized projection parameters
    """
    if not projection:
        return {}
    
    sanitized_projection = {}
    
    for field, value in projection.items():
        # Sanitize field name
        if field and isinstance(field, str):
            # Allow only 0, 1, or simple expressions
            if value in (0, 1, True, False):
                sanitized_projection[field] = 1 if value in (1, True) else 0
            elif isinstance(value, dict) and '$slice' in value:
                # Allow $slice operator with numeric values
                slice_value = value['$slice']
                if isinstance(slice_value, int) or (
                    isinstance(slice_value, list) and 
                    len(slice_value) == 2 and 
                    all(isinstance(x, int) for x in slice_value)
                ):
                    sanitized_projection[field] = {'$slice': slice_value}
    
    return sanitized_projection

def safe_db_operation(operation: Callable, *args, **kwargs) -> Any:
    """
    Execute a database operation with sanitized parameters.
    
    Args:
        operation: Database operation function
        *args: Positional arguments
        **kwargs: Keyword arguments
        
    Returns:
        Result of the database operation
    """
    try:
        # Execute the operation
        return operation(*args, **kwargs)
    except Exception as e:
        # Log the error
        logger.error(f"Database operation error: {str(e)}")
        # Re-raise the exception
        raise
