from utils.encryption import encrypt_document, decrypt_document, decrypt_data
import logging

logger = logging.getLogger(__name__)

# Define fields to encrypt for each collection
SENSITIVE_FIELDS = {
    "complaints": [
        "complainant_name",
        "complainant_address",
        "complainant_phone",
        "complainant_email",
        "account_number",
        "bank_details.account_number",
        "bank_details.ifsc_code",
        "personal_details.address",
        "personal_details.phone",
        "personal_details.email"
    ],
    "users": [
        "phone",
        "address",
        "personal_details.address",
        "personal_details.phone"
    ]
}

def encrypt_complaint(complaint_data: dict) -> dict:
    """
    Encrypt sensitive fields in a complaint document.

    Args:
        complaint_data: The complaint document

    Returns:
        Complaint document with encrypted sensitive fields
    """
    try:
        return encrypt_document(complaint_data, SENSITIVE_FIELDS["complaints"])
    except Exception as e:
        logger.error(f"Error encrypting complaint: {e}")
        return complaint_data

def decrypt_complaint(complaint_data: dict) -> dict:
    """
    Decrypt sensitive fields in a complaint document.

    Args:
        complaint_data: The complaint document with encrypted fields

    Returns:
        Complaint document with decrypted sensitive fields
    """
    try:
        return decrypt_document(complaint_data, SENSITIVE_FIELDS["complaints"])
    except Exception as e:
        logger.error(f"Error decrypting complaint: {e}")
        return complaint_data

def encrypt_user(user_data: dict) -> dict:
    """
    Encrypt sensitive fields in a user document.

    Args:
        user_data: The user document

    Returns:
        User document with encrypted sensitive fields
    """
    try:
        return encrypt_document(user_data, SENSITIVE_FIELDS["users"])
    except Exception as e:
        logger.error(f"Error encrypting user: {e}")
        return user_data

def decrypt_user(user_data: dict) -> dict:
    """
    Decrypt sensitive fields in a user document.

    Args:
        user_data: The user document with encrypted fields

    Returns:
        User document with decrypted sensitive fields
    """
    try:
        return decrypt_document(user_data, SENSITIVE_FIELDS["users"])
    except Exception as e:
        logger.error(f"Error decrypting user: {e}")
        return user_data

# Template encryption/decryption functions removed - using user-specific templates only

def decrypt_field(encrypted_value: str) -> str:
    """
    Decrypt a single encrypted field value.

    Args:
        encrypted_value: The encrypted field value

    Returns:
        Decrypted field value
    """
    try:
        return decrypt_data(encrypted_value)
    except Exception as e:
        logger.error(f"Error decrypting field: {e}")
        return encrypted_value
