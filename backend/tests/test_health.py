import pytest
from fastapi.testclient import TestClient
from main import app

client = TestClient(app)

def test_health_endpoint():
    """Test that the health endpoint returns a 200 status code and the expected response format."""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "ok"
    assert data["success"] is True
    assert "timestamp" in data
    assert "database_status" in data
    assert "environment" in data
