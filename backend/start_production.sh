#!/bin/bash

# Production startup script for Cyber Sakha backend

# Load environment variables from .env.production
if [ -f .env.production ]; then
    echo "Loading environment variables from .env.production"
    export $(grep -v '^#' .env.production | xargs)
else
    echo "Warning: .env.production file not found"
fi

# Set environment to production
export ENVIRONMENT=production

# Start the server using Gunicorn with Uvicorn workers
echo "Starting Cyber Sakha backend in production mode..."
gunicorn main:app \
    --workers 4 \
    --worker-class uvicorn.workers.UvicornWorker \
    --bind 0.0.0.0:${PORT:-8000} \
    --timeout 120 \
    --access-logfile - \
    --error-logfile - \
    --log-level info
