"""
Robust session middleware that ensures session is always available.

This middleware wraps <PERSON><PERSON>'s SessionMiddleware and adds additional
functionality to ensure that the session is always available, even if
the SessionMiddleware is not properly initialized or if there are issues
with the session cookie.
"""
import logging
import uuid
import json
import itsdan<PERSON>ous
from typing import Dict, Any, Optional, Callable, Awaitable
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from starlette.requests import Request
from starlette.responses import Response
from starlette.types import ASGIApp, Scope, Receive, Send, Message

# Setup logging
logger = logging.getLogger(__name__)

class RobustSessionMiddleware(BaseHTTPMiddleware):
    """
    Robust session middleware that ensures session is always available.
    
    This middleware wraps <PERSON><PERSON>'s SessionMiddleware and adds additional
    functionality to ensure that the session is always available, even if
    the SessionMiddleware is not properly initialized or if there are issues
    with the session cookie.
    """
    
    def __init__(
        self,
        app: ASGIApp,
        secret_key: str,
        session_cookie: str = "session",
        max_age: int = 14 * 24 * 60 * 60,  # 14 days in seconds
        same_site: str = "lax",
        https_only: bool = False,
        path: str = "/",
    ) -> None:
        super().__init__(app)
        self.secret_key = secret_key
        self.session_cookie = session_cookie
        self.max_age = max_age
        self.security_flags = f"httponly; samesite={same_site}; path={path}"
        if https_only:
            self.security_flags += "; secure"
        
        # Create signer for session data
        self.signer = itsdangerous.TimestampSigner(secret_key)
        
        # Log initialization
        logger.info(f"RobustSessionMiddleware initialized with cookie: {session_cookie}")
    
    async def dispatch(
        self, request: Request, call_next: RequestResponseEndpoint
    ) -> Response:
        """
        Process the request and ensure session is properly initialized.
        
        Args:
            request: The incoming request
            call_next: The next middleware or route handler
            
        Returns:
            The response from the next middleware or route handler
        """
        # Initialize session if not already present
        if "session" not in request.scope:
            # Create a new session dict
            request.scope["session"] = {}
            
            # Try to load existing session from cookie
            session_cookie = request.cookies.get(self.session_cookie)
            if session_cookie:
                try:
                    data = self.signer.unsign(
                        session_cookie.encode("utf-8"), max_age=self.max_age
                    )
                    request.scope["session"] = json.loads(data.decode("utf-8"))
                    logger.debug(f"Session loaded from cookie: {self.session_cookie}")
                except Exception as e:
                    # Log the error but continue with empty session
                    logger.warning(f"Failed to load session from cookie: {str(e)}")
            else:
                logger.debug(f"No session cookie found, creating new session")
                
            # Add a session ID if not present
            if "session_id" not in request.scope["session"]:
                request.scope["session"]["session_id"] = str(uuid.uuid4())
        
        # Add a flag to indicate that the session was modified
        request.scope["session_modified"] = False
        
        # Define a method to mark the session as modified
        def _mark_session_modified():
            request.scope["session_modified"] = True
        
        # Monkey patch the session to mark it as modified when it's changed
        original_session = request.scope["session"]
        session_proxy = SessionProxy(original_session, _mark_session_modified)
        request.scope["session"] = session_proxy
        
        # Process the request
        response = await call_next(request)
        
        # Update the session cookie if session was modified
        if request.scope.get("session_modified", False):
            # Serialize and sign the session data
            data = json.dumps(dict(original_session)).encode("utf-8")
            session_cookie = self.signer.sign(data).decode("utf-8")
            
            # Set the cookie in the response
            cookie_value = f"{self.session_cookie}={session_cookie}; {self.security_flags}"
            if self.max_age:
                cookie_value += f"; max-age={self.max_age}"
            
            response.headers["Set-Cookie"] = cookie_value
            logger.debug(f"Session cookie updated")
        
        return response


class SessionProxy(dict):
    """
    Proxy for the session dict that marks the session as modified when it's changed.
    """
    
    def __init__(self, session_dict: Dict[str, Any], on_modified: Callable[[], None]):
        self._session_dict = session_dict
        self._on_modified = on_modified
        super().__init__(session_dict)
    
    def __setitem__(self, key: str, value: Any) -> None:
        self._session_dict[key] = value
        self._on_modified()
    
    def __delitem__(self, key: str) -> None:
        del self._session_dict[key]
        self._on_modified()
    
    def clear(self) -> None:
        self._session_dict.clear()
        self._on_modified()
    
    def pop(self, key: str, default: Any = None) -> Any:
        result = self._session_dict.pop(key, default)
        self._on_modified()
        return result
    
    def popitem(self) -> tuple:
        result = self._session_dict.popitem()
        self._on_modified()
        return result
    
    def setdefault(self, key: str, default: Any = None) -> Any:
        if key not in self._session_dict:
            self._session_dict[key] = default
            self._on_modified()
        return self._session_dict[key]
    
    def update(self, *args, **kwargs) -> None:
        self._session_dict.update(*args, **kwargs)
        self._on_modified()
