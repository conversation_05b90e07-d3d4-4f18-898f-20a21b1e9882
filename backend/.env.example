# Environment
ENVIRONMENT=development  # Set to 'production' for production deployment

# Server settings
PORT=8000
HOST=0.0.0.0

# Database settings
# Replace with your MongoDB Atlas connection string for production
DATABASE_URL=mongodb://localhost:27017/cybersakha
# For MongoDB Atlas, use format: mongodb+srv://username:<EMAIL>/cybersakha

# CORS settings
FRONTEND_ORIGIN=http://localhost:3001
PRODUCTION_DOMAIN=https://cybersakha.in
ALLOWED_HOSTS=cybersakha.in,www.cybersakha.in,api.cybersakha.in,localhost,127.0.0.1

# Security settings
# Generate strong random values for these in production
SECRET_KEY=your_secret_key_here
SESSION_SECRET_KEY=your_session_secret_key_here
ADMIN_API_KEY=your_admin_api_key_here

# Email settings
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password_here
EMAIL_FROM=<EMAIL>

# Logging
LOG_LEVEL=INFO  # Set to DEBUG for development, INFO for production

# Cleanup settings
ENABLE_CLEANUP=true
CLEANUP_INTERVAL_HOURS=6
