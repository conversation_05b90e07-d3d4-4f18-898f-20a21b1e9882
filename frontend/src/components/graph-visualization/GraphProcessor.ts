import { MarkerType } from 'reactflow';
import {
  buildTransactionHierarchy,
  Transaction,
  TransactionNode,
  TransactionHierarchy
} from './TransactionHierarchyBuilder';

// Define the complaint metadata type
interface ComplaintMetadata {
  complaint_number: string;
  complainant_name: string;
  date_of_complaint: string;
  total_amount: string | number;
  category?: string;
  subcategory?: string;
  [key: string]: any; // Allow for additional fields
}

// Define the data type for our graph
interface GraphData {
  nodes: GraphNode[];
  edges: GraphEdge[];
}

// Define the node type for our graph
type GraphNode = {
  id: string;
  type: string;
  data: any;
  position: { x: number; y: number };
  layer?: number;
};

// Define a type that matches ReactFlow's Edge type but with our custom properties
type GraphEdge = {
  id: string;
  source: string;
  target: string;
  animated?: boolean;
  label?: string;
  data?: any;
  markerEnd?: {
    type: MarkerType;
    width: number;
    height: number;
    color: string;
  };
};

/**
 * Process transactions for graph visualization using hierarchical structure
 * @param transactions Array of transaction data
 * @param metadata Complaint metadata
 * @returns Object containing nodes and edges for ReactFlow
 */
export const processTransactionsForGraph = (
  transactions: Transaction[],
  metadata: any,
  horizontalLayout: boolean = false // Controls whether to use horizontal (LR) or vertical (TB) layout
): GraphData => {
  console.log("Processing transactions for graph:", transactions.length, "transactions");

  const nodes: GraphNode[] = [];
  const edges: GraphEdge[] = [];

  // Ensure metadata has required fields
  const processedMetadata: ComplaintMetadata = {
    complaint_number: metadata.complaint_number || metadata.COMPLAINT_NUMBER || 'Unknown',
    complainant_name: metadata.complainant_name || metadata.VICTIM_NAME || 'Unknown',
    // Use the extracted date from the data, not the current date
    date_of_complaint: metadata.date || metadata.date_of_complaint || metadata.COMPLAINT_DATE || 'Unknown',
    total_amount: metadata.total_amount || metadata.amount || metadata.TOTAL_FRAUD_AMOUNT || '0',
    // Add category/subcategory information
    category: metadata.category || metadata.CATEGORY,
    subcategory: metadata.subcategory || metadata.SUBCATEGORY,
    ...metadata
  };

  // Log the processed metadata to debug
  console.log("Processed metadata for graph:", processedMetadata);

  // Build hierarchical structure from transactions
  const hierarchy: TransactionHierarchy = buildTransactionHierarchy(transactions);
  console.log("Built transaction hierarchy with", hierarchy.senderNodes.length, "sender nodes");

  // Node spacing configuration
  const horizontalSpacing = 80; // Space between nodes horizontally
  const nodeWidth = 350; // Width of each node
  const baseNodeHeight = 350; // Base height of a node

  // Calculate vertical spacing based on node complexity
  const calculateNodeHeight = (node: TransactionNode): number => {
    const txn = node.transaction;
    let height = baseNodeHeight;

    // Add height for multiple transactions
    if (txn.hasMultipleTransactions && txn.multipleTransactions) {
      // Base height for the multiple transactions section
      const multiTxnSectionHeight = 50;
      // Height per transaction - increased to account for expanded state
      const heightPerTxn = 150;
      // Calculate total height for multiple transactions - consider all transactions for better spacing
      height += multiTxnSectionHeight + txn.multipleTransactions.length * heightPerTxn;
    }

    // Add height for sub-transactions
    if (txn.subTransactions && txn.subTransactions.length > 0) {
      // Base height for the sub-transactions section
      const subTxnSectionHeight = 50;
      // Height per sub-transaction - increased to account for expanded state
      const heightPerSubTxn = 180;
      // Calculate total height for sub-transactions - consider all sub-transactions for better spacing
      height += subTxnSectionHeight + txn.subTransactions.length * heightPerSubTxn;
    }

    return height;
  };

  // Calculate vertical spacing for each layer
  const layerHeights: { [key: number]: number } = {};
  const verticalSpacings: { [key: number]: number } = {};

  // Initialize with base heights
  for (let i = -1; i <= 10; i++) { // Assuming max 10 layers
    layerHeights[i] = baseNodeHeight;
  }

  // Calculate max node height for each layer
  hierarchy.senderNodes.forEach(senderNode => {
    // Update layer 0 height
    layerHeights[0] = Math.max(layerHeights[0], calculateNodeHeight(senderNode));

    // Process each level of the hierarchy
    const processNodeForHeight = (node: TransactionNode, level: number) => {
      // Update max height for this layer
      layerHeights[level] = Math.max(layerHeights[level], calculateNodeHeight(node));

      // Process children
      node.children.forEach(childNode => {
        processNodeForHeight(childNode, level + 1);
      });
    };

    // Process all children of this sender
    senderNode.children.forEach(childNode => {
      processNodeForHeight(childNode, 1);
    });
  });

  // Calculate spacing between layers
  const minSpacing = 200; // Minimum spacing between layers
  const spacingFactor = 1.2; // Multiplier for spacing

  // For horizontal layout, we need more consistent spacing between columns
  if (horizontalLayout) {
    // Use a fixed horizontal spacing between columns for consistency
    const horizontalColumnSpacing = 400; // Fixed spacing between columns

    // Apply the same spacing for all layers in horizontal layout
    for (let i = -1; i < 10; i++) {
      verticalSpacings[i] = horizontalColumnSpacing;
    }
  } else {
    // For vertical layout, calculate spacing based on node heights
    for (let i = -1; i < 10; i++) {
      const currentLayerHeight = layerHeights[i] || baseNodeHeight;
      const nextLayerHeight = layerHeights[i + 1] || baseNodeHeight;

      // Calculate spacing based on the heights of both layers
      verticalSpacings[i] = Math.max(
        minSpacing,
        (currentLayerHeight + nextLayerHeight) / 2 * spacingFactor
      );
    }
  }

  // Create metadata node (root node) at the center
  const metadataNodeId = 'metadata_node';
  const metadataX = 0;
  const metadataY = 0;

  // Add metadata node
  nodes.push({
    id: metadataNodeId,
    type: 'metadata',
    data: {
      label: `Complaint: ${processedMetadata.complaint_number}`,
      account: processedMetadata.complainant_name,
      complaint_number: processedMetadata.complaint_number,
      date: processedMetadata.date_of_complaint,
      total_amount: processedMetadata.total_amount,
      complainant_name: processedMetadata.complainant_name,
      // Add category/subcategory information
      category: processedMetadata.category || processedMetadata.CATEGORY,
      subcategory: processedMetadata.subcategory || processedMetadata.SUBCATEGORY,
      // Add all possible metadata fields to ensure compatibility
      COMPLAINT_NUMBER: processedMetadata.complaint_number,
      VICTIM_NAME: processedMetadata.complainant_name,
      COMPLAINT_DATE: processedMetadata.date_of_complaint,
      TOTAL_FRAUD_AMOUNT: processedMetadata.total_amount,
      CATEGORY: processedMetadata.category || processedMetadata.CATEGORY,
      SUBCATEGORY: processedMetadata.subcategory || processedMetadata.SUBCATEGORY,
      // Add layout direction for handle positioning
      layoutDirection: horizontalLayout ? 'horizontal' : 'vertical'
    },
    position: { x: metadataX, y: metadataY }
  });

  // Track nodes by ID and account for edge creation
  const nodesByAccount: { [key: string]: string } = {};

  // Calculate the total leaf nodes across all senders
  const totalLeafNodes = hierarchy.senderNodes.reduce((sum, node) => sum + Math.max(node.leafNodeCount, 1), 0);

  // Calculate the total width needed based on leaf nodes
  const totalWidth = totalLeafNodes * nodeWidth + (totalLeafNodes - 1) * horizontalSpacing;

  // Calculate starting positions based on layout direction
  let currentX = -totalWidth / 2;
  let currentY = 0;

  // Position for sender nodes depends on layout direction
  const senderX = horizontalLayout ? metadataX + verticalSpacings[-1] : 0;
  const senderY = horizontalLayout ? 0 : metadataY + verticalSpacings[-1];

  // For horizontal layout, calculate total height needed for all senders
  if (horizontalLayout) {
    // Calculate the maximum node height to ensure proper spacing
    // Increased to account for expanded nodes with multiple transactions and sub-transactions
    const maxNodeHeight = 600; // Maximum height of a node - increased from 350

    // Calculate total height needed for all sender nodes with proper spacing
    const totalSenderHeight = hierarchy.senderNodes.reduce((sum, node) => {
      // Calculate height based on leaf nodes in the deepest layer
      const leafCount = Math.max(node.leafNodeCount, 1);
      // Each leaf node needs vertical space, but ensure minimum spacing between nodes
      return sum + (leafCount * maxNodeHeight);
    }, 0);

    // Add spacing between sender nodes - use larger spacing to prevent overlaps
    const verticalNodeSpacing = 100; // Increased spacing between nodes in the same column
    const totalSpacing = (hierarchy.senderNodes.length - 1) * verticalNodeSpacing;

    // Start from the top, centered vertically
    currentY = -(totalSenderHeight + totalSpacing) / 2;
  }

  // Create sender nodes (Layer 0)
  hierarchy.senderNodes.forEach((senderNode) => {
    const senderTransaction: Transaction = senderNode.transaction;
    const senderAccount = senderTransaction.sender_account || '';
    const bankName = senderTransaction.sender_bank || 'Unknown Bank';
    const nodeId = senderTransaction.id as string;

    // Calculate width/height allocation for this sender based on leaf nodes
    const leafCount = Math.max(senderNode.leafNodeCount, 1);
    const senderWidth = leafCount * nodeWidth + (leafCount - 1) * horizontalSpacing;
    const senderHeight = leafCount * baseNodeHeight;

    // Calculate position based on layout direction
    let nodePosX, nodePosY;

    if (horizontalLayout) {
      // In horizontal layout, senders are arranged vertically based on their tree size
      nodePosX = senderX;
      nodePosY = currentY + (senderHeight / 2); // Center the node in its allocated space

      // Store the vertical range this sender occupies for its children
      senderNode.transaction.verticalRange = {
        start: currentY,
        end: currentY + senderHeight,
        center: nodePosY
      };

      // Move down for next sender, with increased vertical spacing to prevent overlaps
      currentY += senderHeight + 100; // Use consistent vertical spacing between nodes in the same column
    } else {
      // In vertical layout, senders are arranged horizontally
      nodePosX = currentX + senderWidth / 2;
      nodePosY = senderY;
      currentX += senderWidth + horizontalSpacing; // Move right for next sender
    }

    // Create sender node
    nodes.push({
      id: nodeId,
      type: 'transaction',
      data: {
        ...senderTransaction,
        label: bankName,
        account: senderAccount,
        isSenderNode: true,
        isVictim: true,
        sender_account: senderAccount,
        sender_bank: bankName,
        type: senderTransaction.type || 'Money Transfer to',
        layer: 0,
        totalDescendants: senderNode.totalDescendants,
        layoutDirection: horizontalLayout ? 'horizontal' : 'vertical'
      },
      position: { x: nodePosX, y: nodePosY }
    });

    // Connect to metadata node
    edges.push({
      id: `edge_metadata_${nodeId}`,
      source: metadataNodeId,
      target: nodeId,
      animated: true,
      data: {
        isParentChild: true, // Mark as parent-child for direct connection
        layoutDirection: horizontalLayout ? 'horizontal' : 'vertical'
      },
      markerEnd: {
        type: MarkerType.ArrowClosed,
        width: 20,
        height: 20,
        color: '#3b82f6'
      }
    });

    // Track this node by account and ID
    if (senderAccount) {
      nodesByAccount[senderAccount] = nodeId;
    }
  });

  // Process transaction nodes in the hierarchy (recursive function)
  const processNode = (node: TransactionNode, parentId: string | null, level: number): void => {
    const txn: Transaction = node.transaction;
    const nodeId = txn.id as string;

    // Skip if this is a sender node (already processed)
    if (level === 0) return;

    // Find parent node to position children relative to parent
    let parentX = 0;
    let parentY = 0;

    if (parentId) {
      const parentNode = nodes.find(n => n.id === parentId);
      if (parentNode) {
        parentX = parentNode.position.x;
        parentY = parentNode.position.y;
      }
    }

    // Calculate position based on layout direction and level
    let nodeX, nodeY;

    if (horizontalLayout) {
      // In horizontal layout, levels increase from left to right
      nodeX = parentX;
      // Add up all the spacings for previous layers
      for (let i = 0; i < level; i++) {
        nodeX += verticalSpacings[i];
      }

      // Use the position info from the transaction if available, otherwise calculate based on parent
      nodeY = txn.positionY !== undefined ? txn.positionY : parentY;
    } else {
      // In vertical layout, levels increase from top to bottom
      nodeY = senderY;
      // Add up all the vertical spacings for previous layers
      for (let i = 0; i < level; i++) {
        nodeY += verticalSpacings[i];
      }

      // Use the position info from the transaction if available, otherwise use parent's X
      nodeX = txn.positionX !== undefined ? txn.positionX : parentX;
    }

    // Create the node
    nodes.push({
      id: nodeId,
      type: 'transaction',
      data: {
        ...txn,
        label: txn.receiver_bank || 'Unknown Bank',
        account: txn.receiver_account || '',
        layer: level,
        isMainTransaction: true,
        parentId: parentId, // Track parent ID
        totalDescendants: node.totalDescendants, // Add totalDescendants property
        layoutDirection: horizontalLayout ? 'horizontal' : 'vertical'
      },
      position: { x: nodeX, y: nodeY }
    });

    // Connect to parent node
    if (parentId) {
      edges.push({
        id: `edge_${parentId}_${nodeId}`,
        source: parentId,
        target: nodeId,
        animated: false,
        data: {
          isParentChild: true, // Flag for the edge renderer to use direct path
          layoutDirection: horizontalLayout ? 'horizontal' : 'vertical'
        },
        markerEnd: {
          type: MarkerType.ArrowClosed,
          width: 20,
          height: 20,
          color: '#3b82f6'
        }
      });
    }

    // Track this node by account
    if (txn.receiver_account) {
      nodesByAccount[txn.receiver_account] = nodeId;
    }

    // Process children
    if (node.children.length > 0) {
      // Calculate total leaf nodes for all children
      const totalChildLeafNodes = node.children.reduce((sum, child) =>
        sum + Math.max(child.leafNodeCount, 1), 0);

      // Calculate total width needed based on leaf nodes
      const totalChildWidth = totalChildLeafNodes * nodeWidth + (totalChildLeafNodes - 1) * horizontalSpacing;

      if (horizontalLayout) {
        // In horizontal layout, children are arranged vertically within parent's vertical range
        const verticalRange = txn.verticalRange || {
          start: nodeY - totalChildWidth / 2,
          end: nodeY + totalChildWidth / 2,
          center: nodeY
        };

        // Calculate the available vertical space
        const availableHeight = verticalRange.end - verticalRange.start;

        // Use a consistent vertical spacing between nodes in the same column
        // Increased spacing to prevent overlaps when nodes are expanded
        const verticalNodeSpacing = 250; // Increased from 100 to prevent overlaps

        // Calculate total height needed for all children with fixed spacing
        const totalChildrenHeight = (node.children.length * baseNodeHeight) +
                                   ((node.children.length - 1) * verticalNodeSpacing);

        // Start position - center the children vertically within the parent's range
        let childCurrentY = verticalRange.center - (totalChildrenHeight / 2);

        // If we have only one child, center it with the parent
        if (node.children.length === 1) {
          childCurrentY = verticalRange.center - (baseNodeHeight / 2);
        }

        // Process each child with proportional spacing
        node.children.forEach((childNode) => {
          // Calculate Y position
          const childY = childCurrentY + (baseNodeHeight / 2);

          // Calculate X position - move to the right by the spacing for this level
          const childX = nodeX + verticalSpacings[level];

          // Update child node's transaction with position info for rendering
          childNode.transaction.positionX = childX;
          childNode.transaction.positionY = childY;

          // Store the vertical range this child occupies for its own children
          const childLeafCount = Math.max(childNode.leafNodeCount, 1);

          // Calculate the vertical range proportionally to the child's leaf count
          // Add extra padding to account for expanded nodes
          const childRangeHeight = (childLeafCount / totalChildLeafNodes) * availableHeight;

          // Calculate node height based on complexity
          const nodeHeight = calculateNodeHeight(childNode);

          // Use the larger of calculated range height or actual node height
          const effectiveRangeHeight = Math.max(childRangeHeight, nodeHeight);

          childNode.transaction.verticalRange = {
            start: childY - (effectiveRangeHeight / 2),
            end: childY + (effectiveRangeHeight / 2),
            center: childY
          };

          // Update childCurrentY for next child
          // Use the calculated node height instead of baseNodeHeight to ensure proper spacing
          childCurrentY += calculateNodeHeight(childNode) + verticalNodeSpacing;

          // Process this child node
          processNode(childNode, nodeId, level + 1);
        });
      } else {
        // In vertical layout, children are arranged horizontally
        let childCurrentX = nodeX - totalChildWidth / 2;

        // Process each child with proportional spacing
        node.children.forEach((childNode) => {
          // Calculate width allocation for this child based on its leaf nodes
          const childLeafCount = Math.max(childNode.leafNodeCount, 1);
          const childWidth = childLeafCount * nodeWidth + (childLeafCount - 1) * horizontalSpacing;

          // Calculate X position (center of allocated space)
          const childX = childCurrentX + childWidth / 2;

          // Update child node's transaction with position info for rendering
          childNode.transaction.positionX = childX;
          childNode.transaction.positionY = nodeY + verticalSpacings[level];

          // Update childCurrentX for next child
          childCurrentX += childWidth + horizontalSpacing;

          // Process this child node
          processNode(childNode, nodeId, level + 1);
        });
      }
    }
  };

  // Process each sender node and its children
  hierarchy.senderNodes.forEach((senderNode: TransactionNode) => {
    const senderId = senderNode.transaction.id as string;

    // Find the sender node's position
    const senderNode_pos = nodes.find(n => n.id === senderId)?.position;
    if (!senderNode_pos) return;

    // Calculate total leaf nodes for all children
    const totalChildLeafNodes = senderNode.children.reduce((sum, child) =>
      sum + Math.max(child.leafNodeCount, 1), 0);

    // Calculate total width/height needed based on leaf nodes
    const totalChildSize = totalChildLeafNodes * nodeWidth + (totalChildLeafNodes - 1) * horizontalSpacing;

    if (horizontalLayout) {
      // In horizontal layout, children are arranged vertically within the sender's vertical range
      const verticalRange = senderNode.transaction.verticalRange || {
        start: senderNode_pos.y - totalChildSize / 2,
        end: senderNode_pos.y + totalChildSize / 2,
        center: senderNode_pos.y
      };

      // Calculate the available vertical space
      const availableHeight = verticalRange.end - verticalRange.start;

      // Use a consistent vertical spacing between nodes in the same column
      // Increased spacing to prevent overlaps when nodes are expanded
      const verticalNodeSpacing = 250; // Increased from 100 to prevent overlaps

      // Calculate total height needed for all children with fixed spacing
      const totalChildrenHeight = (senderNode.children.length * baseNodeHeight) +
                                 ((senderNode.children.length - 1) * verticalNodeSpacing);

      // Start position - center the children vertically within the sender's range
      let childCurrentY = verticalRange.center - (totalChildrenHeight / 2);

      // If we have only one child, center it with the sender
      if (senderNode.children.length === 1) {
        childCurrentY = verticalRange.center - (baseNodeHeight / 2);
      }

      // Process each child with proportional spacing
      senderNode.children.forEach((childNode: TransactionNode) => {
        // Calculate Y position
        const childY = childCurrentY + (baseNodeHeight / 2);

        // Update child node's transaction with position info for rendering
        childNode.transaction.positionX = senderNode_pos.x + verticalSpacings[0];
        childNode.transaction.positionY = childY;

        // Store the vertical range this child occupies for its own children
        const childLeafCount = Math.max(childNode.leafNodeCount, 1);

        // Calculate the vertical range proportionally to the child's leaf count
        // Add extra padding to account for expanded nodes
        const childRangeHeight = (childLeafCount / totalChildLeafNodes) * availableHeight;

        // Calculate node height based on complexity
        const nodeHeight = calculateNodeHeight(childNode);

        // Use the larger of calculated range height or actual node height
        const effectiveRangeHeight = Math.max(childRangeHeight, nodeHeight);

        childNode.transaction.verticalRange = {
          start: childY - (effectiveRangeHeight / 2),
          end: childY + (effectiveRangeHeight / 2),
          center: childY
        };

        // Update childCurrentY for next child
        // Use the calculated node height instead of baseNodeHeight to ensure proper spacing
        childCurrentY += calculateNodeHeight(childNode) + verticalNodeSpacing;

        // Process this child node and its descendants
        processNode(childNode, senderId, 1);
      });
    } else {
      // In vertical layout, children are arranged horizontally
      let childCurrentX = senderNode_pos.x - totalChildSize / 2;

      // Process each child with proportional spacing
      senderNode.children.forEach((childNode: TransactionNode) => {
        // Calculate width allocation for this child based on its leaf nodes
        const childLeafCount = Math.max(childNode.leafNodeCount, 1);
        const childWidth = childLeafCount * nodeWidth + (childLeafCount - 1) * horizontalSpacing;

        // Calculate X position (center of allocated space)
        const childX = childCurrentX + childWidth / 2;

        // Update child node's transaction with position info for rendering
        childNode.transaction.positionX = childX;
        childNode.transaction.positionY = senderY + verticalSpacings[0];

        // Update childCurrentX for next child
        childCurrentX += childWidth + horizontalSpacing;

        // Process this child node and its descendants
        processNode(childNode, senderId, 1);
      });
    }
  });

  // Position the metadata node appropriately based on layout
  const metadataNode = nodes.find(node => node.id === metadataNodeId);
  if (metadataNode) {
    if (horizontalLayout) {
      // In horizontal layout, position metadata node with consistent spacing from sender nodes
      // Find the first sender node's X position
      const firstSenderNode = nodes.find(node => node.data.layer === 0);
      if (firstSenderNode) {
        // Position metadata node with the same spacing as between other columns
        metadataNode.position.x = firstSenderNode.position.x - verticalSpacings[-1];
        metadataNode.position.y = 0; // Keep it vertically centered
      } else {
        // Fallback if no sender nodes found
        metadataNode.position.x = -400;
        metadataNode.position.y = 0;
      }
    } else {
      // In vertical layout, keep metadata node centered at the top
      metadataNode.position.x = 0; // Keep it horizontally centered
    }
  }

  return { nodes, edges };
};
