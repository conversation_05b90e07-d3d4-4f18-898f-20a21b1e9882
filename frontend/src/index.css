@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Base styles */
@layer base {
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  }

  code, pre, kbd, samp {
    font-family: 'JetBrains Mono', 'Roboto Mono', Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
  }

  html, body, #root {
    width: 100%;
    height: 100%;
    overflow: hidden;
    max-width: 100vw;
  }

  /* Responsive font sizes */
  html {
    font-size: 16px;
  }

  @media (max-width: 768px) {
    html {
      font-size: 14px;
    }
  }

  @media (max-width: 480px) {
    html {
      font-size: 12px;
    }
  }

  /* Theme-based styling */
  body {
    background-color: var(--theme-bg-primary);
    color: var(--theme-text);
    position: relative;
  }

  @keyframes gradient {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  @keyframes scanLine {
    0% {
      transform: translateY(-100%);
    }
    100% {
      transform: translateY(100%);
    }
  }

  @keyframes glowPulse {
    0%, 100% {
      opacity: 0.3;
      background: radial-gradient(circle at 50% 50%, rgba(0, 170, 255, 0.05) 0%, rgba(0, 0, 0, 0) 70%);
    }
    50% {
      opacity: 0.6;
      background: radial-gradient(circle at 50% 50%, rgba(0, 170, 255, 0.15) 0%, rgba(0, 0, 0, 0) 70%);
    }
  }

  /* Text colors based on theme - more consistent approach */
  .text-gray-700 {
    color: var(--theme-text) !important;
    opacity: 0.9;
  }

  .text-gray-600 {
    color: var(--theme-text) !important;
    opacity: 0.8;
  }

  .text-gray-500 {
    color: var(--theme-text) !important;
    opacity: 0.7;
  }

  .text-gray-400 {
    color: var(--theme-text) !important;
    opacity: 0.6;
  }

  /* Table styles */
  .hoverable-table tr:hover {
    background-color: var(--theme-glow);
    opacity: 0.7;
  }

  .striped-table tr:nth-child(even) {
    background-color: var(--theme-glow);
    opacity: 0.3;
  }
}

/* Fix for mobile viewport height issues */
.vh-100 {
  height: 100vh;
  height: calc(var(--vh, 1vh) * 100);
}

/* React Flow container fix */
.react-flow-wrapper {
  width: 100%;
  height: 100%;
  min-height: 500px;
}

.react-flow {
  width: 100%;
  height: 100%;
  background-color: #f7f7f7 !important; /* Muted white background instead of pure white */
}

/* Dark mode graph background */
.theme-darkmode .react-flow {
  background-color: #1a1c2a !important;
}

/* Ensure graph node colors are preserved during processing */
.react-flow__node {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease !important;
}

/* Preserve node colors in dark mode */
.theme-darkmode .bg-blue-900\/30 { background-color: rgba(30, 58, 138, 0.3) !important; }
.theme-darkmode .border-blue-700 { border-color: #1d4ed8 !important; }
.theme-darkmode .text-blue-400 { color: #60a5fa !important; }

.theme-darkmode .bg-red-900\/30 { background-color: rgba(127, 29, 29, 0.3) !important; }
.theme-darkmode .border-red-700 { border-color: #b91c1c !important; }
.theme-darkmode .text-red-400 { color: #f87171 !important; }

.theme-darkmode .bg-purple-900\/30 { background-color: rgba(88, 28, 135, 0.3) !important; }
.theme-darkmode .border-purple-700 { border-color: #7e22ce !important; }
.theme-darkmode .text-purple-400 { color: #c084fc !important; }

.theme-darkmode .bg-green-900\/30 { background-color: rgba(20, 83, 45, 0.3) !important; }
.theme-darkmode .border-green-700 { border-color: #15803d !important; }
.theme-darkmode .text-green-400 { color: #4ade80 !important; }

.theme-darkmode .bg-amber-900\/30 { background-color: rgba(120, 53, 15, 0.3) !important; }
.theme-darkmode .border-amber-700 { border-color: #b45309 !important; }
.theme-darkmode .text-amber-400 { color: #fbbf24 !important; }

.theme-darkmode .bg-indigo-900\/30 { background-color: rgba(49, 46, 129, 0.3) !important; }
.theme-darkmode .border-indigo-700 { border-color: #4338ca !important; }
.theme-darkmode .text-indigo-400 { color: #818cf8 !important; }

/* Excel-like grid styles */
.excel-grid table {
  border-collapse: collapse !important;
  width: 100% !important;
  border-spacing: 0 !important;
}

.excel-grid table th,
.excel-grid table td {
  border: 1px solid var(--theme-border) !important;
  box-sizing: border-box !important;
}

.excel-grid table th {
  font-weight: 600 !important;
  height: 40px !important; /* Increased header height */
  padding: 10px 8px !important; /* Increased padding */
  background-color: var(--theme-accent) !important;
  color: var(--theme-text) !important;
  letter-spacing: 0.01em !important;
}

/* Force table borders to be visible */
.excel-grid table,
.excel-grid table th,
.excel-grid table td {
  border-style: solid !important;
  border-width: 1px !important;
  border-color: var(--theme-border) !important;
}

/* Override any conflicting styles */
.excel-grid table td > div,
.excel-grid table th > div {
  border: none !important;
}

/* CSV Preview Table specific styles */
.csv-preview-table table {
  border-collapse: collapse !important;
}

.csv-preview-table table th,
.csv-preview-table table td {
  border: 0.5px solid var(--theme-border) !important;
  padding: 2px !important;
}

/* Direct CSS for ReactTableCSVPreview */
#root .ReactTableCSVPreview table,
#root .ReactTableCSVPreview th,
#root .ReactTableCSVPreview td {
  border: 0.5px solid var(--theme-border) !important;
}

#root .ReactTableCSVPreview table {
  table-layout: fixed !important;
  width: 100% !important;
  display: table !important;
}

#root .ReactTableCSVPreview table tr {
  display: table-row !important;
}

#root .ReactTableCSVPreview table th,
#root .ReactTableCSVPreview table td {
  display: table-cell !important;
}

#root .ReactTableCSVPreview table th {
  height: 40px !important; /* Increased header height */
  padding: 8px 4px !important; /* Increased padding */
  background-color: var(--theme-accent) !important;
  color: var(--theme-text) !important;
  font-weight: 600 !important;
}

/* Force table styling for complaint detail page */
.complaint-details-container table,
.complaint-details-container th,
.complaint-details-container td {
  border: 0.5px solid var(--theme-border) !important;
  padding: 4px !important;
}

.complaint-details-container th {
  height: 40px !important; /* Increased header height */
  background-color: var(--theme-accent) !important;
  color: var(--theme-text) !important;
  font-weight: 600 !important;
  padding: 8px 4px !important;
}

/* Direct styling for excel-table class */
.excel-table {
  border-collapse: collapse !important;
  border-spacing: 0 !important;
  background-color: transparent !important;
  table-layout: fixed !important;
  width: 100% !important;
  display: table !important;
}

.excel-table tr {
  display: table-row !important;
}

.excel-table th,
.excel-table td {
  display: table-cell !important;
}

/* Cell styling */
.excel-table th {
  border: 0.5px solid var(--theme-border) !important;
  padding: 8px 4px !important;
  height: 40px !important; /* Increased header height */
  background-color: var(--theme-accent) !important;
  color: var(--theme-text) !important;
  font-weight: 600 !important;
}

.excel-table td {
  background-color: var(--theme-bg-card) !important;
  border: 0.5px solid var(--theme-border) !important;
  padding: 4px !important;
  color: var(--theme-text) !important;
  transition: none !important;
}

/* Row styling */
.excel-table tr {
  background-color: transparent !important;
}

/* Force table styling */
.excel-table,
.excel-table * {
  box-sizing: border-box !important;
}

/* Global table styling to ensure consistency */
table {
  table-layout: fixed !important;
  width: 100% !important;
  display: table !important;
  border-collapse: collapse !important;
}

table tr {
  display: table-row !important;
}

table th,
table td {
  display: table-cell !important;
}

table th {
  height: 40px !important;
  border: 1px solid var(--theme-border) !important;
  background-color: var(--theme-accent) !important;
  color: var(--theme-text) !important;
  font-weight: 600 !important;
  padding: 10px 8px !important;
  letter-spacing: 0.01em !important;
}

table td {
  border: 1px solid var(--theme-border) !important;
  color: var(--theme-text) !important;
  padding: 8px !important;
  transition: none !important;
}

/* Add subtle shadow to tables */
.excel-grid,
.ReactTableCSVPreview,
.complaint-details-container {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
  border-radius: 6px !important;
  overflow: hidden !important;
}

/* Fix DashboardTable borders */
td[style*="border:"],
td[style*="border-color:"],
th[style*="border:"],
th[style*="border-color:"] {
  border: 1px solid var(--theme-border) !important;
  border-color: var(--theme-border) !important;
}

/* Add glow effect to action buttons */
button[aria-label="View complaint"],
button[aria-label="Edit complaint"] {
  box-shadow: 0 2px 6px var(--theme-glow) !important;
  transition: all 0.2s ease-in-out !important;
}

button[aria-label="View complaint"]:hover,
button[aria-label="Edit complaint"]:hover {
  box-shadow: 0 4px 12px var(--theme-glow) !important;
  transform: translateY(-1px) !important;
}

button[aria-label="Delete complaint"] {
  box-shadow: 0 2px 6px rgba(244, 67, 54, 0.2) !important;
  opacity: 0.5;
  transition: all 0.2s ease-in-out !important;
}

button[aria-label="Delete complaint"]:hover {
  opacity: 0.8;
  box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3) !important;
}

/* Force all table cells to have the correct border color */
table td, table th,
td[style*="border"], th[style*="border"],
.excel-grid td, .excel-grid th,
.ReactTableCSVPreview td, .ReactTableCSVPreview th,
.complaint-details-container td, .complaint-details-container th {
  border: 1px solid var(--theme-border) !important;
}

/* Add hover effect to table rows - optimized to prevent blinking */
table tr:hover td {
  background-color: rgba(0, 0, 0, 0.02) !important;
  transition: none !important;
}

.theme-darkmode table tr:hover td {
  background-color: rgba(255, 255, 255, 0.03) !important;
  transition: none !important;
}

/* Theme colors with consistent black text */
:root {
  /* 🎨 1. Off-White */
  --theme-offwhite-bg-primary: #eae3e3;
  --theme-offwhite-bg-card: #f4eaea;
  --theme-offwhite-bg-lighter: #ffffffe5;
  --theme-offwhite-border: #E0E0E0;
  --theme-offwhite-accent: #d5d0d0;
  --theme-offwhite-glow: rgba(204, 204, 204, 0.3);
  --theme-offwhite-text: #333333;
  --theme-offwhite-text-muted: #666666;
  --theme-offwhite-button: #CCCCCC;
  --theme-offwhite-button-hover: #AAAAAA;
  --theme-offwhite-sidebar-darker: #a6a4a4;

  /* 🎨 2. Mint Green - More muted for eye protection */
  --theme-mint-bg-primary: #a8d9bc;
  --theme-mint-bg-card: #d8efe0;
  --theme-mint-bg-lighter: #e8f5ec;
  --theme-mint-border: #8abca0;
  --theme-mint-accent: #66a080;
  --theme-mint-glow: rgba(102, 160, 128, 0.2);
  --theme-mint-text: #333333;
  --theme-mint-text-muted: #4d5d52;
  --theme-mint-button: #66a080;
  --theme-mint-button-hover: #4d8066;
  --theme-mint-sidebar-darker: #66a080;

  /* 🎨 3. Lavender - Even more muted for eye protection */
  --theme-lavender-bg-primary: #d8d0e0;
  --theme-lavender-bg-card: #e6e0eb;
  --theme-lavender-bg-lighter: #f0ebf2;
  --theme-lavender-border: #c0b8cc;
  --theme-lavender-accent: #9f90aa;
  --theme-lavender-glow: rgba(159, 144, 170, 0.15);
  --theme-lavender-text: #333333;
  --theme-lavender-text-muted: #5d5266;
  --theme-lavender-button: #9f90aa;
  --theme-lavender-button-hover: #8a7a96;
  --theme-lavender-sidebar-darker: #9f90aa;

  /* 🎨 4. Light Blue - Even more muted for eye protection */
  --theme-lightblue-bg-primary: #c0d8e6;
  --theme-lightblue-bg-card: #d8e6f0;
  --theme-lightblue-bg-lighter: #e6eef5;
  --theme-lightblue-border: #a8c0d0;
  --theme-lightblue-accent: #7a9cb3;
  --theme-lightblue-glow: rgba(122, 156, 179, 0.15);
  --theme-lightblue-text: #333333;
  --theme-lightblue-text-muted: #4d5d66;
  --theme-lightblue-button: #7a9cb3;
  --theme-lightblue-button-hover: #6889a3;
  --theme-lightblue-sidebar-darker: #7a9cb3;

  /* 🎨 5. Light Yellow - Even more muted for eye protection */
  --theme-lightyellow-bg-primary: #e0e0c0;
  --theme-lightyellow-bg-card: #eeeed8;
  --theme-lightyellow-bg-lighter: #f5f5e6;
  --theme-lightyellow-border: #d0d0b0;
  --theme-lightyellow-accent: #b0b090;
  --theme-lightyellow-glow: rgba(176, 176, 144, 0.15);
  --theme-lightyellow-text: #333333;
  --theme-lightyellow-text-muted: #66664d;
  --theme-lightyellow-button: #b0b090;
  --theme-lightyellow-button-hover: #9c9c7d;
  --theme-lightyellow-sidebar-darker: #b0b090;

  /* 🎨 6. Light Coral - Even more muted for eye protection */
  --theme-lightcoral-bg-primary: #e0c0c0;
  --theme-lightcoral-bg-card: #eed8d8;
  --theme-lightcoral-bg-lighter: #f5e6e6;
  --theme-lightcoral-border: #d0b0b0;
  --theme-lightcoral-accent: #b09090;
  --theme-lightcoral-glow: rgba(176, 144, 144, 0.15);
  --theme-lightcoral-text: #333333;
  --theme-lightcoral-text-muted: #664d4d;
  --theme-lightcoral-button: #b09090;
  --theme-lightcoral-button-hover: #9c7d7d;
  --theme-lightcoral-sidebar-darker: #b09090;

  /* 🎨 7. Periwinkle - Even more muted for eye protection */
  --theme-periwinkle-bg-primary: #d0d0e0;
  --theme-periwinkle-bg-card: #e0e0eb;
  --theme-periwinkle-bg-lighter: #ebebf2;
  --theme-periwinkle-border: #b8b8cc;
  --theme-periwinkle-accent: #9090aa;
  --theme-periwinkle-glow: rgba(144, 144, 170, 0.15);
  --theme-periwinkle-text: #333333;
  --theme-periwinkle-text-muted: #52526b;
  --theme-periwinkle-button: #9090aa;
  --theme-periwinkle-button-hover: #7a7a96;
  --theme-periwinkle-sidebar-darker: #9090aa;

  /* 🎨 8. Light Gray - Even more muted for eye protection */
  --theme-lightgray-bg-primary: #e0e0e0;
  --theme-lightgray-bg-card: #e8e8e8;
  --theme-lightgray-bg-lighter: #f2f2f2;
  --theme-lightgray-border: #c8c8c8;
  --theme-lightgray-accent: #a0a0a0;
  --theme-lightgray-glow: rgba(160, 160, 160, 0.15);
  --theme-lightgray-text: #333333;
  --theme-lightgray-text-muted: #595959;
  --theme-lightgray-button: #a0a0a0;
  --theme-lightgray-button-hover: #888888;
  --theme-lightgray-sidebar-darker: #a0a0a0;

  /* 🎨 9. Light Purple - Even more muted for eye protection */
  --theme-lightpurple-bg-primary: #d8c0e0;
  --theme-lightpurple-bg-card: #e6d8eb;
  --theme-lightpurple-bg-lighter: #f0e8f2;
  --theme-lightpurple-border: #c0a8cc;
  --theme-lightpurple-accent: #a890aa;
  --theme-lightpurple-glow: rgba(168, 144, 170, 0.15);
  --theme-lightpurple-text: #333333;
  --theme-lightpurple-text-muted: #5d4d66;
  --theme-lightpurple-button: #a890aa;
  --theme-lightpurple-button-hover: #967a96;
  --theme-lightpurple-sidebar-darker: #a890aa;

  /* 🎨 10. Light Cyan - Even more muted for eye protection */
  --theme-lightcyan-bg-primary: #c0e0e0;
  --theme-lightcyan-bg-card: #d8ebeb;
  --theme-lightcyan-bg-lighter: #e6f2f2;
  --theme-lightcyan-border: #a8c8c8;
  --theme-lightcyan-accent: #90a8a8;
  --theme-lightcyan-glow: rgba(144, 168, 168, 0.15);
  --theme-lightcyan-text: #333333;
  --theme-lightcyan-text-muted: #4d6666;
  --theme-lightcyan-button: #90a8a8;
  --theme-lightcyan-button-hover: #7a9696;
  --theme-lightcyan-sidebar-darker: #90a8a8;

  /* 🎨 11. Light Peach - Even more muted for eye protection */
  --theme-lightpeach-bg-primary: #e0d0c0;
  --theme-lightpeach-bg-card: #ebe0d8;
  --theme-lightpeach-bg-lighter: #f2ebe6;
  --theme-lightpeach-border: #c8b8a8;
  --theme-lightpeach-accent: #b0a090;
  --theme-lightpeach-glow: rgba(176, 160, 144, 0.15);
  --theme-lightpeach-text: #333333;
  --theme-lightpeach-text-muted: #66524d;
  --theme-lightpeach-button: #b0a090;
  --theme-lightpeach-button-hover: #9c8c7d;
  --theme-lightpeach-sidebar-darker: #b0a090;

  /* 🎨 12. Dark Mode - GitHub/Vercel inspired with reduced contrast */
  --theme-darkmode-bg-primary: #1a1c28;
  --theme-darkmode-bg-card: #252836;
  --theme-darkmode-bg-lighter: #2f3142;
  --theme-darkmode-border: #2d2f40;
  --theme-darkmode-accent: #3b4772;
  --theme-darkmode-glow: rgba(59, 71, 114, 0.2);
  --theme-darkmode-text: #e0e0ea;
  --theme-darkmode-text-muted: #9090a8;
  --theme-darkmode-button: #3b4772;
  --theme-darkmode-button-hover: #4a5a8a;
  --theme-darkmode-sidebar-darker: #141620;
}

/* Theme color classes */
:root {
  /* Default theme (Light Blue) */
  --theme-bg-primary: var(--theme-lightblue-bg-primary);
  --theme-bg-card: var(--theme-lightblue-bg-card);
  --theme-bg-lighter: var(--theme-lightblue-bg-lighter);
  --theme-border: var(--theme-lightblue-border);
  --theme-accent: var(--theme-lightblue-accent);
  --theme-glow: var(--theme-lightblue-glow);
  --theme-text: var(--theme-lightblue-text);
  --theme-text-muted: var(--theme-lightblue-text-muted);
  --theme-button: var(--theme-lightblue-button);
  --theme-button-hover: var(--theme-lightblue-button-hover);

  /* Additional theme variables for components */
  --theme-sidebar-bg: var(--theme-lightblue-sidebar-darker);
  --theme-sidebar-darker: var(--theme-lightblue-sidebar-darker);
  --theme-sidebar-border: var(--theme-lightblue-border);
  --theme-sidebar-text: var(--theme-lightblue-text);
  --theme-header-bg: var(--theme-lightblue-accent);
  --theme-input-bg: var(--theme-lightblue-bg-card);
  --theme-input-border: var(--theme-lightblue-border);
  --theme-input-text: var(--theme-lightblue-text);
  --theme-success-color: #4CAF50;
  --theme-warning-color: #FF9800;
  --theme-error-color: #F44336;
  --theme-info-color: #2196F3;
}



.theme-offwhite {
  --theme-bg-primary: var(--theme-offwhite-bg-primary);
  --theme-bg-card: var(--theme-offwhite-bg-card);
  --theme-bg-lighter: var(--theme-offwhite-bg-lighter);
  --theme-border: var(--theme-offwhite-border);
  --theme-accent: var(--theme-offwhite-accent);
  --theme-glow: var(--theme-offwhite-glow);
  --theme-text: var(--theme-offwhite-text);
  --theme-text-muted: var(--theme-offwhite-text-muted);
  --theme-button: var(--theme-offwhite-button);
  --theme-button-hover: var(--theme-offwhite-button-hover);
  --theme-sidebar-bg: var(--theme-offwhite-sidebar-darker);
  --theme-sidebar-darker: var(--theme-offwhite-sidebar-darker);
  --theme-sidebar-border: var(--theme-offwhite-border);
  --theme-sidebar-text: var(--theme-offwhite-text);
  --theme-header-bg: var(--theme-offwhite-accent);
  --theme-input-bg: var(--theme-offwhite-bg-card);
  --theme-input-border: var(--theme-offwhite-border);
  --theme-input-text: var(--theme-offwhite-text);
}

/* New theme classes */

.theme-mint {
  --theme-bg-primary: var(--theme-mint-bg-primary);
  --theme-bg-card: var(--theme-mint-bg-card);
  --theme-bg-lighter: var(--theme-mint-bg-lighter);
  --theme-border: var(--theme-mint-border);
  --theme-accent: var(--theme-mint-accent);
  --theme-glow: var(--theme-mint-glow);
  --theme-text: var(--theme-mint-text);
  --theme-text-muted: var(--theme-mint-text-muted);
  --theme-button: var(--theme-mint-button);
  --theme-button-hover: var(--theme-mint-button-hover);
  --theme-sidebar-bg: var(--theme-mint-sidebar-darker);
  --theme-sidebar-darker: var(--theme-mint-sidebar-darker);
  --theme-sidebar-border: var(--theme-mint-border);
  --theme-sidebar-text: var(--theme-mint-text);
  --theme-header-bg: var(--theme-mint-accent);
  --theme-input-bg: var(--theme-mint-bg-card);
  --theme-input-border: var(--theme-mint-border);
  --theme-input-text: var(--theme-mint-text);
}

.theme-lavender {
  --theme-bg-primary: var(--theme-lavender-bg-primary);
  --theme-bg-card: var(--theme-lavender-bg-card);
  --theme-bg-lighter: var(--theme-lavender-bg-lighter);
  --theme-border: var(--theme-lavender-border);
  --theme-accent: var(--theme-lavender-accent);
  --theme-glow: var(--theme-lavender-glow);
  --theme-text: var(--theme-lavender-text);
  --theme-text-muted: var(--theme-lavender-text-muted);
  --theme-button: var(--theme-lavender-button);
  --theme-button-hover: var(--theme-lavender-button-hover);
  --theme-sidebar-bg: var(--theme-lavender-sidebar-darker);
  --theme-sidebar-darker: var(--theme-lavender-sidebar-darker);
  --theme-sidebar-border: var(--theme-lavender-border);
  --theme-sidebar-text: var(--theme-lavender-text);
  --theme-header-bg: var(--theme-lavender-accent);
  --theme-input-bg: var(--theme-lavender-bg-card);
  --theme-input-border: var(--theme-lavender-border);
  --theme-input-text: var(--theme-lavender-text);
}

.theme-lightblue {
  --theme-bg-primary: var(--theme-lightblue-bg-primary);
  --theme-bg-card: var(--theme-lightblue-bg-card);
  --theme-bg-lighter: var(--theme-lightblue-bg-lighter);
  --theme-border: var(--theme-lightblue-border);
  --theme-accent: var(--theme-lightblue-accent);
  --theme-glow: var(--theme-lightblue-glow);
  --theme-text: var(--theme-lightblue-text);
  --theme-text-muted: var(--theme-lightblue-text-muted);
  --theme-button: var(--theme-lightblue-button);
  --theme-button-hover: var(--theme-lightblue-button-hover);
  --theme-sidebar-bg: var(--theme-lightblue-sidebar-darker);
  --theme-sidebar-darker: var(--theme-lightblue-sidebar-darker);
  --theme-sidebar-border: var(--theme-lightblue-border);
  --theme-sidebar-text: var(--theme-lightblue-text);
  --theme-header-bg: var(--theme-lightblue-accent);
  --theme-input-bg: var(--theme-lightblue-bg-card);
  --theme-input-border: var(--theme-lightblue-border);
  --theme-input-text: var(--theme-lightblue-text);
}

.theme-lightyellow {
  --theme-bg-primary: var(--theme-lightyellow-bg-primary);
  --theme-bg-card: var(--theme-lightyellow-bg-card);
  --theme-bg-lighter: var(--theme-lightyellow-bg-lighter);
  --theme-border: var(--theme-lightyellow-border);
  --theme-accent: var(--theme-lightyellow-accent);
  --theme-glow: var(--theme-lightyellow-glow);
  --theme-text: var(--theme-lightyellow-text);
  --theme-text-muted: var(--theme-lightyellow-text-muted);
  --theme-button: var(--theme-lightyellow-button);
  --theme-button-hover: var(--theme-lightyellow-button-hover);
  --theme-sidebar-bg: var(--theme-lightyellow-sidebar-darker);
  --theme-sidebar-darker: var(--theme-lightyellow-sidebar-darker);
  --theme-sidebar-border: var(--theme-lightyellow-border);
  --theme-sidebar-text: var(--theme-lightyellow-text);
  --theme-header-bg: var(--theme-lightyellow-accent);
  --theme-input-bg: var(--theme-lightyellow-bg-card);
  --theme-input-border: var(--theme-lightyellow-border);
  --theme-input-text: var(--theme-lightyellow-text);
}

.theme-lightcoral {
  --theme-bg-primary: var(--theme-lightcoral-bg-primary);
  --theme-bg-card: var(--theme-lightcoral-bg-card);
  --theme-bg-lighter: var(--theme-lightcoral-bg-lighter);
  --theme-border: var(--theme-lightcoral-border);
  --theme-accent: var(--theme-lightcoral-accent);
  --theme-glow: var(--theme-lightcoral-glow);
  --theme-text: var(--theme-lightcoral-text);
  --theme-text-muted: var(--theme-lightcoral-text-muted);
  --theme-button: var(--theme-lightcoral-button);
  --theme-button-hover: var(--theme-lightcoral-button-hover);
  --theme-sidebar-bg: var(--theme-lightcoral-sidebar-darker);
  --theme-sidebar-darker: var(--theme-lightcoral-sidebar-darker);
  --theme-sidebar-border: var(--theme-lightcoral-border);
  --theme-sidebar-text: var(--theme-lightcoral-text);
  --theme-header-bg: var(--theme-lightcoral-accent);
  --theme-input-bg: var(--theme-lightcoral-bg-card);
  --theme-input-border: var(--theme-lightcoral-border);
  --theme-input-text: var(--theme-lightcoral-text);
}

.theme-periwinkle {
  --theme-bg-primary: var(--theme-periwinkle-bg-primary);
  --theme-bg-card: var(--theme-periwinkle-bg-card);
  --theme-bg-lighter: var(--theme-periwinkle-bg-lighter);
  --theme-border: var(--theme-periwinkle-border);
  --theme-accent: var(--theme-periwinkle-accent);
  --theme-glow: var(--theme-periwinkle-glow);
  --theme-text: var(--theme-periwinkle-text);
  --theme-text-muted: var(--theme-periwinkle-text-muted);
  --theme-button: var(--theme-periwinkle-button);
  --theme-button-hover: var(--theme-periwinkle-button-hover);
  --theme-sidebar-bg: var(--theme-periwinkle-sidebar-darker);
  --theme-sidebar-darker: var(--theme-periwinkle-sidebar-darker);
  --theme-sidebar-border: var(--theme-periwinkle-border);
  --theme-sidebar-text: var(--theme-periwinkle-text);
  --theme-header-bg: var(--theme-periwinkle-accent);
  --theme-input-bg: var(--theme-periwinkle-bg-card);
  --theme-input-border: var(--theme-periwinkle-border);
  --theme-input-text: var(--theme-periwinkle-text);
}

.theme-lightgray {
  --theme-bg-primary: var(--theme-lightgray-bg-primary);
  --theme-bg-card: var(--theme-lightgray-bg-card);
  --theme-bg-lighter: var(--theme-lightgray-bg-lighter);
  --theme-border: var(--theme-lightgray-border);
  --theme-accent: var(--theme-lightgray-accent);
  --theme-glow: var(--theme-lightgray-glow);
  --theme-text: var(--theme-lightgray-text);
  --theme-text-muted: var(--theme-lightgray-text-muted);
  --theme-button: var(--theme-lightgray-button);
  --theme-button-hover: var(--theme-lightgray-button-hover);
  --theme-sidebar-bg: var(--theme-lightgray-sidebar-darker);
  --theme-sidebar-darker: var(--theme-lightgray-sidebar-darker);
  --theme-sidebar-border: var(--theme-lightgray-border);
  --theme-sidebar-text: var(--theme-lightgray-text);
  --theme-header-bg: var(--theme-lightgray-accent);
  --theme-input-bg: var(--theme-lightgray-bg-card);
  --theme-input-border: var(--theme-lightgray-border);
  --theme-input-text: var(--theme-lightgray-text);
}

.theme-lightpurple {
  --theme-bg-primary: var(--theme-lightpurple-bg-primary);
  --theme-bg-card: var(--theme-lightpurple-bg-card);
  --theme-bg-lighter: var(--theme-lightpurple-bg-lighter);
  --theme-border: var(--theme-lightpurple-border);
  --theme-accent: var(--theme-lightpurple-accent);
  --theme-glow: var(--theme-lightpurple-glow);
  --theme-text: var(--theme-lightpurple-text);
  --theme-text-muted: var(--theme-lightpurple-text-muted);
  --theme-button: var(--theme-lightpurple-button);
  --theme-button-hover: var(--theme-lightpurple-button-hover);
  --theme-sidebar-bg: var(--theme-lightpurple-sidebar-darker);
  --theme-sidebar-darker: var(--theme-lightpurple-sidebar-darker);
  --theme-sidebar-border: var(--theme-lightpurple-border);
  --theme-sidebar-text: var(--theme-lightpurple-text);
  --theme-header-bg: var(--theme-lightpurple-accent);
  --theme-input-bg: var(--theme-lightpurple-bg-card);
  --theme-input-border: var(--theme-lightpurple-border);
  --theme-input-text: var(--theme-lightpurple-text);
}

.theme-lightcyan {
  --theme-bg-primary: var(--theme-lightcyan-bg-primary);
  --theme-bg-card: var(--theme-lightcyan-bg-card);
  --theme-bg-lighter: var(--theme-lightcyan-bg-lighter);
  --theme-border: var(--theme-lightcyan-border);
  --theme-accent: var(--theme-lightcyan-accent);
  --theme-glow: var(--theme-lightcyan-glow);
  --theme-text: var(--theme-lightcyan-text);
  --theme-text-muted: var(--theme-lightcyan-text-muted);
  --theme-button: var(--theme-lightcyan-button);
  --theme-button-hover: var(--theme-lightcyan-button-hover);
  --theme-sidebar-bg: var(--theme-lightcyan-sidebar-darker);
  --theme-sidebar-darker: var(--theme-lightcyan-sidebar-darker);
  --theme-sidebar-border: var(--theme-lightcyan-border);
  --theme-sidebar-text: var(--theme-lightcyan-text);
  --theme-header-bg: var(--theme-lightcyan-accent);
  --theme-input-bg: var(--theme-lightcyan-bg-card);
  --theme-input-border: var(--theme-lightcyan-border);
  --theme-input-text: var(--theme-lightcyan-text);
}

.theme-lightpeach {
  --theme-bg-primary: var(--theme-lightpeach-bg-primary);
  --theme-bg-card: var(--theme-lightpeach-bg-card);
  --theme-bg-lighter: var(--theme-lightpeach-bg-lighter);
  --theme-border: var(--theme-lightpeach-border);
  --theme-accent: var(--theme-lightpeach-accent);
  --theme-glow: var(--theme-lightpeach-glow);
  --theme-text: var(--theme-lightpeach-text);
  --theme-text-muted: var(--theme-lightpeach-text-muted);
  --theme-button: var(--theme-lightpeach-button);
  --theme-button-hover: var(--theme-lightpeach-button-hover);
  --theme-sidebar-bg: var(--theme-lightpeach-sidebar-darker);
  --theme-sidebar-darker: var(--theme-lightpeach-sidebar-darker);
  --theme-sidebar-border: var(--theme-lightpeach-border);
  --theme-sidebar-text: var(--theme-lightpeach-text);
  --theme-header-bg: var(--theme-lightpeach-accent);
  --theme-input-bg: var(--theme-lightpeach-bg-card);
  --theme-input-border: var(--theme-lightpeach-border);
  --theme-input-text: var(--theme-lightpeach-text);
}

/* Dark Mode Theme with sci-fi elements */
.theme-darkmode {
  --theme-bg-primary: var(--theme-darkmode-bg-primary);
  --theme-bg-card: var(--theme-darkmode-bg-card);
  --theme-bg-lighter: var(--theme-darkmode-bg-lighter);
  --theme-border: var(--theme-darkmode-border);
  --theme-accent: var(--theme-darkmode-accent);
  --theme-glow: var(--theme-darkmode-glow);
  --theme-text: var(--theme-darkmode-text);
  --theme-text-muted: var(--theme-darkmode-text-muted);
  --theme-button: var(--theme-darkmode-button);
  --theme-button-hover: var(--theme-darkmode-button-hover);
  --theme-sidebar-bg: var(--theme-darkmode-sidebar-darker);
  --theme-sidebar-darker: var(--theme-darkmode-sidebar-darker);
  --theme-sidebar-border: var(--theme-darkmode-border);
  --theme-sidebar-text: var(--theme-darkmode-text);
  --theme-header-bg: var(--theme-darkmode-accent);
  --theme-input-bg: var(--theme-darkmode-bg-card);
  --theme-input-border: var(--theme-darkmode-border);
  --theme-input-text: var(--theme-darkmode-text);
}

/* Subtle animated stars for dark mode - inspired by GitHub's dark theme */
.theme-darkmode body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  background-image:
    radial-gradient(0.8px 0.8px at 25px 5px, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)),
    radial-gradient(0.8px 0.8px at 50px 25px, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)),
    radial-gradient(0.8px 0.8px at 125px 20px, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)),
    radial-gradient(1px 1px at 50px 75px, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)),
    radial-gradient(1.2px 1.2px at 175px 125px, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)),
    radial-gradient(1.5px 1.5px at 20px 150px, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)),
    radial-gradient(0.8px 0.8px at 200px 220px, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)),
    radial-gradient(1px 1px at 120px 250px, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)),
    radial-gradient(0.5px 0.5px at 200px 280px, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0));
  background-repeat: repeat;
  background-size: 400px 400px;
  opacity: 0.08;
  z-index: -1;
  animation: starsMovement 180s linear infinite;
}

@keyframes starsMovement {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 400px 400px;
  }
}

/* Add second layer of stars for parallax effect */
.theme-darkmode body::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  background-image:
    radial-gradient(0.7px 0.7px at 150px 15px, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0)),
    radial-gradient(0.7px 0.7px at 90px 40px, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0)),
    radial-gradient(0.7px 0.7px at 230px 85px, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0)),
    radial-gradient(1px 1px at 110px 120px, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0)),
    radial-gradient(1.2px 1.2px at 10px 170px, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0));
  background-repeat: repeat;
  background-size: 400px 400px;
  opacity: 0.05;
  z-index: -1;
  animation: starsMovement2 240s linear infinite;
}

@keyframes starsMovement2 {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: -400px -400px;
  }
}

/* GitHub-inspired glassmorphic effect for dark mode */
.theme-darkmode .card,
.theme-darkmode .bg-white,
.theme-darkmode [class*="bg-card"] {
  background: rgba(35, 37, 50, 0.4) !important;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(65, 70, 100, 0.1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.theme-darkmode .card:hover,
.theme-darkmode .bg-white:hover,
.theme-darkmode [class*="bg-card"]:hover {
  box-shadow: 0 6px 20px rgba(65, 70, 100, 0.15);
  border: 1px solid rgba(65, 70, 100, 0.15);
}

/* Improved table styling in dark mode - inspired by GitHub and Vercel */
.theme-darkmode table,
.theme-darkmode .table {
  background-color: rgba(35, 37, 50, 0.2) !important;
  border-color: rgba(65, 70, 100, 0.1) !important;
}

.theme-darkmode th {
  background-color: rgba(35, 37, 50, 0.4) !important;
  color: var(--theme-darkmode-text) !important;
  border-color: rgba(65, 70, 100, 0.15) !important;
}

.theme-darkmode td {
  border-color: rgba(65, 70, 100, 0.1) !important;
}

/* Improved header styling in dark mode */
.theme-darkmode header,
.theme-darkmode .header,
.theme-darkmode [class*="header"] {
  background-color: rgba(30, 32, 45, 0.7) !important;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(65, 70, 100, 0.15) !important;
}

/* Apply theme colors to elements */
body {
  background-color: var(--theme-bg-primary);
  color: var(--theme-text);
}

/* Card styles */
.card, .bg-white, [class*="bg-white"] {
  background-color: var(--theme-bg-card) !important;
  border-color: var(--theme-border) !important;
}

/* Sidebar styles */
aside, .sidebar, [class*="sidebar"] {
  background-color: var(--theme-sidebar-bg) !important;
  border-color: var(--theme-sidebar-border) !important;
  color: var(--theme-sidebar-text) !important;
}

/* Header styles - improved consistency */
header, .header, [class*="header"] {
  background-color: var(--theme-header-bg) !important;
  border-color: var(--theme-border) !important;
  color: var(--theme-text) !important;
}

/* Fix for header text colors */
header h1, header h2, header h3, header h4, header h5, header h6,
.header h1, .header h2, .header h3, .header h4, .header h5, .header h6,
[class*="header"] h1, [class*="header"] h2, [class*="header"] h3,
[class*="header"] h4, [class*="header"] h5, [class*="header"] h6 {
  color: var(--theme-text) !important;
}

/* Button styles */
.btn-primary, button.primary, [class*="btn-primary"] {
  background-color: var(--theme-button) !important;
  color: white !important;
  border-color: var(--theme-button) !important;
}

.btn-primary:hover, button.primary:hover, [class*="btn-primary"]:hover {
  background-color: var(--theme-button-hover) !important;
  border-color: var(--theme-button-hover) !important;
}

/* Secondary button styles */
.btn-secondary, button.secondary, [class*="btn-secondary"] {
  background-color: transparent !important;
  color: var(--theme-text) !important;
  border-color: var(--theme-border) !important;
}

.btn-secondary:hover, button.secondary:hover, [class*="btn-secondary"]:hover {
  background-color: var(--theme-bg-primary) !important;
  border-color: var(--theme-accent) !important;
}

/* Input styles */
input, textarea, select, .input, [class*="input"] {
  background-color: var(--theme-input-bg) !important;
  border-color: var(--theme-input-border) !important;
  color: var(--theme-input-text) !important;
}

input:focus, textarea:focus, select:focus {
  border-color: var(--theme-accent) !important;
  box-shadow: 0 0 0 2px var(--theme-glow) !important;
}

/* Link styles */
a, .link, [class*="link"] {
  color: var(--theme-accent) !important;
}

a:hover, .link:hover, [class*="link"]:hover {
  color: var(--theme-button-hover) !important;
}

/* Text colors - consistent styling */
.text-primary, [class*="text-primary"] {
  color: var(--theme-text) !important;
}

.text-secondary, [class*="text-secondary"] {
  color: var(--theme-text) !important;
  opacity: 0.8;
}

/* Fix for dark mode text colors */
.theme-darkmode .text-gray-700,
.theme-darkmode .text-gray-600,
.theme-darkmode .text-gray-500,
.theme-darkmode .text-gray-400,
.theme-darkmode .text-primary,
.theme-darkmode .text-secondary,
.theme-darkmode [class*="text-gray-"],
.theme-darkmode [class*="text-primary"],
.theme-darkmode [class*="text-secondary"] {
  color: var(--theme-darkmode-text) !important;
}

/* Border colors */
.border, [class*="border"] {
  border-color: var(--theme-border) !important;
}

/* Background colors */
.bg-primary, [class*="bg-primary"] {
  background-color: var(--theme-bg-primary) !important;
}

.bg-secondary, [class*="bg-secondary"] {
  background-color: var(--theme-bg-card) !important;
}

/* Remove any Tailwind dark mode classes */
[class*="dark:bg-"], [class*="dark:text-"], [class*="dark:border-"] {
  display: inherit !important; /* Override with neutral property */
}

/* Apply theme colors to tables and cells */
table td, table th,
td[style*="border"], th[style*="border"],
.excel-grid td, .excel-grid th,
.ReactTableCSVPreview td, .ReactTableCSVPreview th,
.complaint-details-container td, .complaint-details-container th {
  border: 1px solid var(--theme-border) !important;
  color: var(--theme-text) !important;
}

/* Fix DashboardTable cell borders */
td[style*="border: isDark"],
th[style*="border: isDark"] {
  border: 1px solid var(--theme-border) !important;
}

/* Standardized table header background colors */
table th,
th[style*="backgroundColor"],
thead th,
.excel-grid th,
.ReactTableCSVPreview th,
.complaint-details-container th {
  background-color: var(--theme-accent) !important;
  color: var(--theme-text) !important;
  font-weight: 600 !important;
  padding: 8px 4px !important;
  height: 40px !important;
}

/* Complaint details page specific fixes */
.complaint-details-container {
  width: 100% !important;
  box-sizing: border-box !important;
}

.complaint-details-content {
  width: 100% !important;
  box-sizing: border-box !important;
}

.complaint-details-table {
  width: 100% !important;
  box-sizing: border-box !important;
}

/* Page transition effects - optimized to prevent blinking */
.page-transition {
  position: relative;
  width: 100%;
  transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
  opacity: 1;
  transform: translateY(0);
}

.page-transition.fadeIn {
  opacity: 1;
  transform: translateY(0);
}

.page-transition.fadeOut {
  opacity: 0;
  transform: translateY(-10px);
}

/* Disable animations for table components to prevent blinking */
.excel-grid,
.ReactTableCSVPreview,
.complaint-details-container {
  animation: none !important;
  transition: none !important;
}

/* Cyberpunk-inspired utility classes */
.cyber-card {
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease-in-out;
  transform: translateY(0);
}

.cyber-card:hover {
  transform: translateY(-5px);
}

/* Light mode cyber card */
.cyber-card.light {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 170, 255, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.cyber-card.light:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  border-color: rgba(0, 170, 255, 0.4);
}

/* Dark mode cyber card */
.dark .cyber-card {
  background: rgba(16, 16, 30, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 170, 255, 0.3);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.dark .cyber-card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3), 0 0 15px rgba(0, 170, 255, 0.3);
  border-color: rgba(0, 170, 255, 0.5);
}

/* Cyber grid background - completely removed */

/* Cyber dots background - completely removed */

/* Cyber glow effect */
.cyber-glow {
  position: relative;
}

.cyber-glow::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: inherit;
  box-shadow: 0 0 15px rgba(0, 170, 255, 0.5);
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
  pointer-events: none;
  z-index: -1;
}

.cyber-glow:hover::after {
  opacity: 1;
}

/* Cyber text with gradient */
.cyber-text-gradient {
  background: linear-gradient(135deg, #00DDFF 0%, #0088FF 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

.cyber-text-gradient-purple {
  background: linear-gradient(135deg, #FF5CFF 0%, #CC00CC 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

.cyber-text-gradient-green {
  background: linear-gradient(135deg, #66FFBB 0%, #00AA44 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

/* Sidebar toggle button styles */
.sidebar-toggle-btn {
  transition: all 0.3s ease-in-out;
  opacity: 0;
  transform: translateX(0);
  position: relative;
  border-top-left-radius: 20px !important;
  border-bottom-left-radius: 20px !important;
  border-top-right-radius: 20px !important;
  border-bottom-right-radius: 20px !important;
}

.sidebar-toggle-btn::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 20px;
  background: transparent;
  z-index: -1;
  transition: all 0.3s ease-in-out;
  opacity: 0;
}

aside:hover .sidebar-toggle-btn {
  opacity: 0.9;
  transform: translateX(0);
}

.sidebar-toggle-btn:hover {
  opacity: 1;
  transform: scale(1.05);
  box-shadow: 0 0 12px var(--theme-glow) !important;
}

.sidebar-toggle-btn:hover::after {
  background: radial-gradient(circle, var(--theme-glow) 0%, transparent 70%);
  opacity: 0.6;
}

/* Make the toggle button always visible on mobile */
@media (max-width: 768px) {
  .sidebar-toggle-btn {
    opacity: 0.9;
  }
}

:root {
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 16px; /* Base font size */
  line-height: 1.5;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  letter-spacing: -0.011em; /* Slight letter spacing adjustment for Inter */
}

a {
  font-weight: 500;
  color: var(--theme-accent); /* Use theme accent color */
  text-decoration: inherit;
  transition: all 0.2s ease-in-out;
  position: relative;
}
a:hover {
  color: var(--theme-button-hover); /* Use theme hover color */
}

/* Special links with glow effect */
a.glow-link {
  position: relative;
  color: var(--theme-accent);
  text-decoration: none;
  padding: 0 2px;
}
a.glow-link:hover {
  color: var(--theme-button-hover);
  text-shadow: 0 0 8px var(--theme-glow);
}
a.glow-link::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  bottom: -2px;
  left: 0;
  background-color: var(--theme-accent);
  transform: scaleX(0);
  transform-origin: bottom right;
  transition: transform 0.3s ease-out;
  box-shadow: 0 0 8px var(--theme-glow);
}
a.glow-link:hover::after {
  transform: scaleX(1);
  transform-origin: bottom left;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  overflow: hidden;
}

#root {
  width: 100%;
  min-height: 100vh;
  overflow: hidden;
}

/* Typography System */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

/* Page title */
h1 {
  font-size: 1.75rem; /* 28px */
  line-height: 1.2;
  letter-spacing: -0.025em;
}

/* Section title */
h2 {
  font-size: 1.5rem; /* 24px */
  line-height: 1.3;
  letter-spacing: -0.025em;
}

/* Card title */
h3 {
  font-size: 1.25rem; /* 20px */
  line-height: 1.4;
  letter-spacing: -0.015em;
}

/* Subsection title */
h4 {
  font-size: 1.125rem; /* 18px */
  line-height: 1.5;
  letter-spacing: -0.015em;
}

/* Small title */
h5 {
  font-size: 1rem; /* 16px */
  line-height: 1.5;
}

/* Tiny title */
h6 {
  font-size: 0.875rem; /* 14px */
  line-height: 1.5;
}

/* Body text */
p {
  font-size: 1rem; /* 16px */
  line-height: 1.5;
  margin-bottom: 0.5rem;
}

/* Small text */
.text-sm {
  font-size: 0.875rem; /* 14px */
  line-height: 1.5;
}

/* Large text */
.text-lg {
  font-size: 1.125rem; /* 18px */
  line-height: 1.5;
}

/* Extra large text */
.text-xl {
  font-size: 1.375rem; /* 22px */
  line-height: 1.3;
}

/* Label text */
label {
  font-size: 1rem; /* 16px */
  line-height: 1.5;
  font-weight: 500;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: var(--theme-button);
  color: white;
  cursor: pointer;
  transition: all 0.25s ease-in-out;
  letter-spacing: -0.011em; /* Slight letter spacing adjustment for Inter */
  position: relative;
  overflow: hidden;
  transform: translateY(0);
}

button:hover {
  background-color: var(--theme-button-hover);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

button:active {
  transform: translateY(0);
}

button:focus,
button:focus-visible {
  outline: none;
  box-shadow: 0 0 0 3px var(--theme-glow);
}

/* Primary button */
button.primary {
  background-color: var(--theme-button);
  color: white;
  border: none;
}

button.primary:hover {
  background-color: var(--theme-button-hover);
  box-shadow: 0 0 15px var(--theme-glow);
}

/* Secondary button */
button.secondary {
  background-color: var(--theme-bg-card);
  color: var(--theme-text);
  border: 1px solid var(--theme-border);
}

button.secondary:hover {
  background-color: var(--theme-bg-primary);
  border-color: var(--theme-accent);
  box-shadow: 0 0 10px var(--theme-glow);
}

/* Glassmorphism utility classes */
.glass {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid var(--theme-border);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

/* Shadow effects */
.shadow-glow {
  box-shadow: 0 0 10px var(--theme-glow);
}

.shadow-glow-sm {
  box-shadow: 0 0 5px var(--theme-glow);
}

.shadow-glow-lg {
  box-shadow: 0 0 15px var(--theme-glow), 0 0 30px var(--theme-glow);
}
