import React from 'react';
import { twMerge } from 'tailwind-merge';

type StatusType = 'pending' | 'processing' | 'resolved' | 'rejected';

interface StatusBadgeProps {
  status: StatusType;
  size?: 'small' | 'medium';
  className?: string;
}

const TailwindStatusBadge: React.FC<StatusBadgeProps> = ({ 
  status, 
  size = 'medium',
  className 
}) => {
  const getStatusConfig = (status: StatusType) => {
    switch (status) {
      case 'pending':
        return {
          label: 'Pending',
          textColor: 'text-amber-800 dark:text-amber-300',
          bgColor: 'bg-amber-100 dark:bg-amber-900/30',
          borderColor: 'border-amber-200 dark:border-amber-800/50',
        };
      case 'processing':
        return {
          label: 'Processing',
          textColor: 'text-blue-800 dark:text-blue-300',
          bgColor: 'bg-blue-100 dark:bg-blue-900/30',
          borderColor: 'border-blue-200 dark:border-blue-800/50',
        };
      case 'resolved':
        return {
          label: 'Resolved',
          textColor: 'text-green-800 dark:text-green-300',
          bgColor: 'bg-green-100 dark:bg-green-900/30',
          borderColor: 'border-green-200 dark:border-green-800/50',
        };
      case 'rejected':
        return {
          label: 'Rejected',
          textColor: 'text-red-800 dark:text-red-300',
          bgColor: 'bg-red-100 dark:bg-red-900/30',
          borderColor: 'border-red-200 dark:border-red-800/50',
        };
      default:
        return {
          label: status.charAt(0).toUpperCase() + status.slice(1),
          textColor: 'text-gray-800 dark:text-gray-300',
          bgColor: 'bg-gray-100 dark:bg-gray-800/50',
          borderColor: 'border-gray-200 dark:border-gray-700/50',
        };
    }
  };

  const { label, textColor, bgColor, borderColor } = getStatusConfig(status);
  
  const sizeClasses = {
    small: 'text-xs px-2 py-0.5',
    medium: 'text-sm px-2.5 py-1',
  };

  return (
    <span
      className={twMerge(
        'inline-flex items-center justify-center rounded-full font-medium border',
        bgColor,
        textColor,
        borderColor,
        sizeClasses[size],
        'transition-all duration-200',
        className
      )}
    >
      {label}
    </span>
  );
};

export default TailwindStatusBadge;
