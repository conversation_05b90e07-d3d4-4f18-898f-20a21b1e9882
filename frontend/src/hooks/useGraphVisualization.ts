import { useState, useEffect } from 'react';
import complaintService from '../services/complaintService';
import { useAlert } from '../context/TailwindAlertContext';

interface GraphData {
  metadata: any;
  transactions: any[];
}

/**
 * Custom hook for handling graph visualization data
 * @param complaintId The ID of the complaint to fetch graph data for
 * @returns Object containing loading state, error state, and graph data
 */
export const useGraphVisualization = (complaintId: string | undefined) => {
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [graphData, setGraphData] = useState<GraphData | null>(null);
  const { showError } = useAlert();

  useEffect(() => {
    const fetchGraphData = async () => {
      if (!complaintId) {
        setError('No complaint ID provided');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        console.log('Fetching graph data for complaint ID:', complaintId);

        // Fetch graph data from API
        const response = await complaintService.getComplaintGraphData(complaintId);
        console.log('Graph data response received:', response);

        if (!response) {
          throw new Error('No data received from server');
        }

        // Extract metadata and transactions from the response
        const metadata = response.metadata || {};
        console.log('Metadata:', metadata);
        
        // Check if graph_data exists and has the right structure
        if (!response.graph_data) {
          console.error('No graph_data in response:', response);
          throw new Error('No graph data available for this complaint');
        }
        
        // Extract transactions from graph_data
        let transactions = [];
        if (response.graph_data.transactions) {
          transactions = response.graph_data.transactions;
        } else if (typeof response.graph_data === 'string') {
          // Try to parse graph_data if it's a string
          try {
            const parsedData = JSON.parse(response.graph_data);
            transactions = parsedData.transactions || [];
          } catch (parseErr) {
            console.error('Failed to parse graph_data string:', parseErr);
            throw new Error('Invalid graph data format');
          }
        }
        
        console.log('Transactions:', transactions);

        if (!metadata) {
          throw new Error('No metadata available');
        }

        // Set graph data even if transactions is empty - we'll show a message in the UI
        setGraphData({ metadata, transactions });
        
        // Log success
        console.log('Graph data successfully processed and set');
      } catch (err: any) {
        console.error('Error fetching graph data:', err);
        setError(err.message || 'Failed to load graph data');
        showError('Failed to load graph data: ' + (err.message || 'Unknown error'));
      } finally {
        setLoading(false);
      }
    };

    fetchGraphData();
  }, [complaintId, showError]);

  return { loading, error, graphData };
};

export default useGraphVisualization;
