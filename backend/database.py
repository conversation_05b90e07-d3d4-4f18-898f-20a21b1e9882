import os
import logging
import asyncio
import time
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List, TypeVar, cast
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
from dotenv import load_dotenv
from utils.query_sanitizer import sanitize_query, sanitize_mongodb_id

# Type definitions
T = TypeVar('T')

# Load environment variables
load_dotenv()

# Setup logging
logger = logging.getLogger(__name__)

# Database metrics tracking
db_metrics = {
    "connection_attempts": 0,
    "successful_connections": 0,
    "failed_connections": 0,
    "reconnection_attempts": 0,
    "successful_reconnections": 0,
    "failed_reconnections": 0,
    "last_connection_time": None,
    "last_connection_error": None,
    "query_count": 0,
    "slow_queries": 0,
    "query_errors": 0,
    "average_query_time_ms": 0,
    "total_query_time_ms": 0,
    "connection_pool_size": 0,
    "connection_pool_in_use": 0
}

# Slow query threshold in milliseconds
SLOW_QUERY_THRESHOLD_MS = int(os.getenv("SLOW_QUERY_THRESHOLD_MS", "500"))

# Query history for diagnostics (limited size)
MAX_QUERY_HISTORY = 100
query_history = []

# Get MongoDB connection details from environment variables
DATABASE_URL = os.getenv("DATABASE_URL")
DATABASE_NAME = os.getenv("DATABASE_NAME", "sahayak")

# Validate database configuration
if not DATABASE_URL:
    logger.critical("DATABASE_URL environment variable is not set")
    raise ValueError("DATABASE_URL environment variable is required")

# Configure MongoDB client with proper connection pooling, timeouts, and security
try:
    # Update metrics
    db_metrics["connection_attempts"] += 1
    connection_start_time = time.time()

    # Connection options with enhanced security and optimized for 500 users
    connection_options = {
        # Connection pool settings - optimized for 500 users
        "maxPoolSize": int(os.getenv("DB_MAX_POOL_SIZE", "50")),  # Optimized for 500 users
        "minPoolSize": int(os.getenv("DB_MIN_POOL_SIZE", "10")),   # Optimized for better performance
        "maxIdleTimeMS": int(os.getenv("DB_MAX_IDLE_TIME_MS", "3600000")),  # Increased to 1 hour to match session timeout

        # Timeout settings - adjusted for production
        "connectTimeoutMS": int(os.getenv("DB_CONNECT_TIMEOUT_MS", "3000")),  # Reduced for faster failure detection
        "socketTimeoutMS": int(os.getenv("DB_SOCKET_TIMEOUT_MS", "45000")),   # Increased for long-running operations
        "serverSelectionTimeoutMS": int(os.getenv("DB_SERVER_SELECTION_TIMEOUT_MS", "5000")),

        # Connection health monitoring
        "heartbeatFrequencyMS": int(os.getenv("DB_HEARTBEAT_FREQUENCY_MS", "10000")),  # Check server health every 10 seconds

        # Write concern and retry settings
        "retryWrites": True,
        "w": "majority",  # Ensure writes are acknowledged by a majority of replicas
        "journal": True,  # Ensure writes are written to the journal

        # TLS/SSL settings - enable in production
        "tls": os.getenv("ENVIRONMENT") == "production" or os.getenv("DB_USE_TLS") == "true",
        "tlsAllowInvalidCertificates": os.getenv("DB_ALLOW_INVALID_CERT") == "true",

        # Authentication settings
        "authSource": os.getenv("DB_AUTH_SOURCE", "admin"),

        # Application name for monitoring
        "appName": "CyberSakha"
    }

    # Store pool size in metrics
    db_metrics["connection_pool_size"] = connection_options["maxPoolSize"]

    # Connect to MongoDB with optimized and secure settings
    client = AsyncIOMotorClient(DATABASE_URL, **connection_options)
    db = client[DATABASE_NAME]

    # Update metrics for successful connection
    db_metrics["successful_connections"] += 1
    db_metrics["last_connection_time"] = datetime.now(timezone.utc).isoformat()
    connection_time_ms = (time.time() - connection_start_time) * 1000
    logger.info(f"MongoDB connection established in {connection_time_ms:.2f}ms")

    # Log connection but don't expose full connection string
    masked_url = DATABASE_URL.split("@")[-1] if DATABASE_URL and "@" in DATABASE_URL else "localhost"
    logger.info(f"Connected to MongoDB database: {DATABASE_NAME} at {masked_url}")
except Exception as e:
    # Update metrics for failed connection
    db_metrics["failed_connections"] += 1
    db_metrics["last_connection_error"] = str(e)
    logger.critical(f"Failed to connect to MongoDB: {str(e)}")
    client = None
    db = None

def get_db():
    """
    Get the database instance with connection status check

    This function checks if the database connection is available and logs appropriate warnings.
    It doesn't attempt reconnection since it's synchronous, but it provides detailed diagnostics.

    Returns:
        AsyncIOMotorDatabase: The database instance or None if not connected
    """
    # No need for global declaration as we're not assigning to these variables
    if db is None:
        logger.warning("Database connection not available - connection has not been established")
        return None

    # Check if client is connected without making an async call
    # This is a basic check that doesn't guarantee the connection is fully working
    try:
        if client is None:
            logger.warning("Database client is None")
            return None

        if not hasattr(client, 'nodes') or not client.nodes:
            logger.warning("Database connection appears to be broken - no nodes available")
            return None

        # We can't do a full connection check in a synchronous function
        # The caller should handle potential errors
    except Exception as e:
        logger.warning(f"Database connection check failed: {str(e)}")
        # Still return db as the connection might recover automatically
        # The caller should handle potential errors

    # Return the database instance
    return db

async def try_reconnect(max_retries=3, retry_delay=2):
    """
    Try to reconnect to the database if the connection is lost

    Enhanced for production with 500 users:
    - Implements retry mechanism with exponential backoff
    - Properly closes existing connections
    - Validates connection after establishing it
    - Detailed logging for diagnostics
    - Tracks metrics for monitoring and diagnostics

    Args:
        max_retries (int): Maximum number of reconnection attempts
        retry_delay (int): Initial delay between retries in seconds (doubles with each retry)

    Returns:
        bool: True if reconnection was successful, False otherwise
    """
    global client, db

    # Update metrics
    db_metrics["reconnection_attempts"] += 1
    reconnection_start_time = time.time()

    # Get the connection options once to avoid duplication
    connection_options = {
        # Connection pool settings - optimized for 500 users
        "maxPoolSize": int(os.getenv("DB_MAX_POOL_SIZE", "50")),  # Optimized for 500 users
        "minPoolSize": int(os.getenv("DB_MIN_POOL_SIZE", "10")),   # Optimized for better performance
        "maxIdleTimeMS": int(os.getenv("DB_MAX_IDLE_TIME_MS", "3600000")),  # Increased to 1 hour to match session timeout

        # Timeout settings - adjusted for production
        "connectTimeoutMS": int(os.getenv("DB_CONNECT_TIMEOUT_MS", "3000")),  # Reduced for faster failure detection
        "socketTimeoutMS": int(os.getenv("DB_SOCKET_TIMEOUT_MS", "45000")),   # Increased for long-running operations
        "serverSelectionTimeoutMS": int(os.getenv("DB_SERVER_SELECTION_TIMEOUT_MS", "5000")),

        # Connection health monitoring
        "heartbeatFrequencyMS": int(os.getenv("DB_HEARTBEAT_FREQUENCY_MS", "10000")),  # Check server health every 10 seconds

        # Write concern and retry settings
        "retryWrites": True,
        "w": "majority",
        "journal": True,

        # TLS/SSL settings
        "tls": os.getenv("ENVIRONMENT") == "production" or os.getenv("DB_USE_TLS") == "true",
        "tlsAllowInvalidCertificates": os.getenv("DB_ALLOW_INVALID_CERT") == "true",

        # Authentication settings
        "authSource": os.getenv("DB_AUTH_SOURCE", "admin"),

        # Application name for monitoring
        "appName": "CyberSakha"
    }

    # Store pool size in metrics
    db_metrics["connection_pool_size"] = connection_options["maxPoolSize"]

    # Try to close existing client first if it exists
    if client is not None:
        try:
            logger.info("Closing existing MongoDB client before reconnection")
            client.close()
        except Exception as e:
            logger.error(f"Error closing existing MongoDB client: {str(e)}")

    # Set to None before reconnection attempts
    client = None
    db = None

    # Implement retry with exponential backoff
    for attempt in range(1, max_retries + 1):
        try:
            attempt_start_time = time.time()
            logger.info(f"Attempting to reconnect to MongoDB (attempt {attempt}/{max_retries})")

            # Create new client
            client = AsyncIOMotorClient(DATABASE_URL, **connection_options)
            db = client[DATABASE_NAME]

            # Test the connection with a ping command
            ping_start = time.time()
            await client.admin.command('ping')
            ping_time_ms = (time.time() - ping_start) * 1000
            logger.info(f"MongoDB ping successful in {ping_time_ms:.2f}ms")

            # Further validate connection by listing databases
            db_list_start = time.time()
            databases = await client.list_database_names()
            db_list_time_ms = (time.time() - db_list_start) * 1000
            logger.info(f"Listed {len(databases)} databases in {db_list_time_ms:.2f}ms")

            # Log connection but don't expose full connection string
            masked_url = DATABASE_URL.split("@")[-1] if DATABASE_URL and "@" in DATABASE_URL else "localhost"
            attempt_time_ms = (time.time() - attempt_start_time) * 1000
            logger.info(f"Successfully reconnected to MongoDB database: {DATABASE_NAME} at {masked_url} (attempt {attempt}/{max_retries}) in {attempt_time_ms:.2f}ms")

            # Update metrics for successful reconnection
            db_metrics["successful_reconnections"] += 1
            db_metrics["last_connection_time"] = datetime.now(timezone.utc).isoformat()
            total_reconnection_time_ms = (time.time() - reconnection_start_time) * 1000
            logger.info(f"Total reconnection time: {total_reconnection_time_ms:.2f}ms")

            # We'll recreate indexes after successful connection
            # This will be done outside this function to avoid circular references
            logger.info("Connection established - indexes will be created separately")

            return True

        except Exception as e:
            logger.error(f"Failed to reconnect to MongoDB (attempt {attempt}/{max_retries}): {str(e)}")

            # Clean up failed connection
            if client:
                try:
                    client.close()
                except:
                    pass

            client = None
            db = None

            # If we have more retries, wait before the next attempt with exponential backoff
            if attempt < max_retries:
                current_delay = retry_delay * (2 ** (attempt - 1))  # Exponential backoff
                logger.info(f"Waiting {current_delay} seconds before next reconnection attempt")
                await asyncio.sleep(current_delay)

    # If we get here, all retries failed
    db_metrics["failed_reconnections"] += 1
    db_metrics["last_connection_error"] = f"Failed after {max_retries} reconnection attempts"
    logger.critical(f"All {max_retries} reconnection attempts to MongoDB failed")
    return False

async def check_connection(detailed=False):
    """
    Check if the database connection is working

    Enhanced for production with 500 users:
    - Performs multiple checks for more accurate diagnostics
    - Can return detailed status information
    - Logs comprehensive error information

    Args:
        detailed (bool): If True, returns a dict with detailed status info instead of a boolean

    Returns:
        bool or dict: True if connected, False otherwise. If detailed=True, returns a dict with status info
    """
    if client is None or db is None:
        status = {
            "connected": False,
            "error": "Database client or database object is None",
            "details": None
        }
        logger.error("Database connection check failed: client or db is None")
        return status if detailed else False

    try:
        # Perform a series of checks
        status = {
            "connected": False,
            "ping": False,
            "list_databases": False,
            "error": None,
            "details": {}
        }

        # Check 1: Ping the database
        try:
            ping_start = asyncio.get_event_loop().time()
            await client.admin.command('ping')
            ping_time = asyncio.get_event_loop().time() - ping_start
            status["ping"] = True
            status["details"]["ping_time_ms"] = round(ping_time * 1000, 2)
        except Exception as e:
            status["error"] = f"Ping failed: {str(e)}"
            logger.error(f"Database ping check failed: {str(e)}")
            return status if detailed else False

        # Check 2: List databases
        try:
            db_start = asyncio.get_event_loop().time()
            databases = await client.list_database_names()
            db_time = asyncio.get_event_loop().time() - db_start
            status["list_databases"] = True
            status["details"]["list_db_time_ms"] = round(db_time * 1000, 2)
            status["details"]["database_count"] = len(databases)
        except Exception as e:
            status["error"] = f"List databases failed: {str(e)}"
            logger.error(f"Database list check failed: {str(e)}")
            return status if detailed else False

        # Check 3: Get server info
        try:
            server_info = await client.admin.command('serverStatus')
            status["details"]["server_version"] = server_info.get("version", "unknown")
            status["details"]["connections"] = server_info.get("connections", {}).get("current", 0)
        except Exception as e:
            # This is not a critical check, so we don't fail if it doesn't work
            logger.warning(f"Server status check failed: {str(e)}")
            status["details"]["server_info_error"] = str(e)

        # All critical checks passed
        status["connected"] = True
        logger.debug(f"Database connection check successful: {status}")
        return status if detailed else True

    except Exception as e:
        status = {
            "connected": False,
            "error": f"Unexpected error: {str(e)}",
            "details": None
        }
        logger.error(f"Database connection check failed with unexpected error: {str(e)}")
        return status if detailed else False

async def retry_db_operation(operation_func, max_retries=3, retry_delay=1):
    """
    Retry a database operation with exponential backoff

    Args:
        operation_func (callable): Async function to execute
        max_retries (int): Maximum number of retry attempts
        retry_delay (int): Initial delay between retries in seconds (doubles with each retry)

    Returns:
        Any: Result of the operation function

    Raises:
        Exception: The last exception encountered after all retries fail
    """
    last_error = None

    for attempt in range(1, max_retries + 1):
        try:
            # Execute the operation
            return await operation_func()
        except Exception as e:
            last_error = e

            # Check if this is the last attempt
            if attempt >= max_retries:
                logger.error(f"Operation failed after {max_retries} attempts: {str(e)}")
                raise

            # Calculate delay with exponential backoff
            current_delay = retry_delay * (2 ** (attempt - 1))

            # Log the error and retry
            logger.warning(f"Database operation failed (attempt {attempt}/{max_retries}): {str(e)}")
            logger.info(f"Retrying in {current_delay} seconds...")

            # Wait before retrying
            await asyncio.sleep(current_delay)

    # This should never be reached due to the raise in the loop, but just in case
    if last_error:
        raise last_error
    raise Exception("Unknown error in retry_db_operation")

async def track_query_performance(collection_name, operation, query=None):
    """
    Track database query performance for monitoring and diagnostics

    Args:
        collection_name (str): Name of the collection being queried
        operation (str): Type of operation (find, insert, update, delete, etc.)
        query (dict, optional): The query parameters (for debugging)

    Returns:
        callable: A context manager for tracking query performance
    """
    class QueryTracker:
        def __init__(self, collection, op, q):
            self.collection = collection
            self.operation = op
            self.query = q
            self.start_time = None

        async def __aenter__(self):
            self.start_time = time.time()
            return self

        async def __aexit__(self, exc_type, exc_val, _):
            # Calculate query time
            if self.start_time is not None:
                query_time_ms = (time.time() - self.start_time) * 1000
            else:
                query_time_ms = 0

            # Update metrics
            db_metrics["query_count"] += 1
            db_metrics["total_query_time_ms"] += query_time_ms
            db_metrics["average_query_time_ms"] = db_metrics["total_query_time_ms"] / db_metrics["query_count"]

            # Check if this is a slow query
            if query_time_ms > SLOW_QUERY_THRESHOLD_MS:
                db_metrics["slow_queries"] += 1
                logger.warning(f"Slow query detected: {self.operation} on {self.collection} took {query_time_ms:.2f}ms")

                # Add to query history for diagnostics
                query_info = {
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "collection": self.collection,
                    "operation": self.operation,
                    "duration_ms": query_time_ms,
                    "query": str(self.query)[:200] if self.query else None  # Limit query size
                }

                # Add to history with size limit
                query_history.append(query_info)
                if len(query_history) > MAX_QUERY_HISTORY:
                    query_history.pop(0)  # Remove oldest entry

            # If there was an error, track it
            if exc_type:
                db_metrics["query_errors"] += 1
                logger.error(f"Query error in {self.operation} on {self.collection}: {exc_val}")

    return QueryTracker(collection_name, operation, query)

async def get_db_metrics():
    """
    Get current database metrics and status

    Returns:
        dict: Database metrics and status information
    """
    metrics = db_metrics.copy()

    # Add current time
    metrics["current_time"] = datetime.now(timezone.utc).isoformat()

    # Add connection status
    try:
        if client is not None and db is not None:
            # Get server status if possible
            try:
                server_status = await client.admin.command('serverStatus')
                metrics["server_status"] = {
                    "version": server_status.get("version", "unknown"),
                    "uptime_seconds": server_status.get("uptime", 0),
                    "connections": server_status.get("connections", {}).get("current", 0),
                    "active_connections": server_status.get("connections", {}).get("active", 0),
                    "available_connections": server_status.get("connections", {}).get("available", 0)
                }
            except Exception as e:
                metrics["server_status_error"] = str(e)

            # Get connection pool stats if possible
            try:
                # Use a safer approach to get connection pool stats
                if hasattr(client, '_topology') and hasattr(client._topology, '_servers'):
                    servers_dict = client._topology._servers
                    if isinstance(servers_dict, dict):
                        metrics["connection_pool_in_use"] = len(servers_dict)
                    else:
                        metrics["connection_pool_error"] = "Servers object is not a dictionary"
            except Exception as e:
                metrics["connection_pool_error"] = str(e)

            metrics["connected"] = True
        else:
            metrics["connected"] = False
    except Exception as e:
        metrics["connected"] = False
        metrics["connection_check_error"] = str(e)

    # Add recent slow queries
    metrics["recent_slow_queries"] = query_history[-10:] if query_history else []

    return metrics

async def create_indexes():
    """
    Create optimized indexes for collections to support 500 users

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        if db is None:
            logger.error("Cannot create indexes: database connection not established")
            return False

        # Create indexes for users collection
        await db.users.create_index("email", unique=True)
        await db.users.create_index("last_login")  # For user activity reports
        await db.users.create_index([("email_verified", 1), ("active", 1)])  # For filtering active verified users
        await db.users.create_index("account_locked_until")  # For account lockout checks

        # Create compound index for OTP verification
        await db.users.create_index([
            ("email", 1),
            ("verification_otp", 1),
            ("verification_otp_expires", 1)
        ])

        # Create indexes for complaints collection
        await db.complaints.create_index("user_id")
        await db.complaints.create_index("created_at")
        await db.complaints.create_index([("user_id", 1), ("created_at", -1)])  # For user's complaints by date
        await db.complaints.create_index([("status", 1), ("user_id", 1)])  # For filtering by status

        # Create indexes for templates
        await db.user_templates.create_index("user_id", unique=True)  # Each user has one template

        # Blacklisted tokens functionality removed for simplicity
        # await db.blacklisted_tokens.create_index("jti", unique=True)
        # await db.blacklisted_tokens.create_index("expires_at", expireAfterSeconds=0)  # TTL index

        # Create indexes for refresh tokens with TTL
        await db.user_refresh_tokens.create_index("jti", unique=True)
        await db.user_refresh_tokens.create_index("user_id")
        await db.user_refresh_tokens.create_index("expires_at", expireAfterSeconds=0)  # TTL index
        await db.user_refresh_tokens.create_index([("is_revoked", 1), ("user_id", 1)])
        await db.user_refresh_tokens.create_index([("is_revoked", 1), ("revoked_at", 1)])

        # Create indexes for rate limiting (if using MongoDB for rate limiting)
        if hasattr(db, 'rate_limits'):
            await db.rate_limits.create_index([("key", 1), ("timestamp", 1)])
            await db.rate_limits.create_index("expires_at", expireAfterSeconds=0)  # TTL index

        # Create indexes for operation protection
        await db.operation_attempts.create_index([("user_id", 1), ("operation_type", 1)])  # Non-unique index
        await db.operation_attempts.create_index([("user_id", 1), ("operation_type", 1), ("timestamp", -1)])

        # Handle the TTL index for operation_attempts with special care to avoid conflicts
        try:
            # First, check if the index exists
            indexes = await db.operation_attempts.list_indexes().to_list(length=100)
            timestamp_index_exists = False
            timestamp_index_has_ttl = False

            for index in indexes:
                if index.get("name") == "timestamp_1":
                    timestamp_index_exists = True
                    if "expireAfterSeconds" in index:
                        timestamp_index_has_ttl = True
                        break

            # If index exists but doesn't have TTL, drop it first
            if timestamp_index_exists and not timestamp_index_has_ttl:
                logger.info("Dropping existing timestamp index without TTL to recreate with TTL")
                await db.operation_attempts.drop_index("timestamp_1")

            # If index doesn't exist or was dropped, create it with TTL
            if not timestamp_index_exists or (timestamp_index_exists and not timestamp_index_has_ttl):
                # Add TTL index to automatically remove old operation attempts after 7 days
                await db.operation_attempts.create_index("timestamp", expireAfterSeconds=7*24*60*60)
                logger.info("Created timestamp TTL index for operation_attempts")
            else:
                # Index already exists with TTL, no need to recreate
                logger.info("timestamp TTL index already exists for operation_attempts")
        except Exception as e:
            logger.error(f"Error handling timestamp TTL index: {str(e)}")

        # Create indexes for operation cooldowns
        await db.operation_cooldowns.create_index([("user_id", 1), ("operation_type", 1)])  # Non-unique index

        # Handle the TTL index for operation_cooldowns with special care to avoid conflicts
        try:
            # First, check if the index exists
            indexes = await db.operation_cooldowns.list_indexes().to_list(length=100)
            cooldown_index_exists = False
            cooldown_index_has_ttl = False

            for index in indexes:
                if index.get("name") == "cooldown_until_1":
                    cooldown_index_exists = True
                    if "expireAfterSeconds" in index:
                        cooldown_index_has_ttl = True
                        break

            # If index exists but doesn't have TTL, drop it first
            if cooldown_index_exists and not cooldown_index_has_ttl:
                logger.info("Dropping existing cooldown_until index without TTL to recreate with TTL")
                await db.operation_cooldowns.drop_index("cooldown_until_1")

            # If index doesn't exist or was dropped, create it with TTL
            if not cooldown_index_exists or (cooldown_index_exists and not cooldown_index_has_ttl):
                # Add TTL index to automatically remove expired cooldowns
                await db.operation_cooldowns.create_index("cooldown_until", expireAfterSeconds=0)
                logger.info("Created cooldown_until TTL index for operation_cooldowns")
            else:
                # Index already exists with TTL, no need to recreate
                logger.info("cooldown_until TTL index already exists for operation_cooldowns")
        except Exception as e:
            logger.error(f"Error handling cooldown_until TTL index: {str(e)}")

        logger.info("Database indexes created successfully for production use with 500 users")
        return True
    except Exception as e:
        logger.error(f"Failed to create database indexes: {str(e)}")
        return False
