import os
import smtplib
import secrets  # Using secrets instead of random for better security
import asyncio
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON>ultipart
from dotenv import load_dotenv
import logging
from datetime import datetime, timedelta, timezone
from concurrent.futures import ThreadPoolExecutor

load_dotenv()

logger = logging.getLogger(__name__)

# Thread pool for sending emails asynchronously
email_thread_pool = ThreadPoolExecutor(max_workers=4)

# Hostinger SMTP Configuration
HOSTINGER_SMTP_HOST = os.getenv("HOSTINGER_SMTP_HOST", "smtp.hostinger.com")
HOSTINGER_SMTP_PORT = int(os.getenv("HOSTINGER_SMTP_PORT", "465"))
HOSTINGER_SMTP_USERNAME = os.getenv("HOSTINGER_SMTP_USERNAME")
HOSTINGER_SMTP_PASSWORD = os.getenv("HOSTINGER_SMTP_PASSWORD")
HOSTINGER_SMTP_USE_SSL = os.getenv("HOSTINGER_SMTP_USE_SSL", "True").lower() == "true"

# Email Configuration
EMAIL_FROM = os.getenv("SENDER_EMAIL", "<EMAIL>")
EMAIL_FROM_NAME = os.getenv("SENDER_NAME", "Cyber Sakha")
FRONTEND_URL = os.getenv("FRONTEND_URL", "http://localhost:3001")


def _send_email_sync(to_email: str, subject: str, html_content: str):
    """Synchronous function to send emails using SMTP (runs in a thread)"""
    logger.info(f"Attempting to send email to {to_email} with subject: {subject}")

    if not HOSTINGER_SMTP_USERNAME or not HOSTINGER_SMTP_PASSWORD:
        logger.error("SMTP credentials not configured")
        return False

    # Create message
    msg = MIMEMultipart()
    msg['From'] = f"{EMAIL_FROM_NAME} <{EMAIL_FROM}>"
    msg['To'] = to_email
    msg['Subject'] = subject

    # Add HTML content
    msg.attach(MIMEText(html_content, 'html'))

    try:
        # Connect to SMTP server
        logger.info("Connecting to SMTP server...")
        if HOSTINGER_SMTP_USE_SSL:
            server = smtplib.SMTP_SSL(HOSTINGER_SMTP_HOST, HOSTINGER_SMTP_PORT)
        else:
            server = smtplib.SMTP(HOSTINGER_SMTP_HOST, HOSTINGER_SMTP_PORT)
            server.starttls()  # Upgrade to secure connection

        # Login to SMTP server
        server.login(HOSTINGER_SMTP_USERNAME, HOSTINGER_SMTP_PASSWORD)

        # Send email
        server.send_message(msg)
        server.quit()

        logger.info(f"Email sent to {to_email} using SMTP")
        return True
    except Exception as e:
        logger.error(f"Error sending email via SMTP: {str(e)}")
        # Print more details about the exception
        import traceback
        logger.error(f"Exception traceback: {traceback.format_exc()}")
        return False

async def _send_email(to_email: str, subject: str, html_content: str):
    """Asynchronous wrapper for sending emails using a thread pool"""
    logger.info(f"Queueing email to {to_email} with subject: {subject}")

    # Run the synchronous email sending function in a thread pool
    loop = asyncio.get_event_loop()
    result = await loop.run_in_executor(
        email_thread_pool,
        lambda: _send_email_sync(to_email, subject, html_content)
    )

    return result


def generate_otp(length=6):
    """
    Generate a random alphanumeric OTP of specified length

    Uses a mix of uppercase letters and digits for better security
    while maintaining readability (excludes similar looking characters)

    Enhanced for production with 500 users:
    - Uses a more secure random generator
    - Ensures a mix of letters and numbers for better security
    - Excludes confusing characters
    """
    # Use the imported secrets module for cryptographically strong random numbers

    # Exclude confusing characters like 0, O, 1, I, etc.
    digits = '23456789'
    letters = 'ABCDEFGHJKLMNPQRSTUVWXYZ'

    # Ensure at least 2 digits and 2 letters for better security
    # This makes the OTP harder to guess while keeping it readable
    num_digits = max(2, length // 3)  # At least 2 digits, or 1/3 of length
    num_letters = length - num_digits

    # Generate the components
    otp_digits = ''.join(secrets.choice(digits) for _ in range(num_digits))
    otp_letters = ''.join(secrets.choice(letters) for _ in range(num_letters))

    # Combine and shuffle
    combined = list(otp_digits + otp_letters)
    secrets.SystemRandom().shuffle(combined)

    return ''.join(combined)

def get_otp_expiry(minutes=15):
    """
    Get OTP expiry time (default: 15 minutes from now)

    Reduced from 30 minutes to 15 minutes for better security with 500 users

    Args:
        minutes (int): Number of minutes until OTP expires (default: 15)

    Returns:
        datetime: UTC timestamp when the OTP will expire
    """
    return datetime.now(timezone.utc) + timedelta(minutes=minutes)

async def send_verification_email(email: str, otp: str, first_name: str = "", is_login: bool = False):
    """
    Send email verification or login OTP using SMTP asynchronously

    Args:
        email (str): Recipient email address
        otp (str): One-time password for verification
        first_name (str, optional): Recipient's first name
        is_login (bool, optional): Whether this is for login verification (two-step)

    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    if is_login:
        logger.info(f"Preparing login verification email with OTP for {email}...")
        subject = "Login Verification Code - Cyber Sakha"
        title = "Login Verification"
        message = f"Hello {first_name}, we received a login request for your Cyber Sakha account. Please use the verification code below to complete your login:"
        expiry = "5 minutes"
        note = "If you didn't attempt to login, please change your password immediately."
    else:
        logger.info(f"Preparing email verification OTP for {email}...")
        subject = "Verify your Cyber Sakha account"
        title = f"Welcome {first_name} to Cyber Sakha!"
        message = "Thank you for registering with Cyber Sakha. Please verify your email address using the verification code below:"
        expiry = "15 minutes"
        note = "If you didn't create this account, please ignore this email."

    # Create HTML content
    html_content = f"""
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
                <div style="text-align: center; margin-bottom: 20px;">
                    <h1 style="color: #0891B2;">{title}</h1>
                </div>
                <p>{message}</p>
                <div style="padding: 15px; background-color: #f5f5f5; font-size: 24px; font-weight: bold; text-align: center; margin: 20px 0; letter-spacing: 5px; border-radius: 4px; border: 1px dashed #ccc;">
                    {otp}
                </div>
                <p><strong>Note:</strong> This code contains both letters and numbers. Please enter it exactly as shown above.</p>
                <p>This verification code will expire in {expiry}.</p>
                <hr style="border: none; border-top: 1px solid #e0e0e0; margin: 20px 0;">
                <p>{note}</p>
                <p>Best regards,<br>The Cyber Sakha Team</p>
                <div style="font-size: 12px; color: #888; margin-top: 20px; text-align: center;">
                    <p>This is an automated message, please do not reply to this email.</p>
                </div>
            </div>
        </body>
        </html>
    """

    result = await _send_email(
        to_email=email,
        subject=subject,
        html_content=html_content
    )

    logger.info(f"Verification email send result: {result}")
    return result


async def test_email_configuration(email: str):
    """Test the email configuration by sending a test email asynchronously"""
    logger.info(f"Testing email configuration by sending to {email}")

    html_content = f"""
        <html>
        <body>
            <h1>Email Configuration Test</h1>
            <p>This is a test email to verify that your Cyber Sakha email configuration is working correctly.</p>
            <p>If you received this email, your SMTP settings are configured properly.</p>
            <p>Best regards,<br>The Cyber Sakha Team</p>
        </body>
        </html>
    """

    result = await _send_email(
        to_email=email,
        subject="Cyber Sakha Email Configuration Test",
        html_content=html_content
    )

    logger.info(f"Test email send result: {result}")
    return result


async def send_password_reset_email(email: str, otp: str, first_name: str = ""):
    """Send password reset OTP using SMTP asynchronously"""
    logger.info(f"Preparing password reset email with OTP for {email}...")

    # Create HTML content
    html_content = f"""
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
                <div style="text-align: center; margin-bottom: 20px;">
                    <h1 style="color: #0891B2;">Password Reset Request</h1>
                </div>
                <p>Hello {first_name},</p>
                <p>We received a request to reset your Cyber Sakha account password.</p>
                <p>Please use the verification code below to reset your password:</p>
                <div style="padding: 15px; background-color: #f5f5f5; font-size: 24px; font-weight: bold; text-align: center; margin: 20px 0; letter-spacing: 5px; border-radius: 4px; border: 1px dashed #ccc;">
                    {otp}
                </div>
                <p><strong>Note:</strong> This code contains both letters and numbers. Please enter it exactly as shown above.</p>
                <p>This verification code will expire in 15 minutes.</p>
                <hr style="border: none; border-top: 1px solid #e0e0e0; margin: 20px 0;">
                <p>If you didn't request a password reset, please ignore this email or contact support if you have concerns.</p>
                <p>Best regards,<br>The Cyber Sakha Team</p>
                <div style="font-size: 12px; color: #888; margin-top: 20px; text-align: center;">
                    <p>This is an automated message, please do not reply to this email.</p>
                </div>
            </div>
        </body>
        </html>
    """

    result = await _send_email(
        to_email=email,
        subject="Reset Your Cyber Sakha Password",
        html_content=html_content
    )

    logger.info(f"Password reset email send result: {result}")
    return result
