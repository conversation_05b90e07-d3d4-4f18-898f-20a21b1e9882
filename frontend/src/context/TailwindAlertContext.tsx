import React, { createContext, useContext, useState, ReactNode } from 'react';
import Tailwind<PERSON>lert from '../components/TailwindAlert';

type AlertSeverity = 'success' | 'info' | 'warning' | 'error';

interface AlertContextType {
  showSuccess: (message: ReactNode, duration?: number) => void;
  showError: (message: ReactNode, duration?: number) => void;
  showInfo: (message: ReactNode, duration?: number) => void;
  showWarning: (message: ReactNode, duration?: number) => void;
}

interface AlertItem {
  id: number;
  message: ReactNode;
  severity: AlertSeverity;
  duration: number;
}

const TailwindAlertContext = createContext<AlertContextType | undefined>(undefined);

interface AlertProviderProps {
  children: ReactNode;
}

export const TailwindAlertProvider: React.FC<AlertProviderProps> = ({ children }) => {
  const [alerts, setAlerts] = useState<AlertItem[]>([]);

  // Show a new alert
  const showAlert = (message: ReactNode, severity: AlertSeverity, duration: number = 6000) => {
    // Generate a unique ID using UUID-like approach
    const timestamp = Date.now();
    const randomPart = Math.floor(Math.random() * 10000);
    const id = timestamp + randomPart; // Combine timestamp with random number for uniqueness

    // Ensure no duplicate IDs
    const newAlert = { id, message, severity, duration };

    setAlerts(prev => {
      // Check if this ID already exists (extremely unlikely but possible)
      const exists = prev.some(alert => alert.id === id);
      if (exists) {
        // If it exists, generate a different ID
        return [...prev, { ...newAlert, id: id + 1 }];
      }
      return [...prev, newAlert];
    });

    // Auto-remove alert after duration + 500ms (for animation)
    setTimeout(() => {
      setAlerts(prev => prev.filter(alert => alert.id !== id));
    }, duration + 500);
  };

  // Helper functions for different alert types
  const showSuccess = (message: ReactNode, duration?: number) => showAlert(message, 'success', duration);
  const showError = (message: ReactNode, duration?: number) => showAlert(message, 'error', duration);
  const showInfo = (message: ReactNode, duration?: number) => showAlert(message, 'info', duration);
  const showWarning = (message: ReactNode, duration?: number) => showAlert(message, 'warning', duration);

  // Handle alert close
  const handleClose = (id: number) => {
    setAlerts(prev => prev.filter(alert => alert.id !== id));
  };

  return (
    <TailwindAlertContext.Provider value={{ showSuccess, showError, showInfo, showWarning }}>
      {children}

      {/* Render alerts */}
      <div className="fixed top-0 right-0 z-50 p-4 space-y-4 pointer-events-none">
        {alerts.map((alert, index) => (
          <div key={alert.id} className="pointer-events-auto" style={{ zIndex: 9999 - index }}>
            <TailwindAlert
              open={true}
              message={alert.message}
              severity={alert.severity}
              duration={alert.duration}
              onClose={() => handleClose(alert.id)}
            />
          </div>
        ))}
      </div>
    </TailwindAlertContext.Provider>
  );
};

// Custom hook to use the alert context
export const useTailwindAlert = (): AlertContextType => {
  const context = useContext(TailwindAlertContext);
  if (context === undefined) {
    throw new Error('useTailwindAlert must be used within a TailwindAlertProvider');
  }
  return context;
};

// For backward compatibility
export const useAlert = useTailwindAlert;
