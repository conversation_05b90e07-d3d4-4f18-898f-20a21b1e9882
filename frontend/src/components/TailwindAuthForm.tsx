import React, { useState, useEffect } from 'react';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import { useAlert } from '../context/TailwindAlertContext';
import { useAuth } from '../context/AuthContext';
import authService from '../services/authService';
import OTPVerification from './OTPVerification';
import logger from '../utils/logger';

// Icons
import {
  FiMail,
  FiLock,
  FiUser,
  FiBriefcase,
  FiEye,
  FiEyeOff,
  FiArrowRight
} from 'react-icons/fi';

interface FormData {
  first_name: string;
  last_name: string;
  email: string;
  password: string;
  designation: string;
  isSignup: boolean;
  showPassword: boolean;
}

interface TailwindAuthFormProps {
  isRegister?: boolean;
}

function TailwindAuthForm({ isRegister = false }: TailwindAuthFormProps) {
  const navigate = useNavigate();
  const { showSuccess, showError } = useAlert();
  const { refreshUserData } = useAuth();

  const [formData, setFormData] = useState<FormData>({
    first_name: '',
    last_name: '',
    email: '',
    password: '',
    designation: '',
    isSignup: isRegister,
    showPassword: false,
  });

  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showOTPVerification, setShowOTPVerification] = useState(false);
  const [verificationEmail, setVerificationEmail] = useState('');
  const [requiresTwoStep, setRequiresTwoStep] = useState(false);

  // Reset form when switching between login and signup
  useEffect(() => {
    setFormData(prev => ({
      ...prev,
      isSignup: isRegister,
    }));
    setErrors({});
  }, [isRegister]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Email validation
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 8) {
      newErrors.password = 'Password must be at least 8 characters';
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(formData.password)) {
      newErrors.password = 'Password must contain at least one uppercase letter, one lowercase letter, and one number';
    }

    // Additional validations for signup
    if (formData.isSignup) {
      if (!formData.first_name) {
        newErrors.first_name = 'First name is required';
      }

      if (!formData.last_name) {
        newErrors.last_name = 'Last name is required';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));

    // Clear error when user types
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: '',
      }));
    }
  };

  const togglePasswordVisibility = () => {
    setFormData(prev => ({
      ...prev,
      showPassword: !prev.showPassword,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      if (!validateForm()) {
        setLoading(false);
        return;
      }

      if (formData.isSignup) {
        // Handle registration
        const response = await authService.register({
          email: formData.email,
          password: formData.password,
          first_name: formData.first_name,
          last_name: formData.last_name,
          designation: formData.designation
        });

        if (response.requires_verification) {
          setVerificationEmail(formData.email);
          setShowOTPVerification(true);
          showSuccess('Registration successful! Please verify your email.');
        }
      } else {
        // Handle login
        logger.debug('Attempting login with:', { email: formData.email });
        const response = await authService.login(formData.email, formData.password);
        logger.debug('Login response:', response);
        
        if (response.requires_verification) {
          // Two-step verification required
          setRequiresTwoStep(true);
          setVerificationEmail(formData.email);
          setShowOTPVerification(true);
          showSuccess('Please enter the verification code sent to your email.');
        } else if (response.access_token && response.user) {
          // Regular login successful
          await refreshUserData();
          showSuccess('Login successful!');
          navigate('/dashboard');
        } else {
          logger.error('Unexpected login response:', response);
          throw new Error('Invalid login response');
        }
      }
    } catch (err: any) {
      logger.error('Auth error:', err);
      const errorMessage = err.response?.data?.detail || err.message || 'An error occurred';
      showError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Handle OTP verification completion
  const handleVerificationComplete = async (otp: string) => {
    try {
      setLoading(true);
      if (requiresTwoStep) {
        // Handle two-step verification
        const response = await authService.verifyTwoStep(verificationEmail, otp);
        if (response.access_token) {
          await refreshUserData();
          showSuccess('Login successful!');
          navigate('/dashboard');
        }
      } else {
        // Handle email verification
        const response = await authService.verifyOTP(verificationEmail, otp);
        if (response.success) {
          showSuccess('Email verified successfully! Please login.');
          navigate('/login');
        }
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.detail || 'Verification failed';
      showError(errorMessage);
    } finally {
      setLoading(false);
      setShowOTPVerification(false);
    }
  };

  // Handle OTP verification cancellation
  const handleVerificationCancel = () => {
    setShowOTPVerification(false);
    if (requiresTwoStep) {
      setRequiresTwoStep(false);
    } else {
      navigate('/login');
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center px-4 py-8 sm:py-12 overflow-hidden">
      {/* Theme selector will be handled in the main layout */}

      {/* Clean background without patterns */}
      <div className="absolute inset-0 z-0 opacity-10">
        {/* No background pattern */}
      </div>

      <div className="w-full max-w-sm mx-auto relative z-10">
        {showOTPVerification ? (
          <OTPVerification
            email={verificationEmail}
            onVerified={handleVerificationComplete}
            onCancel={handleVerificationCancel}
          />
        ) : (
          <div className="bg-white/80 dark:bg-[rgba(16,16,30,0.6)] backdrop-blur-md rounded-xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl dark:shadow-glow-blue-sm dark:border dark:border-[rgba(0,170,255,0.3)] animate-fade-in">
          {/* Header */}
          <div className="p-6 relative overflow-hidden" style={{ backgroundColor: 'var(--theme-accent)' }}>
            {/* Clean header without effects */}

            <div className="flex justify-center mb-4 relative z-10">
              <div className="w-14 h-14 rounded-full flex items-center justify-center shadow-md border" style={{
                backgroundColor: 'var(--theme-bg-card)',
                borderColor: 'var(--theme-border)',
                boxShadow: '0 0 10px var(--theme-glow)'
              }}>
                <span className="text-xl font-bold" style={{ color: 'var(--theme-text)' }}>CS</span>
              </div>
            </div>
            <h1 className="text-xl font-bold text-center text-white relative z-10 drop-shadow-md">
              {formData.isSignup ? 'Create Account' : 'Welcome Back'}
            </h1>
            <p className="text-center text-primary-100 text-sm mt-1 relative z-10">
              {formData.isSignup
                ? 'Sign up to get started with Cyber Sakha'
                : 'Sign in to continue to Cyber Sakha'}
            </p>
          </div>

          {/* Form */}
          <div className="p-5">
            <form onSubmit={handleSubmit} className="space-y-3">
              {formData.isSignup && (
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  {/* First Name */}
                  <div>
                    <label htmlFor="first_name" className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                      First Name
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <FiUser className="h-4 w-4 text-primary-500 dark:text-primary-400" />
                      </div>
                      <input
                        id="first_name"
                        name="first_name"
                        type="text"
                        value={formData.first_name}
                        onChange={handleChange}
                        className={`block w-full pl-10 pr-3 py-2 text-sm rounded-lg border ${
                          errors.first_name
                            ? 'border-error-500 focus:ring-error-500 focus:border-error-500 dark:border-error-400 dark:focus:ring-error-400 dark:focus:border-error-400'
                            : 'border-gray-300 dark:border-[rgba(0,170,255,0.3)] focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-400 dark:focus:border-primary-400'
                        } bg-white/90 dark:bg-[rgba(16,16,30,0.6)] text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-500 shadow-sm focus:outline-none focus:ring-1 backdrop-blur-sm transition-all duration-200`}
                        placeholder="John"
                      />
                    </div>
                    {errors.first_name && (
                      <p className="mt-1 text-xs text-red-600 dark:text-red-400">{errors.first_name}</p>
                    )}
                  </div>

                  {/* Last Name */}
                  <div>
                    <label htmlFor="last_name" className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Last Name
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <FiUser className="h-4 w-4 text-primary-500 dark:text-primary-400" />
                      </div>
                      <input
                        id="last_name"
                        name="last_name"
                        type="text"
                        value={formData.last_name}
                        onChange={handleChange}
                        className={`block w-full pl-10 pr-3 py-2 text-sm rounded-lg border ${
                          errors.last_name
                            ? 'border-error-500 focus:ring-error-500 focus:border-error-500 dark:border-error-400 dark:focus:ring-error-400 dark:focus:border-error-400'
                            : 'border-gray-300 dark:border-[rgba(0,170,255,0.3)] focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-400 dark:focus:border-primary-400'
                        } bg-white/90 dark:bg-[rgba(16,16,30,0.6)] text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-500 shadow-sm focus:outline-none focus:ring-1 backdrop-blur-sm transition-all duration-200`}
                        placeholder="Doe"
                      />
                    </div>
                    {errors.last_name && (
                      <p className="mt-1 text-xs text-red-600 dark:text-red-400">{errors.last_name}</p>
                    )}
                  </div>
                </div>
              )}

              {/* Email */}
              <div>
                <label htmlFor="email" className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Email Address
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FiMail className="h-4 w-4 text-primary-500 dark:text-primary-400" />
                  </div>
                  <input
                    id="email"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleChange}
                    className={`block w-full pl-10 pr-3 py-2 text-sm rounded-lg border ${
                      errors.email
                        ? 'border-error-500 focus:ring-error-500 focus:border-error-500 dark:border-error-400 dark:focus:ring-error-400 dark:focus:border-error-400'
                        : 'border-gray-300 dark:border-[rgba(0,170,255,0.3)] focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-400 dark:focus:border-primary-400'
                    } bg-white/90 dark:bg-[rgba(16,16,30,0.6)] text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-500 shadow-sm focus:outline-none focus:ring-1 backdrop-blur-sm transition-all duration-200`}
                    placeholder="<EMAIL>"
                  />
                </div>
                {errors.email && (
                  <p className="mt-1 text-xs text-red-600 dark:text-red-400">{errors.email}</p>
                )}
              </div>

              {/* Password */}
              <div>
                <label htmlFor="password" className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Password
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FiLock className="h-4 w-4 text-primary-500 dark:text-primary-400" />
                  </div>
                  <input
                    id="password"
                    name="password"
                    type={formData.showPassword ? 'text' : 'password'}
                    value={formData.password}
                    onChange={handleChange}
                    className={`block w-full pl-10 pr-10 py-2 text-sm rounded-lg border ${
                      errors.password
                        ? 'border-error-500 focus:ring-error-500 focus:border-error-500 dark:border-error-400 dark:focus:ring-error-400 dark:focus:border-error-400'
                        : 'border-gray-300 dark:border-[rgba(0,170,255,0.3)] focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-400 dark:focus:border-primary-400'
                    } bg-white/90 dark:bg-[rgba(16,16,30,0.6)] text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-500 shadow-sm focus:outline-none focus:ring-1 backdrop-blur-sm transition-all duration-200`}
                    placeholder="••••••••"
                  />
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                    <button
                      type="button"
                      onClick={togglePasswordVisibility}
                      className="text-gray-700 hover:text-gray-900 focus:outline-none bg-transparent p-1 rounded-full hover:bg-gray-100/50 dark:hover:bg-gray-700/50 transition-colors duration-200"
                    >
                      {formData.showPassword ? (
                        <FiEyeOff className="h-3 w-3" />
                      ) : (
                        <FiEye className="h-3 w-3" />
                      )}
                    </button>
                  </div>
                </div>
                {errors.password && (
                  <p className="mt-1 text-xs text-red-600 dark:text-red-400">{errors.password}</p>
                )}
              </div>

              {/* Designation (only for signup) */}
              {formData.isSignup && (
                <div>
                  <label htmlFor="designation" className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Designation (Optional)
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <FiBriefcase className="h-4 w-4 text-primary-500 dark:text-primary-400" />
                    </div>
                    <input
                      id="designation"
                      name="designation"
                      type="text"
                      value={formData.designation}
                      onChange={handleChange}
                      className="block w-full pl-10 pr-3 py-2 text-sm rounded-lg border border-gray-300 dark:border-[rgba(0,170,255,0.3)] focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-400 dark:focus:border-primary-400 bg-white/90 dark:bg-[rgba(16,16,30,0.6)] text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-500 shadow-sm focus:outline-none focus:ring-1 backdrop-blur-sm transition-all duration-200"
                      placeholder="Manager"
                    />
                  </div>
                </div>
              )}

              {/* Submit Button */}
              <div className="pt-2">
                <button
                  type="submit"
                  disabled={loading}
                  className="w-full flex justify-center items-center py-2.5 px-4 rounded-lg shadow-md text-white focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-300 disabled:opacity-70 disabled:cursor-not-allowed relative overflow-hidden hover:-translate-y-0.5 transform"
                  style={{
                    backgroundColor: 'var(--theme-button)',
                    boxShadow: '0 0 10px var(--theme-glow)'
                  }}
                >
                  {loading ? (
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  ) : (
                    <span className="flex items-center">
                      {formData.isSignup ? 'Sign Up' : 'Sign In'}
                      <FiArrowRight className="ml-2 h-4 w-4" />
                    </span>
                  )}
                </button>
              </div>
            </form>

            {/* Forgot Password (only for login) */}
            {!formData.isSignup && (
              <div className="mt-3 text-center">
                <RouterLink
                  to="/forgot-password"
                  className="text-xs text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 transition-colors duration-200"
                >
                  Forgot your password?
                </RouterLink>
              </div>
            )}

            {/* Divider */}
            <div className="mt-5 relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-200 dark:border-[rgba(0,170,255,0.2)]"></div>
              </div>
              <div className="relative flex justify-center text-xs">
                <span className="px-2 bg-white/90 dark:bg-[rgba(16,16,30,0.7)] text-gray-500 dark:text-gray-400 backdrop-blur-sm">
                  {formData.isSignup ? 'Already have an account?' : 'Don\'t have an account?'}
                </span>
              </div>
            </div>

            {/* Toggle between login and signup */}
            <div className="mt-5 text-center">
              <RouterLink
                to={formData.isSignup ? '/login' : '/register'}
                className="text-sm text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 font-medium transition-colors duration-200"
              >
                {formData.isSignup ? 'Sign In' : 'Create an Account'}
              </RouterLink>
            </div>
          </div>
        </div>
        )}

        {/* Footer */}
        <div className="mt-6 text-center">
          <p className="text-xs text-gray-500 dark:text-gray-500">
            &copy; {new Date().getFullYear()} <span className="text-primary-600 dark:text-primary-400">Cyber Sakha</span>. All rights reserved.
          </p>
        </div>
      </div>
    </div>
  );
}

export default TailwindAuthForm;
