import logging
from typing import Dict, Any, List, Optional
from motor.motor_asyncio import AsyncIOMotorClient
from fastapi import Depends, HTTPException
from database import get_db
from bson import ObjectId
import math

# Configure logging
logger = logging.getLogger(__name__)

# ATM search functionality has been removed as per requirements

async def get_nodal_officers(
    search_query: Optional[str] = None,
    organization: Optional[str] = None,
    organization_type: Optional[str] = None,
    state: Optional[str] = None,
    page: int = 1,
    limit: int = 10,
    db: AsyncIOMotorClient = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get Nodal Officer data with optional search filters

    Args:
        search_query: General search query to match against multiple fields
        organization: Filter by organization name
        organization_type: Filter by organization type (Bank/Wallet/E-commerce)
        state: Filter by state
        page: Page number for pagination
        limit: Number of items per page
        db: Database connection

    Returns:
        Dictionary with Nodal Officer data and pagination info
    """
    try:
        # Build the query
        query = {}

        # Add search filters if provided
        if search_query:
            # Search across multiple fields
            query["$or"] = [
                {"name": {"$regex": search_query, "$options": "i"}},
                {"organization": {"$regex": search_query, "$options": "i"}},
                {"email": {"$regex": search_query, "$options": "i"}},
                {"phone": {"$regex": search_query, "$options": "i"}},
                {"address": {"$regex": search_query, "$options": "i"}}
            ]

        # Add specific filters
        if organization:
            query["organization"] = {"$regex": organization, "$options": "i"}

        if organization_type:
            query["organization_type"] = {"$regex": organization_type, "$options": "i"}

        if state:
            query["state"] = {"$regex": state, "$options": "i"}

        # Count total documents for pagination
        total_items = await db.nodal_officers.count_documents(query)

        # Calculate pagination values
        total_pages = math.ceil(total_items / limit)
        skip = (page - 1) * limit

        # Get the data with pagination
        cursor = db.nodal_officers.find(query).skip(skip).limit(limit)

        # Convert to list and process ObjectId
        officers = []
        async for officer in cursor:
            # Convert ObjectId to string
            officer["_id"] = str(officer["_id"])
            officers.append(officer)

        return {
            "items": officers,
            "total": total_items,
            "page": page,
            "limit": limit,
            "total_pages": total_pages
        }

    except Exception as e:
        logger.error(f"Error retrieving Nodal Officer data: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve Nodal Officer data")
