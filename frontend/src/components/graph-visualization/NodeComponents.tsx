import React, { useState } from 'react';
import { NodeProps, Handle, Position, useReactFlow } from 'reactflow';
import { useThemeContext } from '../../context/TailwindThemeContext';
import { ExpandCollapseButton } from './NodeExpandCollapseManager';

/**
 * Transaction Node Component
 * Displays transaction information with expand/collapse functionality
 */
export const TransactionNode: React.FC<NodeProps> = ({ data, id }) => {
  // Get the theme context from the current context
  const themeContext = useThemeContext();

  // Use the theme context from the node data if available, otherwise use the current context
  // This ensures the node retains its styling during layout recalculation
  const isDark = data?.themeContext?.isDark !== undefined ? data.themeContext.isDark : themeContext.isDark;

  const theme = {
    primaryColor: isDark ? '#6366f1' : '#3b82f6'
  };
  const [isExpanded, setIsExpanded] = useState(true);
  const [isMultipleTransactionsExpanded, setIsMultipleTransactionsExpanded] = useState(true);
  const [isSubTransactionsExpanded, setIsSubTransactionsExpanded] = useState(true);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const { setNodes } = useReactFlow();

  // We don't need to use isNodeExpanded here since we're using the ExpandCollapseButton component

  // Determine node type - sender, main, sub, or special
  const isSenderNode = data.isSenderNode === true;
  const isMainTransaction = data.isMainTransaction === true;
  const isSubTransaction = data.isSubTransaction === true;
  const isSpecialTxn = data.isSpecial === true;

  // Simple styling based on transaction type
  const getNodeStyle = () => {
    // Sender node - blue
    if (isSenderNode) {
      return {
        bg: isDark ? 'bg-blue-900/30' : 'bg-blue-50',
        border: isDark ? 'border-blue-700' : 'border-blue-300',
        text: isDark ? 'text-blue-400' : 'text-blue-700'
      };
    }

    // Special transaction - red
    if (isSpecialTxn) {
      return {
        bg: isDark ? 'bg-red-900/30' : 'bg-red-50',
        border: isDark ? 'border-red-700' : 'border-red-300',
        text: isDark ? 'text-red-400' : 'text-red-700'
      };
    }

    // Sub-transaction - purple
    if (isSubTransaction) {
      return {
        bg: isDark ? 'bg-purple-900/30' : 'bg-purple-50',
        border: isDark ? 'border-purple-700' : 'border-purple-300',
        text: isDark ? 'text-purple-400' : 'text-purple-700'
      };
    }

    // Main transaction - green
    if (isMainTransaction || data.type === 'Money Transfer to') {
      return {
        bg: isDark ? 'bg-green-900/30' : 'bg-green-50',
        border: isDark ? 'border-green-700' : 'border-green-300',
        text: isDark ? 'text-green-400' : 'text-green-700'
      };
    }

    // Default - amber
    return {
      bg: isDark ? 'bg-amber-900/30' : 'bg-amber-50',
      border: isDark ? 'border-amber-700' : 'border-amber-300',
      text: isDark ? 'text-amber-400' : 'text-amber-700'
    };
  };

  const nodeStyle = getNodeStyle();

  // Function to delete this node
  const handleDeleteNode = () => {
    setNodes((nodes) => nodes.filter((node) => node.id !== id));
    setShowDeleteConfirm(false);
  };

  // Function to toggle expanded/collapsed state
  const toggleExpanded = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsExpanded(!isExpanded);
  };

  return (
    <div
      className={`p-3 rounded-lg border shadow-md w-[300px] ${nodeStyle.bg} ${nodeStyle.border} transition-all duration-200`}
      style={{ cursor: 'move' }}
    >
      {/* Add source and target handles with positions based on layout direction */}
      <Handle
        type="target"
        position={data.layoutDirection === 'horizontal' ? Position.Left : Position.Top}
        style={{ background: theme.primaryColor, width: '8px', height: '8px' }}
      />
      <Handle
        type="source"
        position={data.layoutDirection === 'horizontal' ? Position.Right : Position.Bottom}
        style={{ background: theme.primaryColor, width: '8px', height: '8px' }}
      />

      {/* Controls positioned at top right */}
      <div className="absolute top-1 right-1 z-10 flex gap-1">
        {/* Expand/Collapse button - only show for nodes with children */}
        {data.totalDescendants > 0 && (
          <ExpandCollapseButton
            nodeId={id}
            hasChildren={true}
            className={`p-1 rounded-full bg-${nodeStyle.text.split('-')[1]}-100 hover:bg-${nodeStyle.text.split('-')[1]}-200 ${nodeStyle.text} shadow-sm`}
          />
        )}

        {/* Delete button */}
        <button
          onClick={() => setShowDeleteConfirm(true)}
          className={`p-0.5 rounded hover:bg-red-100 text-red-600`}
          title="Delete node"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      {/* Header with controls */}
      <div className="flex justify-between items-center mb-2">
        <div
          className={`font-medium text-lg pb-1 border-b border-gray-200 ${nodeStyle.text} w-full cursor-pointer`}
          onClick={toggleExpanded}
          title={isExpanded ? "Click to collapse" : "Click to expand"}
        >
          {data.label}
          {!isExpanded && (
            <div className="mt-1 text-xs">
              {data.hasMultipleTransactions && data.multipleTransactions && (
                <span className={`mr-2 text-blue-700 bg-blue-50 px-1 py-0.5 rounded-sm`}>
                  💸 {data.multipleTransactions.length} money transfers
                </span>
              )}
              {data.hasSubTransactions && data.subTransactions && (
                <span className={`text-purple-700 bg-purple-50 px-1 py-0.5 rounded-sm`}>
                  🔄 {data.subTransactions.length} sub transactions
                </span>
              )}
            </div>
          )}
          <span className="ml-2 text-xs">
            {isExpanded ?
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
              :
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            }
          </span>
        </div>
      </div>

      {/* Delete confirmation dialog */}
      {showDeleteConfirm && (
        <div className={`absolute top-0 left-0 w-full h-full ${nodeStyle.bg} rounded-lg border ${nodeStyle.border} z-10 flex flex-col justify-center items-center p-2`}>
          <p className={`text-center mb-2 text-gray-800`}>Delete this node?</p>
          <div className="flex gap-2">
            <button
              onClick={handleDeleteNode}
              className={`px-2 py-1 rounded bg-red-500 text-white`}
            >
              Yes
            </button>
            <button
              onClick={() => setShowDeleteConfirm(false)}
              className={`px-2 py-1 rounded bg-gray-200 text-gray-800`}
            >
              No
            </button>
          </div>
        </div>
      )}

      {/* Always show essential information */}
      <div className="flex flex-col gap-1 text-base">
        <div className="grid grid-cols-2 gap-x-1 gap-y-1">
          {/* Account Details - Always visible */}
          <div className="flex justify-between col-span-2">
            <span className={`${isDark ? 'text-gray-300' : 'text-gray-600'} font-semibold`}>Account:</span>
            <span className={`${isDark ? 'text-white' : 'text-gray-900'} font-medium break-all`} title={data.account}>
              {data.account}
            </span>
          </div>

          {/* Amount - Only show if not a node with multiple transactions */}
          {data.amount && !data.hasMultipleTransactions && (
            <div className="flex justify-between col-span-2">
              <span className={`${isDark ? 'text-gray-300' : 'text-gray-600'} font-semibold`}>Amount:</span>
              <span className={`font-semibold ${isDark ? 'text-white' : 'text-gray-900'}`}>₹{data.amount}</span>
            </div>
          )}

          {/* Transaction Type - Only show if not "Money Transfer to" */}
          {data.type && data.type !== 'Money Transfer to' && (
            <div className="flex justify-between col-span-2">
              <span className={`${isDark ? 'text-gray-300' : 'text-gray-600'} font-semibold`}>Type:</span>
              <span className={`${isDark ? 'text-white' : 'text-gray-900'} font-medium`}>{data.type}</span>
            </div>
          )}

          {/* Layer - Always visible */}
          <div className="flex justify-between col-span-2">
            <span className={`${isDark ? 'text-gray-300' : 'text-gray-600'} font-semibold`}>Layer:</span>
            <span className={`${isDark ? 'text-white' : 'text-gray-900'} font-medium`}>
              {data.layer}
              {data.totalDescendants > 0 && (
                <span className={`ml-2 text-xs ${nodeStyle.text} ${isDark ? 'bg-gray-800' : `bg-${nodeStyle.text.split('-')[1]}-50`} px-1 py-0.5 rounded-sm`}>
                  Has {data.totalDescendants} child node{data.totalDescendants !== 1 ? 's' : ''}
                </span>
              )}
            </span>
          </div>

          {/* Additional details - only show if expanded and not a node with multiple transactions */}
          {isExpanded && !data.hasMultipleTransactions && (
            <>
              {data.date && (
                <div className="flex justify-between col-span-2">
                  <span className={`${isDark ? 'text-gray-300' : 'text-gray-600'} font-semibold`}>Date:</span>
                  <span className={`${isDark ? 'text-white' : 'text-gray-900'} font-medium`}>{data.date}</span>
                </div>
              )}

              {/* Transaction ID */}
              {data.txn_id && (
                <div className="flex justify-between col-span-2">
                  <span className={`${isDark ? 'text-gray-300' : 'text-gray-600'} font-semibold`}>Txn ID:</span>
                  <span className={`${isDark ? 'text-white' : 'text-gray-900'} font-medium break-all`} title={data.txn_id}>
                    {data.txn_id}
                  </span>
                </div>
              )}

              {/* Receiver Transaction ID - labeled as Txn ID */}
              {data.receiver_transaction_id && (
                <div className="flex justify-between col-span-2">
                  <span className={`${isDark ? 'text-gray-300' : 'text-gray-600'} font-semibold`}>Txn ID:</span>
                  <span className={`${isDark ? 'text-white' : 'text-gray-900'} font-medium break-all`} title={data.receiver_transaction_id}>
                    {data.receiver_transaction_id}
                  </span>
                </div>
              )}

              {/* Sender Details - only show for receiver nodes */}
              {!data.isSenderNode && data.sender_account && (
                <div className="flex justify-between col-span-2">
                  <span className={`${isDark ? 'text-gray-300' : 'text-gray-600'} font-semibold`}>From:</span>
                  <span className={`${isDark ? 'text-white' : 'text-gray-900'} font-medium break-all`} title={data.sender_account}>
                    {data.sender_account}
                  </span>
                </div>
              )}

              {/* Reference field (receiver_info) with word wrap */}
              {data.receiver_info && (
                <div className="flex flex-col col-span-2 mt-1">
                  <span className={`${isDark ? 'text-gray-300' : 'text-gray-600'} font-semibold`}>Reference:</span>
                  <div
                    className={`${isDark ? 'text-white bg-gray-800' : 'text-gray-900 bg-gray-100'} font-medium break-words whitespace-normal mt-0.5 p-1.5 rounded text-sm max-h-[60px] overflow-y-auto`}
                  >
                    {data.receiver_info}
                  </div>
                </div>
              )}
            </>
          )}
        </div>

        {/* Multiple Transactions section - Only for "Money Transfer to" type transactions */}
        {data.hasMultipleTransactions && data.multipleTransactions && data.multipleTransactions.length > 0 && (
          <div className="col-span-2 mt-2 border-t border-gray-200 pt-2">
            {/* Calculate total amount from all transactions */}
            {(() => {
              let totalAmount = 0;
              data.multipleTransactions.forEach((txn: any) => {
                if (txn.amount) {
                  // Convert amount to number if it's a string
                  const amount = typeof txn.amount === 'string'
                    ? parseFloat(txn.amount.replace(/[^\d.-]/g, ''))
                    : Number(txn.amount);

                  if (!isNaN(amount)) {
                    totalAmount += amount;
                  }
                }
              });

              return totalAmount > 0 ? (
                <div className={`flex justify-between col-span-2 mb-2 p-1.5 ${isDark ? 'bg-blue-900/30 rounded' : 'bg-blue-100 rounded'}`}>
                  <span className={`${isDark ? 'text-gray-300' : 'text-gray-700'} font-semibold`}>Total Amount:</span>
                  <span className={`font-bold ${isDark ? 'text-blue-400' : 'text-blue-800'}`}>₹{totalAmount.toLocaleString()}</span>
                </div>
              ) : null;
            })()}

            <div
              className={`font-semibold text-base mb-1 ${isDark ? 'text-blue-400' : nodeStyle.text} flex items-center cursor-pointer`}
              onClick={() => setIsMultipleTransactionsExpanded(!isMultipleTransactionsExpanded)}
            >
              <span className="flex-grow">
                <span className="mr-1">💸</span>
                Money Transfer Transactions ({data.multipleTransactions.length})
              </span>
              <span className="ml-2 text-xs">
                {isMultipleTransactionsExpanded ?
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                  :
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                }
              </span>
            </div>

            {isMultipleTransactionsExpanded && data.multipleTransactions.map((txn: any, index: number) => (
              <div
                key={`multi-txn-${index}`}
                className={`p-1.5 mb-1.5 rounded ${isDark ? 'bg-blue-900/30 border-blue-700' : 'bg-blue-50 border-blue-300'} border`}
              >
                {/* Transaction header */}
                <div className={`font-semibold ${isDark ? 'text-blue-400 border-blue-700' : 'text-blue-700 border-blue-200'} border-b pb-0.5 mb-1`}>
                  Money Transfer {index + 1}
                </div>

                {/* Transaction details */}
                <div className="grid grid-cols-2 gap-x-1 gap-y-0.5 text-sm">
                  {/* Amount */}
                  {txn.amount && (
                    <div className="flex justify-between col-span-2">
                      <span className={`${isDark ? 'text-gray-300' : 'text-gray-600'} font-semibold`}>Amount:</span>
                      <span className={`font-semibold ${isDark ? 'text-white' : 'text-gray-900'}`}>₹{txn.amount}</span>
                    </div>
                  )}

                  {/* Transaction ID */}
                  {txn.txn_id && (
                    <div className="flex justify-between col-span-2">
                      <span className={`${isDark ? 'text-gray-300' : 'text-gray-600'} font-semibold`}>Txn ID:</span>
                      <span className={`${isDark ? 'text-white' : 'text-gray-900'} font-medium break-all`} title={txn.txn_id}>
                        {txn.txn_id}
                      </span>
                    </div>
                  )}

                  {/* Receiver Transaction ID - labeled as Txn ID */}
                  {txn.receiver_transaction_id && (
                    <div className="flex justify-between col-span-2">
                      <span className={`${isDark ? 'text-gray-300' : 'text-gray-600'} font-semibold`}>Txn ID:</span>
                      <span className={`${isDark ? 'text-white' : 'text-gray-900'} font-medium break-all`} title={txn.receiver_transaction_id}>
                        {txn.receiver_transaction_id}
                      </span>
                    </div>
                  )}

                  {/* Date */}
                  {txn.date && (
                    <div className="flex justify-between col-span-2">
                      <span className={`${isDark ? 'text-gray-300' : 'text-gray-600'} font-semibold`}>Date:</span>
                      <span className={`${isDark ? 'text-white' : 'text-gray-900'} font-medium`}>{txn.date}</span>
                    </div>
                  )}

                  {/* Reference field (receiver_info) with word wrap */}
                  {txn.receiver_info && (
                    <div className="flex flex-col col-span-2 mt-1">
                      <span className={`${isDark ? 'text-gray-300' : 'text-gray-600'} font-semibold`}>Reference:</span>
                      <div
                        className={`${isDark ? 'text-white bg-gray-800' : 'text-gray-900 bg-gray-100'} font-medium break-words whitespace-normal mt-0.5 p-1.5 rounded text-sm max-h-[60px] overflow-y-auto`}
                      >
                        {txn.receiver_info}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Sub-transactions section - For non-main transaction types */}
        {data.hasSubTransactions && data.subTransactions && data.subTransactions.length > 0 && (
          <div className="mt-2 border-t border-gray-200 pt-2">
            <div
              className={`font-semibold text-base mb-1 ${isDark ? 'text-purple-400' : nodeStyle.text} flex items-center cursor-pointer`}
              onClick={() => setIsSubTransactionsExpanded(!isSubTransactionsExpanded)}
            >
              <span className="flex-grow">
                <span className="mr-1">🔄</span>
                Sub Transactions ({data.subTransactions.length})
              </span>
              <span className="ml-2 text-xs">
                {isSubTransactionsExpanded ?
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                  :
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                }
              </span>
            </div>

            {/* Group sub-transactions by type */}
            {isSubTransactionsExpanded && (() => {
              // Group sub-transactions by type
              const groupedByType: { [key: string]: any[] } = {};

              data.subTransactions.forEach((subTxn: any) => {
                const type = subTxn.type || 'Unknown Type';
                if (!groupedByType[type]) {
                  groupedByType[type] = [];
                }
                groupedByType[type].push(subTxn);
              });

              // Render each type group
              return Object.entries(groupedByType).map(([type, transactions], groupIndex) => (
                <div key={`txn-type-${groupIndex}`} className="mb-2">
                  <div className={`font-semibold text-sm mb-1 ${isDark ? 'text-purple-400 border-purple-700' : 'text-purple-700 border-purple-200'} border-b pb-0.5`}>
                    {type} Group ({transactions.length})
                  </div>

                  {transactions.map((subTxn: any, index: number) => (
                    <div
                      key={`sub-txn-${groupIndex}-${index}`}
                      className={`p-1.5 mb-1.5 rounded ${isDark ? 'bg-purple-900/30 border-purple-700' : 'bg-purple-50 border-purple-300'} border`}
                    >
                      {/* Sub-transaction details */}
                      <div className="grid grid-cols-2 gap-x-1 gap-y-0.5 text-sm">
                        {/* Account */}
                        {subTxn.receiver_account && (
                          <div className="flex justify-between col-span-2">
                            <span className={`${isDark ? 'text-gray-300' : 'text-gray-600'} font-semibold`}>Account:</span>
                            <span className={`${isDark ? 'text-white' : 'text-gray-900'} font-medium break-all`} title={subTxn.receiver_account}>
                              {subTxn.receiver_account}
                            </span>
                          </div>
                        )}

                        {/* Type - Always show for sub-transactions */}
                        {subTxn.type && (
                          <div className="flex justify-between col-span-2">
                            <span className={`${isDark ? 'text-gray-300' : 'text-gray-600'} font-semibold`}>Type:</span>
                            <span className={`${isDark ? 'text-white' : 'text-gray-900'} font-medium`}>{subTxn.type}</span>
                          </div>
                        )}

                        {/* Amount */}
                        {subTxn.amount && (
                          <div className="flex justify-between col-span-2">
                            <span className={`${isDark ? 'text-gray-300' : 'text-gray-600'} font-semibold`}>Amount:</span>
                            <span className={`font-semibold ${isDark ? 'text-white' : 'text-gray-900'}`}>₹{subTxn.amount}</span>
                          </div>
                        )}

                        {/* Transaction ID */}
                        {subTxn.txn_id && (
                          <div className="flex justify-between col-span-2">
                            <span className={`${isDark ? 'text-gray-300' : 'text-gray-600'} font-semibold`}>Txn ID:</span>
                            <span className={`${isDark ? 'text-white' : 'text-gray-900'} font-medium break-all`} title={subTxn.txn_id}>
                              {subTxn.txn_id}
                            </span>
                          </div>
                        )}

                        {/* Receiver Transaction ID - labeled as Txn ID */}
                        {subTxn.receiver_transaction_id && (
                          <div className="flex justify-between col-span-2">
                            <span className={`${isDark ? 'text-gray-300' : 'text-gray-600'} font-semibold`}>Txn ID:</span>
                            <span className={`${isDark ? 'text-white' : 'text-gray-900'} font-medium break-all`} title={subTxn.receiver_transaction_id}>
                              {subTxn.receiver_transaction_id}
                            </span>
                          </div>
                        )}

                        {/* Date */}
                        {subTxn.date && (
                          <div className="flex justify-between col-span-2">
                            <span className={`${isDark ? 'text-gray-300' : 'text-gray-600'} font-semibold`}>Date:</span>
                            <span className={`${isDark ? 'text-white' : 'text-gray-900'} font-medium`}>{subTxn.date}</span>
                          </div>
                        )}

                        {/* Reference field (receiver_info) with word wrap */}
                        {subTxn.receiver_info && (
                          <div className="flex flex-col col-span-2 mt-1">
                            <span className={`${isDark ? 'text-gray-300' : 'text-gray-600'} font-semibold`}>Reference:</span>
                            <div
                              className={`${isDark ? 'text-white bg-gray-800' : 'text-gray-900 bg-gray-100'} font-medium break-words whitespace-normal mt-0.5 p-1.5 rounded text-sm max-h-[60px] overflow-y-auto`}
                            >
                              {subTxn.receiver_info}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ));
            })()}
          </div>
        )}


      </div>
    </div >
  );
};

/**
 * Metadata Node Component
 * Displays complaint metadata with expand/collapse functionality
 */
export const MetadataNode: React.FC<NodeProps> = ({ data }) => {
  // Get the theme context from the current context
  const themeContext = useThemeContext();

  // Use the theme context from the node data if available, otherwise use the current context
  // This ensures the node retains its styling during layout recalculation
  const isDark = data?.themeContext?.isDark !== undefined ? data.themeContext.isDark : themeContext.isDark;

  const theme = {
    primaryColor: isDark ? '#6366f1' : '#3b82f6'
  };
  const [isExpanded, setIsExpanded] = useState(true);

  // Function to toggle expanded/collapsed state
  const toggleExpanded = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsExpanded(!isExpanded);
  };

  // Extract label from data or use default
  const nodeLabel = data?.label || "Complaint Information";

  // Format amount for display
  const formatAmount = (amount: string | number | undefined) => {
    if (!amount) return "0";

    // If it's already a number, format it
    if (typeof amount === 'number') {
      return amount.toLocaleString('en-IN');
    }

    // If it's a string, try to parse it as a number
    const cleanAmount = amount.toString().replace(/[^\d.]/g, '');
    const numAmount = parseFloat(cleanAmount);

    if (!isNaN(numAmount)) {
      return numAmount.toLocaleString('en-IN');
    }

    // If parsing fails, return the original string
    return amount;
  };

  return (
    <div
      className={`p-3 rounded-lg border-2 shadow-md w-[350px] ${isDark ? 'bg-indigo-900/30 border-indigo-700' : 'bg-indigo-50 border-indigo-400'}`}
      style={{ cursor: 'move' }}
    >
      {/* Add source handle with position based on layout direction */}
      <Handle
        type="source"
        position={data.layoutDirection === 'horizontal' ? Position.Right : Position.Bottom}
        style={{ background: theme.primaryColor, width: '10px', height: '10px' }}
      />

      {/* Header with controls */}
      <div className="flex justify-between items-center mb-2">
        <div
          className={`font-medium text-xl pb-1 border-b ${isDark ? 'border-indigo-700 text-indigo-400' : 'border-indigo-300 text-indigo-800'} flex-grow cursor-pointer`}
          onClick={toggleExpanded}
          title={isExpanded ? "Click to collapse" : "Click to expand"}
        >
          {nodeLabel}
          <span className="ml-2 text-xs">
            {isExpanded ?
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
              :
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            }
          </span>
        </div>
      </div>

      {/* Always show category and total amount */}
      <div className="flex flex-col gap-2 text-base">
        {(data?.subcategory || data?.SUBCATEGORY || data?.category || data?.CATEGORY) && (
          <div className="flex justify-between">
            <span className={`${isDark ? 'text-gray-300' : 'text-gray-500'} font-medium`}>Category:</span>
            <span className={`${isDark ? 'text-white' : 'text-gray-800'} break-all`} title={data.subcategory || data.SUBCATEGORY || data.category || data.CATEGORY}>
              {data.subcategory || data.SUBCATEGORY || data.category || data.CATEGORY || "Uncategorized"}
            </span>
          </div>
        )}

        {(data?.total_amount || data?.amount || data?.TOTAL_FRAUD_AMOUNT) && (
          <div className="flex justify-between">
            <span className={`${isDark ? 'text-gray-300' : 'text-gray-500'} font-medium`}>Total Amount:</span>
            <span className={`font-medium ${isDark ? 'text-white' : 'text-gray-800'}`}>
              ₹{formatAmount(data.total_amount || data.amount || data.TOTAL_FRAUD_AMOUNT)}
            </span>
          </div>
        )}

        {/* Additional details - only show if expanded */}
        {isExpanded && (
          <>
            {(data?.date || data?.date_of_complaint || data?.COMPLAINT_DATE) && (
              <div className="flex justify-between">
                <span className={`${isDark ? 'text-gray-300' : 'text-gray-500'} font-medium`}>Date:</span>
                <span className={`${isDark ? 'text-white' : 'text-gray-800'}`}>{data.date || data.date_of_complaint || data.COMPLAINT_DATE}</span>
              </div>
            )}

            {(data?.account || data?.complainant_name || data?.VICTIM_NAME) && (
              <div className="flex justify-between">
                <span className={`${isDark ? 'text-gray-300' : 'text-gray-500'} font-medium`}>Complainant:</span>
                <span className={`${isDark ? 'text-white' : 'text-gray-800'} break-all`} title={data.complainant_name || data.VICTIM_NAME || data.account}>
                  {data.complainant_name || data.VICTIM_NAME || data.account}
                </span>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};
