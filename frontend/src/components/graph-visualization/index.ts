import { TransactionNode, MetadataNode } from './NodeComponents';
import { CustomEdge, edgeTypes } from './EdgeComponents';
import { processTransactionsForGraph } from './GraphProcessor';
import TransactionGraphVisualizer from './TransactionGraphVisualizer';
import GraphExporter from './GraphExporter';
import {
  NodeExpandCollapseProvider,
  useNodeExpandCollapse,
  ExpandCollapseButton
} from './NodeExpandCollapseManager';
import { calculateDynamicLayout } from './DynamicLayoutManager';

// Export node types for ReactFlow
export const nodeTypes = {
  transaction: TransactionNode,
  metadata: MetadataNode
};

// Export all components and utilities
export {
  TransactionNode,
  MetadataNode,
  CustomEdge,
  edgeTypes,
  processTransactionsForGraph,
  TransactionGraphVisualizer,
  GraphExporter,
  NodeExpandCollapseProvider,
  useNodeExpandCollapse,
  ExpandCollapseButton,
  calculateDynamicLayout
};
