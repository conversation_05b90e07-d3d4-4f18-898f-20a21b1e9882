import React, { useState, useEffect, useRef } from 'react';
import { FiX, FiAlertCircle, FiCheckCircle, FiInfo, FiAlertTriangle } from 'react-icons/fi';

// Define props interface
interface AlertMessageProps {
  open: boolean;
  message: React.ReactNode;
  severity?: 'success' | 'info' | 'warning' | 'error';
  duration?: number;
  onClose?: () => void;
}

const TailwindAlert: React.FC<AlertMessageProps> = ({
  open,
  message,
  severity = 'info',
  duration = 6000,
  onClose
}) => {
  const [isOpen, setIsOpen] = useState(open);
  const [progress, setProgress] = useState(100);
  const progressInterval = useRef<number | null>(null);
  const startTime = useRef<number>(0);
  const remainingTime = useRef<number>(duration);

  // Handle alert opening and closing
  useEffect(() => {
    setIsOpen(open);
    if (open) {
      startTime.current = Date.now();
      remainingTime.current = duration;
      setProgress(100);

      if (progressInterval.current) {
        clearInterval(progressInterval.current);
      }

      // Update progress bar every 10ms
      progressInterval.current = window.setInterval(() => {
        const elapsed = Date.now() - startTime.current;
        const newProgress = Math.max(0, 100 - (elapsed / duration) * 100);
        setProgress(newProgress);

        if (newProgress <= 0) {
          handleClose();
        }
      }, 10);
    } else {
      if (progressInterval.current) {
        clearInterval(progressInterval.current);
        progressInterval.current = null;
      }
    }

    return () => {
      if (progressInterval.current) {
        clearInterval(progressInterval.current);
        progressInterval.current = null;
      }
    };
  }, [open, duration]);

  // Handle manual close
  const handleClose = () => {
    setIsOpen(false);
    if (progressInterval.current) {
      clearInterval(progressInterval.current);
      progressInterval.current = null;
    }
    if (onClose) {
      onClose();
    }
  };

  // Handle pause on hover
  const handleMouseEnter = () => {
    if (progressInterval.current) {
      clearInterval(progressInterval.current);
      progressInterval.current = null;
      remainingTime.current = (progress / 100) * duration;
    }
  };

  // Handle resume on mouse leave
  const handleMouseLeave = () => {
    if (isOpen && !progressInterval.current) {
      startTime.current = Date.now();
      const newDuration = remainingTime.current;

      progressInterval.current = window.setInterval(() => {
        const elapsed = Date.now() - startTime.current;
        const newProgress = Math.max(0, 100 - (elapsed / newDuration) * 100);
        setProgress(newProgress);

        if (newProgress <= 0) {
          handleClose();
        }
      }, 10);
    }
  };

  // Get alert styles based on severity
  const getAlertStyles = () => {
    switch (severity) {
      case 'success':
        return {
          customBgColor: 'var(--theme-bg-card)',
          customTextColor: 'var(--theme-accent)',
          customBorderColor: 'var(--theme-accent)',
          customIconColor: 'var(--theme-accent)',
          customProgressColor: 'var(--theme-button)',
          icon: <FiCheckCircle className="w-5 h-5" />,
          useTheme: true
        };
      case 'error':
        return {
          bgColor: 'bg-red-50 dark:bg-red-900/30',
          textColor: 'text-red-800 dark:text-red-300',
          borderColor: 'border-red-200 dark:border-red-800',
          icon: <FiAlertCircle className="w-5 h-5 text-red-500 dark:text-red-400" />,
          progressColor: 'bg-red-500 dark:bg-red-400',
          useTheme: false
        };
      case 'warning':
        return {
          bgColor: 'bg-amber-50 dark:bg-amber-900/30',
          textColor: 'text-amber-800 dark:text-amber-300',
          borderColor: 'border-amber-200 dark:border-amber-800',
          icon: <FiAlertTriangle className="w-5 h-5 text-amber-500 dark:text-amber-400" />,
          progressColor: 'bg-amber-500 dark:bg-amber-400',
          useTheme: false
        };
      case 'info':
      default:
        return {
          customBgColor: 'var(--theme-bg-card)',
          customTextColor: 'var(--theme-text)',
          customBorderColor: 'var(--theme-border)',
          customIconColor: 'var(--theme-accent)',
          customProgressColor: 'var(--theme-accent)',
          icon: <FiInfo className="w-5 h-5" />,
          useTheme: true
        };
    }
  };

  const styles = getAlertStyles();

  if (!isOpen) return null;

  return (
    <div
      className="fixed top-4 right-4 z-50 max-w-md w-full transform transition-all duration-300 ease-in-out animate-slide-down"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div className={`relative rounded-lg shadow-lg border overflow-hidden`} style={{
        backgroundColor: styles.useTheme ? styles.customBgColor : undefined,
        borderColor: styles.useTheme ? styles.customBorderColor : undefined,
        boxShadow: styles.useTheme ? '0 0 10px var(--theme-glow)' : undefined,
        ...(styles.useTheme ? {} : { className: `${styles.borderColor} ${styles.bgColor}` })
      }}>
        <div className="p-4 pr-10">
          <div className="flex items-start">
            <div className="flex-shrink-0" style={{
              color: styles.useTheme ? styles.customIconColor : undefined
            }}>
              {styles.icon}
            </div>
            <div className={`ml-3 ${!styles.useTheme ? styles.textColor : ''}`} style={{
              color: styles.useTheme ? styles.customTextColor : undefined
            }}>
              <div className="text-sm font-medium">
                {message}
              </div>
            </div>
          </div>
          <button
            type="button"
            className={`absolute top-4 right-4 inline-flex rounded-md p-1.5 ${!styles.useTheme ? styles.textColor : ''} hover:bg-gray-100 dark:hover:bg-gray-800 focus:outline-none`}
            style={{
              color: styles.useTheme ? styles.customTextColor : undefined
            }}
            onClick={handleClose}
          >
            <span className="sr-only">Close</span>
            <FiX className="h-4 w-4" />
          </button>
        </div>

        {/* Progress bar */}
        <div className="h-1 w-full bg-gray-200 dark:bg-gray-700">
          <div
            className={`h-full ${!styles.useTheme ? styles.progressColor : ''} transition-all duration-100 ease-linear`}
            style={{
              width: `${progress}%`,
              backgroundColor: styles.useTheme ? styles.customProgressColor : undefined
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default TailwindAlert;
