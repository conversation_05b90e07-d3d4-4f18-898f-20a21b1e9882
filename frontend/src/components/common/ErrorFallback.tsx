import React from 'react';
import logger from '../../utils/logger';

interface ErrorFallbackProps {
  error?: Error;
  resetErrorBoundary?: () => void;
  showDetails?: boolean;
}

/**
 * Error Fallback component to display when an error occurs
 * Can be used with ErrorBoundary or as a standalone component
 */
const ErrorFallback: React.FC<ErrorFallbackProps> = ({
  error,
  resetErrorBoundary,
  showDetails = false
}) => {
  const handleRefresh = () => {
    window.location.reload();
  };

  const handleGoHome = () => {
    logger.log('Navigating to home after error');
    // Use window.location instead of navigate to avoid Router context issues
    window.location.href = '/';
    if (resetErrorBoundary) {
      resetErrorBoundary();
    }
  };

  const handleTryAgain = () => {
    logger.log('Attempting to recover from error');
    if (resetErrorBoundary) {
      resetErrorBoundary();
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-[50vh] p-4">
      <div className="w-full max-w-md p-6 bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
        <div className="flex items-center mb-4">
          <svg
            className="w-8 h-8 text-red-500 mr-3"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
            />
          </svg>
          <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200">
            Something went wrong
          </h2>
        </div>

        <p className="text-gray-700 dark:text-gray-300 mb-4">
          The application encountered an unexpected error. Please try again or refresh the page.
        </p>

        {showDetails && error && (
          <div className="bg-gray-100 dark:bg-gray-700 p-4 rounded-md mb-4 overflow-auto max-h-40">
            <code className="text-sm text-gray-800 dark:text-gray-200">
              {error.toString()}
            </code>
          </div>
        )}

        <div className="flex flex-wrap gap-2 justify-between">
          <button
            onClick={handleTryAgain}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>

          <button
            onClick={handleGoHome}
            className="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
          >
            Go to Dashboard
          </button>

          <button
            onClick={handleRefresh}
            className="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
          >
            Refresh Page
          </button>
        </div>
      </div>
    </div>
  );
};

export default ErrorFallback;
