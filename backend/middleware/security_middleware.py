from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
import os
import logging
import secrets
import time
from typing import Dict

# Setup logging
logger = logging.getLogger(__name__)

# Cache for nonce values with TTL
NONCE_CACHE: Dict[str, float] = {}

def generate_nonce() -> str:
    """Generate a secure nonce value for CSP"""
    return secrets.token_hex(16)

def get_request_nonce(request_id: str) -> str:
    """
    Get or create a nonce for a specific request

    Args:
        request_id: Unique identifier for the request

    Returns:
        str: The nonce value
    """
    # Clean up expired nonces (older than 5 minutes)
    current_time = time.time()
    expired_keys = [k for k, v in NONCE_CACHE.items() if current_time - v > 300]
    for key in expired_keys:
        NONCE_CACHE.pop(key, None)

    # Generate new nonce if needed
    if request_id not in NONCE_CACHE:
        NONCE_CACHE[request_id] = current_time
        request_id = generate_nonce()

    return request_id

class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # Generate a unique request ID and nonce for this request
        request_id = request.headers.get("X-Request-ID", generate_nonce())
        nonce = get_request_nonce(request_id)

        # Store nonce in request state for use in templates
        request.state.csp_nonce = nonce

        # Process the request
        response = await call_next(request)

        # Add enhanced security headers for production with 500 users
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Strict-Transport-Security"] = "max-age=63072000; includeSubDomains; preload"  # 2 years
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["X-Permitted-Cross-Domain-Policies"] = "none"
        response.headers["X-DNS-Prefetch-Control"] = "off"

        # Add security headers for preventing clickjacking and MIME sniffing
        response.headers["X-Download-Options"] = "noopen"  # Prevents IE from executing downloads
        response.headers["X-Content-Security-Policy"] = "default-src 'self'"  # Legacy header for older browsers

        # Add Feature-Policy header (deprecated but still useful for older browsers)
        response.headers["Feature-Policy"] = (
            "camera 'none'; "
            "microphone 'none'; "
            "geolocation 'none'; "
            "payment 'none'"
        )

        # Add Cache-Control for API responses
        if request.url.path.startswith("/api") or request.url.path.startswith("/auth"):
            response.headers["Cache-Control"] = "no-store, max-age=0"
            response.headers["Pragma"] = "no-cache"
            response.headers["Expires"] = "0"

        # Add Content-Security-Policy header with nonce
        # Use a more permissive policy in development for easier debugging
        if os.getenv("ENVIRONMENT", "development") == "development":
            csp = (
                "default-src 'self'; "
                f"script-src 'self' 'nonce-{nonce}' 'unsafe-inline' 'unsafe-eval'; "
                f"style-src 'self' 'nonce-{nonce}' 'unsafe-inline' https://fonts.googleapis.com; "
                "img-src 'self' data: blob:; "
                "font-src 'self' https://fonts.gstatic.com; "
                "connect-src 'self' http://localhost:* http://127.0.0.1:*; "
                "frame-src 'self'; "
                "object-src 'none'; "
                "base-uri 'self'; "
                "form-action 'self'; "
                "frame-ancestors 'self'; "
                "manifest-src 'self'; "
                "worker-src 'self' blob:; "
                "media-src 'self'; "
                "prefetch-src 'self';"
            )

            # Add Content-Security-Policy-Report-Only in development
            # This helps test stricter policies without breaking functionality
            strict_csp = (
                "default-src 'self'; "
                f"script-src 'self' 'nonce-{nonce}'; "
                f"style-src 'self' 'nonce-{nonce}' https://fonts.googleapis.com; "
                "img-src 'self' data: blob:; "
                "font-src 'self' https://fonts.gstatic.com; "
                "connect-src 'self'; "
                "frame-src 'self'; "
                "object-src 'none'; "
                "base-uri 'self'; "
                "form-action 'self'; "
                "frame-ancestors 'none'; "
                "manifest-src 'self'; "
                "worker-src 'self' blob:; "
                "media-src 'self'; "
                "prefetch-src 'self'; "
                "upgrade-insecure-requests; "
                "block-all-mixed-content; "
                "report-uri /security/csp-report; "
                "report-to default;"
            )
            response.headers["Content-Security-Policy-Report-Only"] = strict_csp
        else:
            # Stricter policy for production with 500 users
            csp = (
                "default-src 'self'; "
                f"script-src 'self' 'nonce-{nonce}'; "  # Use nonce instead of unsafe-inline
                f"style-src 'self' 'nonce-{nonce}' https://fonts.googleapis.com; "
                "img-src 'self' data: blob:; "
                "font-src 'self' https://fonts.gstatic.com; "
                "connect-src 'self'; "
                "frame-src 'self'; "
                "object-src 'none'; "
                "base-uri 'self'; "
                "form-action 'self'; "
                "frame-ancestors 'none'; "  # Stricter than 'self' to prevent any framing
                "manifest-src 'self'; "
                "worker-src 'self' blob:; "
                "media-src 'self'; "
                "prefetch-src 'self'; "
                "upgrade-insecure-requests; "  # Force HTTPS in production
                "block-all-mixed-content; "    # Block mixed content
                "require-trusted-types-for 'script'; "  # Require trusted types for scripts (modern browsers)
                "sandbox allow-forms allow-same-origin allow-scripts allow-popups; "  # Apply sandbox with necessary permissions
                "navigate-to 'self';"  # Restrict navigation to same origin (modern browsers)
            )

        response.headers["Content-Security-Policy"] = csp

        # Add comprehensive Permissions-Policy header to limit browser features
        # This is critical for security with 500 users
        response.headers["Permissions-Policy"] = (
            "accelerometer=(), "
            "ambient-light-sensor=(), "
            "autoplay=(), "
            "battery=(), "
            "camera=(), "
            "clipboard-read=(), "
            "clipboard-write=(), "
            "display-capture=(), "
            "document-domain=(), "
            "encrypted-media=(), "
            "fullscreen=(), "
            "gamepad=(), "
            "geolocation=(), "
            "gyroscope=(), "
            "hid=(), "
            "idle-detection=(), "
            "interest-cohort=(), "  # Disable FLoC tracking
            "magnetometer=(), "
            "microphone=(), "
            "midi=(), "
            "payment=(), "
            "picture-in-picture=(), "
            "publickey-credentials-get=(), "
            "screen-wake-lock=(), "
            "serial=(), "
            "sync-xhr=(), "
            "usb=(), "
            "web-share=(), "
            "xr-spatial-tracking=()"
        )

        # Add Cross-Origin headers for API responses
        if request.url.path.startswith("/api"):
            response.headers["Cross-Origin-Resource-Policy"] = "same-origin"
            response.headers["Cross-Origin-Opener-Policy"] = "same-origin"
            response.headers["Cross-Origin-Embedder-Policy"] = "require-corp"

        return response
