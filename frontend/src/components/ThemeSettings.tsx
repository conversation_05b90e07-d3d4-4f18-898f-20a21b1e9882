import React from 'react';
import ThemeColorSelector from './ui/ThemeColorSelector';

interface ThemeSettingsProps {
  className?: string;
}

const ThemeSettings: React.FC<ThemeSettingsProps> = ({ className }) => {
  return (
    <div className={`flex flex-col space-y-4 ${className}`}>
      {/* Theme Color Selector */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <span className="text-base font-medium" style={{ color: 'var(--theme-text)' }}>Theme Color</span>
        </div>
        <ThemeColorSelector size="md" showLabels={false} />
      </div>
    </div>
  );
};

export default ThemeSettings;
