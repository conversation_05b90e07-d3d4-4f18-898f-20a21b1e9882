import React, { createContext, useState, useContext, useEffect, ReactNode } from 'react';
import authService from '../services/authService';
import { User } from '../types/auth';
import { useAlert } from './TailwindAlertContext';
import logger from '../utils/logger';

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  loading: boolean;
  logout: () => Promise<void>;
  refreshUserData: () => Promise<User | null>;
  refreshToken: () => Promise<void>;
  resolveSessionConflict: (action: 'TAKE_OVER' | 'LOGOUT') => Promise<void>;
  sessionId: string | null;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const { showError } = useAlert();

  // Check authentication status with retry mechanism
  const checkAuthStatus = async (retryCount = 0): Promise<boolean> => {
    try {
      const isAuth = await authService.isAuthenticated();
      setIsAuthenticated(isAuth);

      if (!isAuth) {
        setUser(null);
      }

      return isAuth;
    } catch (error) {
      logger.error('Error checking auth status:', error);

      // Retry up to 3 times with exponential backoff
      if (retryCount < 3) {
        const delay = Math.pow(2, retryCount) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
        return checkAuthStatus(retryCount + 1);
      }

      setIsAuthenticated(false);
      setUser(null);
      return false;
    }
  };

  // Refresh user data with error handling
  const refreshUserData = async (): Promise<User | null> => {
    try {
      const userData = await authService.getUserData();
      if (userData) {
        logger.debug('User data received:', userData);
        setUser(userData);
        setIsAuthenticated(true);

        // Update session ID if available
        if (userData.session_id) {
          setSessionId(userData.session_id);
          // Don't store session ID in localStorage for security reasons
          // Instead, rely on the HttpOnly cookie set by the backend
        } else {
          // Try to get session ID from cookies only (more secure)
          const storedSessionId = document.cookie.split('; ')
                                  .find(row => row.startsWith('session_id='))
                                  ?.split('=')[1];

          if (storedSessionId) {
            setSessionId(storedSessionId);
          }
        }
      } else {
        logger.warn('No user data received');
        setUser(null);
        setIsAuthenticated(false);
        setSessionId(null);
      }
      return userData;
    } catch (error) {
      logger.error('Error refreshing user data:', error);
      setUser(null);
      setIsAuthenticated(false);
      setSessionId(null);
      showError('Failed to refresh user data. Please try logging in again.');
      return null;
    }
  };

  // Enhanced token refresh mechanism
  const refreshToken = async (): Promise<void> => {
    try {
      await authService.refreshToken();
      await refreshUserData();
    } catch (error: any) {
      logger.error('Error refreshing token:', error);

      // Check if this is a session conflict error
      if (error.response?.status === 409 && error.response?.data?.code === 'SESSION_CONFLICT') {
        // Handle session conflict
        logger.warn('Session conflict detected during token refresh');
        showError('Another session is active. Please choose to take over or logout.');
        // Don't logout automatically, let the user decide
      } else {
        // If token refresh fails for other reasons, log out the user
        await logout();
      }
    }
  };

  // Enhanced logout function
  const logout = async () => {
    try {
      await authService.logout();
      setUser(null);
      setIsAuthenticated(false);
      setSessionId(null);

      // Clear only auth-related items from localStorage for better UX
      // This preserves user preferences but removes sensitive data
      localStorage.removeItem('session_id');
      localStorage.removeItem('user');
      localStorage.removeItem('csrf_token');

      // Clear session storage completely
      sessionStorage.clear();

      // Redirect to login page
      window.location.href = '/login';
    } catch (error) {
      logger.error('Error during logout:', error);
      showError('Error during logout. Please try again.');

      // Force logout even if API call fails
      setUser(null);
      setIsAuthenticated(false);
      setSessionId(null);
      window.location.href = '/login';
    }
  };

  // Function to resolve session conflicts
  const resolveSessionConflict = async (action: 'TAKE_OVER' | 'LOGOUT'): Promise<void> => {
    try {
      const response = await authService.api.post('/auth/resolve-session-conflict', { action });

      if (action === 'TAKE_OVER') {
        // Refresh user data after taking over the session
        await refreshUserData();
        logger.info('Successfully took over the session');
      } else {
        // Logout if the user chose to logout
        setUser(null);
        setIsAuthenticated(false);
        setSessionId(null);
        localStorage.clear();
        sessionStorage.clear();
        logger.info('Logged out due to session conflict');
      }

      return response.data;
    } catch (error) {
      logger.error('Error resolving session conflict:', error);
      showError('Failed to resolve session conflict. Please try again.');
      throw error;
    }
  };

  // Set up token refresh interval
  useEffect(() => {
    const refreshInterval = setInterval(async () => {
      if (isAuthenticated) {
        await refreshToken();
      }
    }, 24 * 60 * 60 * 1000); // Refresh every 24 hours

    return () => clearInterval(refreshInterval);
  }, [isAuthenticated]);

  // Initial auth check with error handling
  useEffect(() => {
    const initAuth = async () => {
      try {
        const isAuth = await checkAuthStatus();
        if (isAuth) {
          await refreshUserData();
        }
      } catch (error) {
        logger.error('Error during initial auth check:', error);
        showError('Authentication error. Please try logging in again.');
      } finally {
        setLoading(false);
      }
    };

    initAuth();
  }, []);

  return (
    <AuthContext.Provider
      value={{
        user,
        isAuthenticated,
        loading,
        logout,
        refreshUserData,
        refreshToken,
        resolveSessionConflict,
        sessionId
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
