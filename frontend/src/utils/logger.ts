/**
 * Enhanced logger utility for Cyber Sakha
 *
 * Features:
 * - Environment-aware logging (suppresses logs in production)
 * - Support for remote logging in production
 * - Consistent formatting with timestamps and log levels
 * - Performance tracking capabilities
 */

// Environment configuration
const IS_PRODUCTION = import.meta.env.VITE_ENV === 'production';
const DEBUG_ENABLED = import.meta.env.VITE_DEBUG_LOGS === 'true';
const APP_VERSION = import.meta.env.VITE_APP_VERSION || '1.0.0';
const REMOTE_LOGGING = import.meta.env.VITE_REMOTE_LOGGING === 'true';
const REMOTE_LOG_ENDPOINT = import.meta.env.VITE_REMOTE_LOG_ENDPOINT || '/api/logs';

// Performance tracking
const performanceMarks: Record<string, number> = {};

// Sanitize sensitive data from logs
const sanitizeData = (data: any): any => {
  if (data === null || data === undefined) {
    return data;
  }

  // Handle strings that might contain tokens
  if (typeof data === 'string') {
    // Check if the string looks like a JWT token
    if (/^eyJ[a-zA-Z0-9_-]+\.[a-zA-Z0-9_-]+\.[a-zA-Z0-9_-]+$/.test(data)) {
      return '[REDACTED JWT TOKEN]';
    }
    return data;
  }

  // Handle objects recursively
  if (typeof data === 'object') {
    if (Array.isArray(data)) {
      return data.map(item => sanitizeData(item));
    }

    const sanitized = { ...data };

    // Redact sensitive fields
    const sensitiveFields = [
      'password', 'token', 'access_token', 'refresh_token', 'accessToken',
      'refreshToken', 'jwt', 'secret', 'key', 'authorization', 'auth',
      'credential', 'pin', 'otp'
    ];

    for (const key in sanitized) {
      // Check if the key is sensitive
      if (sensitiveFields.some(field => key.toLowerCase().includes(field))) {
        sanitized[key] = '[REDACTED]';
      } else if (typeof sanitized[key] === 'object' && sanitized[key] !== null) {
        // Recursively sanitize nested objects
        sanitized[key] = sanitizeData(sanitized[key]);
      }
    }

    return sanitized;
  }

  return data;
};

// Format log message with timestamp and level
const formatLogMessage = (level: string, args: any[]): any[] => {
  const timestamp = new Date().toISOString();
  const prefix = `[${timestamp}] [${level}]`;

  // Sanitize all arguments to remove sensitive data
  const sanitizedArgs = args.map(arg => sanitizeData(arg));

  // If first arg is a string, prepend our prefix
  if (typeof sanitizedArgs[0] === 'string') {
    return [`${prefix} ${sanitizedArgs[0]}`, ...sanitizedArgs.slice(1)];
  }

  // Otherwise add prefix as separate arg
  return [prefix, ...sanitizedArgs];
};

// Send logs to remote endpoint in production
const sendRemoteLog = (level: string, args: any[]): void => {
  if (!REMOTE_LOGGING || !IS_PRODUCTION) return;

  try {
    const logData = {
      level,
      timestamp: new Date().toISOString(),
      message: args.map(arg =>
        typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
      ).join(' '),
      userAgent: navigator.userAgent,
      appVersion: APP_VERSION,
      url: window.location.href,
    };

    // Use beacon API for non-blocking logging
    navigator.sendBeacon(REMOTE_LOG_ENDPOINT, JSON.stringify(logData));
  } catch (e) {
    // Silently fail if remote logging fails
    // We don't want logging to break the app
  }
};

/**
 * Enhanced logger with environment awareness and remote logging capability
 */
const logger = {
  /**
   * Log informational message (suppressed in production unless explicitly enabled)
   */
  log: (...args: any[]): void => {
    if (DEBUG_ENABLED) {
      console.log(...formatLogMessage('INFO', args));
    }
    sendRemoteLog('INFO', args);
  },

  /**
   * Log warning message (always shown)
   */
  warn: (...args: any[]): void => {
    console.warn(...formatLogMessage('WARN', args));
    sendRemoteLog('WARN', args);
  },

  /**
   * Log error message (always logged)
   */
  error: (...args: any[]): void => {
    // Always log errors, even in production
    console.error(...formatLogMessage('ERROR', args));
    sendRemoteLog('ERROR', args);
  },

  /**
   * Log debug message (suppressed in production and development unless explicitly enabled)
   */
  debug: (...args: any[]): void => {
    // Only log debug messages if explicitly enabled via DEBUG_ENABLED flag
    if (DEBUG_ENABLED) {
      console.debug(...formatLogMessage('DEBUG', args));
    }
    // Don't send debug logs to remote endpoint
  },

  /**
   * Log info message (suppressed unless explicitly enabled)
   */
  info: (...args: any[]): void => {
    if (DEBUG_ENABLED) {
      console.info(...formatLogMessage('INFO', args));
    }
    sendRemoteLog('INFO', args);
  },

  /**
   * Force log even in production
   */
  forceLog: (...args: any[]): void => {
    console.log(...formatLogMessage('FORCE', args));
    sendRemoteLog('FORCE', args);
  },

  /**
   * Start performance tracking for a named operation
   */
  startPerformanceTracking: (name: string): void => {
    performanceMarks[name] = performance.now();
  },

  /**
   * End performance tracking and log the duration
   */
  endPerformanceTracking: (name: string, logLevel: 'debug' | 'info' = 'debug'): number | null => {
    if (!performanceMarks[name]) {
      logger.warn(`No performance mark found for: ${name}`);
      return null;
    }

    const duration = performance.now() - performanceMarks[name];
    delete performanceMarks[name];

    if (logLevel === 'debug') {
      logger.debug(`Performance: ${name} took ${duration.toFixed(2)}ms`);
    } else {
      logger.info(`Performance: ${name} took ${duration.toFixed(2)}ms`);
    }

    return duration;
  },

  /**
   * Group related logs (only in development)
   */
  group: (name: string, collapsed: boolean = false): void => {
    if (!IS_PRODUCTION || DEBUG_ENABLED) {
      collapsed ? console.groupCollapsed(name) : console.group(name);
    }
  },

  /**
   * End a log group (only in development)
   */
  groupEnd: (): void => {
    if (!IS_PRODUCTION || DEBUG_ENABLED) {
      console.groupEnd();
    }
  }
};

export default logger;
