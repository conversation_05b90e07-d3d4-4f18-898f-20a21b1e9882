from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
import time
import logging
import json
from datetime import datetime, timezone

logger = logging.getLogger(__name__)

class RequestLoggingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        start_time = time.time()
        request_id = f"{int(time.time() * 1000)}-{id(request)}"
        timestamp = datetime.now(timezone.utc).isoformat()

        # Extract request information
        client_ip = request.client.host if request.client else "unknown"
        user_agent = request.headers.get("user-agent", "unknown")
        referer = request.headers.get("referer", "none")

        # Get authenticated user if available
        user_id = "anonymous"
        try:
            # Check for token in cookies
            has_cookie_token = "access_token" in request.cookies

            # Check for Authorization header
            auth_header = request.headers.get("authorization")
            has_auth_header = auth_header and auth_header.startswith("Bearer ")

            # If either token source is available, user is authenticated
            if has_cookie_token or has_auth_header:
                # Just log that there is a token, don't log the actual token
                user_id = "authenticated"
        except Exception:
            pass

        # Process the request
        try:
            response = await call_next(request)

            # Calculate processing time
            process_time = time.time() - start_time

            # Prepare log data
            log_data = {
                "request_id": request_id,
                "timestamp": timestamp,
                "client_ip": client_ip,
                "user_id": user_id,
                "method": request.method,
                "path": request.url.path,
                "query_params": str(request.query_params),
                "status_code": response.status_code,
                "processing_time": f"{process_time:.4f}s",
                "user_agent": user_agent,
                "referer": referer
            }

            # Log as structured data
            if 200 <= response.status_code < 400:
                logger.info(f"Request completed: {json.dumps(log_data)}")
            elif 400 <= response.status_code < 500:
                logger.warning(f"Client error: {json.dumps(log_data)}")
            else:
                logger.error(f"Server error: {json.dumps(log_data)}")

        except Exception as e:
            # Log any exceptions
            process_time = time.time() - start_time

            error_data = {
                "request_id": request_id,
                "timestamp": timestamp,
                "client_ip": client_ip,
                "user_id": user_id,
                "method": request.method,
                "path": request.url.path,
                "error": str(e),
                "processing_time": f"{process_time:.4f}s",
                "user_agent": user_agent,
                "referer": referer
            }

            logger.error(f"Request failed: {json.dumps(error_data)}")
            raise  # Re-raise the exception

        # Add request ID to response headers for debugging
        response.headers["X-Request-ID"] = request_id

        return response
