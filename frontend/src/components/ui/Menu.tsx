import React, { useState, useRef, useEffect } from 'react';
import { twMerge } from 'tailwind-merge';

interface MenuProps {
  children: React.ReactNode;
  anchorEl: HTMLElement | null;
  open: boolean;
  onClose: () => void;
  className?: string;
  placement?: 'top' | 'bottom' | 'left' | 'right' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
}

const Menu: React.FC<MenuProps> = ({
  children,
  anchorEl,
  open,
  onClose,
  className,
  placement = 'bottom-left',
  ...props
}) => {
  const menuRef = useRef<HTMLDivElement>(null);
  const [position, setPosition] = useState({ top: 0, left: 0 });
  
  // Calculate position based on anchorEl and placement
  useEffect(() => {
    if (anchorEl && open && menuRef.current) {
      const anchorRect = anchorEl.getBoundingClientRect();
      const menuRect = menuRef.current.getBoundingClientRect();
      
      let top = 0;
      let left = 0;
      
      switch (placement) {
        case 'top':
          top = anchorRect.top - menuRect.height;
          left = anchorRect.left + (anchorRect.width / 2) - (menuRect.width / 2);
          break;
        case 'bottom':
          top = anchorRect.bottom;
          left = anchorRect.left + (anchorRect.width / 2) - (menuRect.width / 2);
          break;
        case 'left':
          top = anchorRect.top + (anchorRect.height / 2) - (menuRect.height / 2);
          left = anchorRect.left - menuRect.width;
          break;
        case 'right':
          top = anchorRect.top + (anchorRect.height / 2) - (menuRect.height / 2);
          left = anchorRect.right;
          break;
        case 'top-left':
          top = anchorRect.top - menuRect.height;
          left = anchorRect.left;
          break;
        case 'top-right':
          top = anchorRect.top - menuRect.height;
          left = anchorRect.right - menuRect.width;
          break;
        case 'bottom-left':
          top = anchorRect.bottom;
          left = anchorRect.left;
          break;
        case 'bottom-right':
          top = anchorRect.bottom;
          left = anchorRect.right - menuRect.width;
          break;
        default:
          top = anchorRect.bottom;
          left = anchorRect.left;
      }
      
      // Adjust position to keep menu within viewport
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;
      
      if (left < 0) left = 0;
      if (top < 0) top = 0;
      if (left + menuRect.width > viewportWidth) left = viewportWidth - menuRect.width;
      if (top + menuRect.height > viewportHeight) top = viewportHeight - menuRect.height;
      
      setPosition({ top, left });
    }
  }, [anchorEl, open, placement]);
  
  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        menuRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        anchorEl !== event.target &&
        !anchorEl?.contains(event.target as Node)
      ) {
        onClose();
      }
    };
    
    if (open) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [anchorEl, onClose, open]);
  
  // Handle escape key press
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };
    
    if (open) {
      document.addEventListener('keydown', handleEscape);
    }
    
    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [onClose, open]);
  
  if (!open) return null;
  
  return (
    <div
      ref={menuRef}
      className={twMerge(
        'absolute z-50 min-w-[200px] py-1 bg-white dark:bg-gray-800 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none',
        className
      )}
      style={{
        top: `${position.top}px`,
        left: `${position.left}px`,
      }}
      {...props}
    >
      {children}
    </div>
  );
};

interface MenuItemProps {
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  className?: string;
  icon?: React.ReactNode;
}

export const MenuItem: React.FC<MenuItemProps> = ({
  children,
  onClick,
  disabled = false,
  className,
  icon,
  ...props
}) => {
  const baseStyles = 'flex items-center w-full px-4 py-2 text-sm text-left';
  const enabledStyles = !disabled
    ? 'text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer'
    : 'text-gray-400 dark:text-gray-500 cursor-not-allowed';
  
  return (
    <button
      className={twMerge(baseStyles, enabledStyles, className)}
      onClick={disabled ? undefined : onClick}
      disabled={disabled}
      {...props}
    >
      {icon && <span className="mr-2">{icon}</span>}
      {children}
    </button>
  );
};

interface MenuDividerProps {
  className?: string;
}

export const MenuDivider: React.FC<MenuDividerProps> = ({ className, ...props }) => {
  return (
    <div
      className={twMerge('my-1 h-px bg-gray-200 dark:bg-gray-700', className)}
      {...props}
    />
  );
};

export default Object.assign(Menu, {
  Item: MenuItem,
  Divider: MenuDivider,
});
